import path from 'path';
import rspack from '@rspack/core';
import { defineConfig, loadEnv } from '@rsbuild/core';
import { pluginReact } from '@rsbuild/plugin-react';

const { publicVars, rawPublicVars } = loadEnv({ prefixes: ['REACT_APP_'] });
const { getBaseUrl } = require('./src/utils/getEnv');

const proxyUrl = 'http://wolf.dev.datatist.cn/';

module.exports = defineConfig({
  source: {
    define: {
      ...publicVars,
      'process.env': JSON.stringify(rawPublicVars)
    },
    tsconfigPath: './tsconfig.json'
  },
  dev: {
    watchFiles: {
      paths: ['./node_modules/wolf-static-cpnt/dist/index.js', './node_modules/wolf-static-cpnt/dist/index.css'],
      type: 'reload-page'
    }
  },
  tools: {
    rspack: {
      module: {
        rules: [
          {
            test: /\.css$/,
            use: ['postcss-loader']
          }
        ]
      },
      plugins: [
        new rspack.CssExtractRspackPlugin({}),
        new rspack.ProvidePlugin({
          stream: require.resolve('stream-browserify'),
          buffer: require.resolve('buffer'),
          process: require.resolve('process/browser'),
          React: 'react',
          _: 'lodash',
          dayjs: 'dayjs'
        })
      ],
      resolve: {
        modules: [path.resolve(__dirname, '../node_modules'), 'node_modules'],
        alias: {
          context: path.resolve(__dirname, 'src/context'),
          pages: path.resolve(__dirname, 'src/pages'),
          assets: path.resolve(__dirname, 'src/assets'),
          components: path.resolve(__dirname, 'src/components'),
          service: path.resolve(__dirname, 'src/service'),
          utils: path.resolve(__dirname, 'src/utils'),
          store: path.resolve(__dirname, 'src/store'),
          '@': path.resolve(__dirname, 'src'),
          react: path.resolve(__dirname, 'node_modules/react')
        },
        extensions: ['.js', '.jsx', '.ts', '.tsx']
      },
      // 排除antd 列表刷新警告， sass冲突升级警告，和Deprecation Warning语义化警告
      ignoreWarnings: [/Conflicting order/, /Failed to parse source map/, /Deprecation Warning/]
    }
  },
  plugins: [pluginReact()],
  server: {
    host: '0.0.0.0',
    port: 8081,
    open: true,
    base: getBaseUrl(),
    proxy: {
      '/api/': {
        context: ['/analyzer/**/*.do', '/usercenter/**/*.do'],
        target: proxyUrl,
        changeOrigin: true,
        router: () => {
          return proxyUrl;
        }
      }
    }
  }
});
