lockfileVersion: 5.4

specifiers:
  '@rsbuild/core': ^1.1.4
  '@rsbuild/plugin-react': ^1.0.7
  '@types/react': ^18.3.12
  '@types/react-dom': ^18.3.1
  react: ^18.3.1
  react-dom: ^18.3.1
  typescript: ^5.7.2
  wolf-static-cpnt: link:../

dependencies:
  react: 18.3.1
  react-dom: 18.3.1_react@18.3.1

devDependencies:
  '@rsbuild/core': 1.1.8
  '@rsbuild/plugin-react': 1.0.7_@rsbuild+core@1.1.8
  '@types/react': 18.3.12
  '@types/react-dom': 18.3.1
  typescript: 5.7.2
  wolf-static-cpnt: link:..

packages:

  /@module-federation/runtime-tools/0.5.1:
    resolution: {integrity: sha512-nfBedkoZ3/SWyO0hnmaxuz0R0iGPSikHZOAZ0N/dVSQaIzlffUo35B5nlC2wgWIc0JdMZfkwkjZRrnuuDIJbzg==}
    dependencies:
      '@module-federation/runtime': 0.5.1
      '@module-federation/webpack-bundler-runtime': 0.5.1
    dev: true

  /@module-federation/runtime/0.5.1:
    resolution: {integrity: sha512-xgiMUWwGLWDrvZc9JibuEbXIbhXg6z2oUkemogSvQ4LKvrl/n0kbqP1Blk669mXzyWbqtSp6PpvNdwaE1aN5xQ==}
    dependencies:
      '@module-federation/sdk': 0.5.1
    dev: true

  /@module-federation/sdk/0.5.1:
    resolution: {integrity: sha512-exvchtjNURJJkpqjQ3/opdbfeT2wPKvrbnGnyRkrwW5o3FH1LaST1tkiNviT6OXTexGaVc2DahbdniQHVtQ7pA==}
    dev: true

  /@module-federation/webpack-bundler-runtime/0.5.1:
    resolution: {integrity: sha512-mMhRFH0k2VjwHt3Jol9JkUsmI/4XlrAoBG3E0o7HoyoPYv1UFOWyqAflfANcUPgbYpvqmyLzDcO+3IT36LXnrA==}
    dependencies:
      '@module-federation/runtime': 0.5.1
      '@module-federation/sdk': 0.5.1
    dev: true

  /@rsbuild/core/1.1.8:
    resolution: {integrity: sha512-UhP260og3aJcqGWpnRcQXLVapdOZZ09JXaQKY+tE55A7nBw8DQy+qrtTsFZYvVKas1bq8GzhGfLxuglCst4Lnw==}
    engines: {node: '>=16.7.0'}
    hasBin: true
    dependencies:
      '@rspack/core': 1.1.5_@swc+helpers@0.5.15
      '@rspack/lite-tapable': 1.0.1
      '@swc/helpers': 0.5.15
      core-js: 3.39.0
    dev: true

  /@rsbuild/plugin-react/1.0.7_@rsbuild+core@1.1.8:
    resolution: {integrity: sha512-t7T/GqDwodusTAnxGpqVRnQ/G+HYh98zk71qIg19WkjVJJGv57AC1Ppx0/6zzbZAbxU60bfK8TeEEXjhXCdSxA==}
    peerDependencies:
      '@rsbuild/core': 1.x
    dependencies:
      '@rsbuild/core': 1.1.8
      '@rspack/plugin-react-refresh': 1.0.0_react-refresh@0.14.2
      react-refresh: 0.14.2
    dev: true

  /@rspack/binding-darwin-arm64/1.1.5:
    resolution: {integrity: sha512-eEynmyPPl+OGYQ9LRFwiQosyRfcca3OQB73akqY4mqDRl39OyiBjq7347DLHJysgbm9z+B1bsiLuh2xc6mdclQ==}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-darwin-x64/1.1.5:
    resolution: {integrity: sha512-I6HPRgogewU5v1OKe3noEzq2U1FCEYAbW+smy+lPvpTW+3X6PlVMzTT4oelhB0EXDQ+KxjXH9KpOKON1hg/JGg==}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-linux-arm64-gnu/1.1.5:
    resolution: {integrity: sha512-LQnqucNa6Dr6y3By+/M2ARO4jDR3AM+PuCsHgzlYT0RDRLS+Ow3f50WbNBf7eI/DhrEA0aucYL3sz1ljguB3EA==}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-linux-arm64-musl/1.1.5:
    resolution: {integrity: sha512-b9L/9HJxrWY4cezPWqgj28I9Xe2XxwLHu8x0CMGobwF2XKR0QQVLAst38RW/EusJ8TURdyvNEOuRZlWEIJuYOw==}
    cpu: [arm64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-linux-x64-gnu/1.1.5:
    resolution: {integrity: sha512-0az52ZXTg/ErCGC1v/oFLWByKAiXvng4euv+prwMWF6p1pA7lfLRLzdibDFO4KgC16Zlfcg3hqs7YikLng4x+w==}
    cpu: [x64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-linux-x64-musl/1.1.5:
    resolution: {integrity: sha512-EF/LJTtCTkuti2gJnCyvXHC5Q2L5M4+RXm5kj9Bfu/t0Zmmfe6Jd5QUsifgogioeL0ZsH/Pou5QiiVcOFcqFKQ==}
    cpu: [x64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-win32-arm64-msvc/1.1.5:
    resolution: {integrity: sha512-VEqhK6HwIHby6gtOkxIx66SkqYndiaP1ddZ3X39RLE40TY3KlNgfG/SzbN9J5Qb+8jjq3ogV8n50+wLEGkhiWw==}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-win32-ia32-msvc/1.1.5:
    resolution: {integrity: sha512-Yi2BwYehc5/sRVgI7zTGYJKjnV8UszAJt/stWdFHaq82chHiuuF/tQd1WcBUq0Iin9ylBMo16mRJAuFkFmJ74Q==}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-win32-x64-msvc/1.1.5:
    resolution: {integrity: sha512-4UArXYqJO1Ni7TmCw1T11JnrwfpoThDdiQ9k1P1voBWK3bDahPEBOptk9ZPu2+ZuRX8hFrvumRKkLY3oy7fTMw==}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding/1.1.5:
    resolution: {integrity: sha512-RsSkgi56Q5XUXut0qweLSE1C4Ogcm7g/ueKoOgsbHAYVKrCs9/dTFlPHWSIAaI7QWh0GWEePR/MM2O2HIu+1rw==}
    optionalDependencies:
      '@rspack/binding-darwin-arm64': 1.1.5
      '@rspack/binding-darwin-x64': 1.1.5
      '@rspack/binding-linux-arm64-gnu': 1.1.5
      '@rspack/binding-linux-arm64-musl': 1.1.5
      '@rspack/binding-linux-x64-gnu': 1.1.5
      '@rspack/binding-linux-x64-musl': 1.1.5
      '@rspack/binding-win32-arm64-msvc': 1.1.5
      '@rspack/binding-win32-ia32-msvc': 1.1.5
      '@rspack/binding-win32-x64-msvc': 1.1.5
    dev: true

  /@rspack/core/1.1.5_@swc+helpers@0.5.15:
    resolution: {integrity: sha512-/FmxDeMuW8fJkhz8fHuCu7OiJHFKW78xclEu7LkEujWl4PqJgdWjUL/6FWIj50spRwj6PRfuc31hFSL4hbNfCA==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@swc/helpers': '>=0.5.1'
    peerDependenciesMeta:
      '@swc/helpers':
        optional: true
    dependencies:
      '@module-federation/runtime-tools': 0.5.1
      '@rspack/binding': 1.1.5
      '@rspack/lite-tapable': 1.0.1
      '@swc/helpers': 0.5.15
      caniuse-lite: 1.0.30001686
    dev: true

  /@rspack/lite-tapable/1.0.1:
    resolution: {integrity: sha512-VynGOEsVw2s8TAlLf/uESfrgfrq2+rcXB1muPJYBWbsm1Oa6r5qVQhjA5ggM6z/coYPrsVMgovl3Ff7Q7OCp1w==}
    engines: {node: '>=16.0.0'}
    dev: true

  /@rspack/plugin-react-refresh/1.0.0_react-refresh@0.14.2:
    resolution: {integrity: sha512-WvXkLewW5G0Mlo5H1b251yDh5FFiH4NDAbYlFpvFjcuXX2AchZRf9zdw57BDE/ADyWsJgA8kixN/zZWBTN3iYA==}
    peerDependencies:
      react-refresh: '>=0.10.0 <1.0.0'
    peerDependenciesMeta:
      react-refresh:
        optional: true
    dependencies:
      error-stack-parser: 2.1.4
      html-entities: 2.5.2
      react-refresh: 0.14.2
    dev: true

  /@swc/helpers/0.5.15:
    resolution: {integrity: sha512-JQ5TuMi45Owi4/BIMAJBoSQoOJu12oOk/gADqlcUL9JEdHB8vyjUSsxqeNXnmXHjYKMi2WcYtezGEEhqUI/E2g==}
    dependencies:
      tslib: 2.8.1
    dev: true

  /@types/prop-types/15.7.13:
    resolution: {integrity: sha512-hCZTSvwbzWGvhqxp/RqVqwU999pBf2vp7hzIjiYOsl8wqOmUxkQ6ddw1cV3l8811+kdUFus/q4d1Y3E3SyEifA==}
    dev: true

  /@types/react-dom/18.3.1:
    resolution: {integrity: sha512-qW1Mfv8taImTthu4KoXgDfLuk4bydU6Q/TkADnDWWHwi4NX4BR+LWfTp2sVmTqRrsHvyDDTelgelxJ+SsejKKQ==}
    dependencies:
      '@types/react': 18.3.12
    dev: true

  /@types/react/18.3.12:
    resolution: {integrity: sha512-D2wOSq/d6Agt28q7rSI3jhU7G6aiuzljDGZ2hTZHIkrTLUI+AF3WMeKkEZ9nN2fkBAlcktT6vcZjDFiIhMYEQw==}
    dependencies:
      '@types/prop-types': 15.7.13
      csstype: 3.1.3
    dev: true

  /caniuse-lite/1.0.30001686:
    resolution: {integrity: sha512-Y7deg0Aergpa24M3qLC5xjNklnKnhsmSyR/V89dLZ1n0ucJIFNs7PgR2Yfa/Zf6W79SbBicgtGxZr2juHkEUIA==}
    dev: true

  /core-js/3.39.0:
    resolution: {integrity: sha512-raM0ew0/jJUqkJ0E6e8UDtl+y/7ktFivgWvqw8dNSQeNWoSDLvQ1H/RN3aPXB9tBd4/FhyR4RDPGhsNIMsAn7g==}
    requiresBuild: true
    dev: true

  /csstype/3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}
    dev: true

  /error-stack-parser/2.1.4:
    resolution: {integrity: sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==}
    dependencies:
      stackframe: 1.3.4
    dev: true

  /html-entities/2.5.2:
    resolution: {integrity: sha512-K//PSRMQk4FZ78Kyau+mZurHn3FH0Vwr+H36eE0rPbeYkRRi9YxceYPhuN60UwWorxyKHhqoAJl2OFKa4BVtaA==}
    dev: true

  /js-tokens/4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}
    dev: false

  /loose-envify/1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true
    dependencies:
      js-tokens: 4.0.0
    dev: false

  /react-dom/18.3.1_react@18.3.1:
    resolution: {integrity: sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==}
    peerDependencies:
      react: ^18.3.1
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2
    dev: false

  /react-refresh/0.14.2:
    resolution: {integrity: sha512-jCvmsr+1IUSMUyzOkRcvnVbX3ZYC6g9TDrDbFuFmRDq7PD4yaGbLKNQL6k2jnArV8hjYxh7hVhAZB6s9HDGpZA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /react/18.3.1:
    resolution: {integrity: sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /scheduler/0.23.2:
    resolution: {integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /stackframe/1.3.4:
    resolution: {integrity: sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==}
    dev: true

  /tslib/2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}
    dev: true

  /typescript/5.7.2:
    resolution: {integrity: sha512-i5t66RHxDvVN40HfDd1PsEThGNnlMCMT3jMUuoh9/0TaqWevNontacunWyN02LA9/fIbEWlcHZcgTKb9QoaLfg==}
    engines: {node: '>=14.17'}
    hasBin: true
    dev: true
