import React, { useState, useEffect } from 'react';
import { Input, Button } from 'antd';
// import { FlowEditor } from 'wolf-static-cpnt';
import { FlowEditor, FlowCanvas } from 'wolf-static-cpnt';
import beautify from 'json-beautify';
// import _ from 'lodash';
// import debounce from 'lodash/debounce';
import TestFlowNodeBoxData from './TestFlowNodeBoxData';
import { flowConfig } from './config';

const { boxData, canvasData } = TestFlowNodeBoxData;
const { TextArea } = Input;

export default function TestFlowEditor() {
  const [jsonBoxData, setJsonBoxData] = useState(beautify(boxData, null, 2, 100));
  const [jsonCanvasData, setJsonCanvasData] = useState(beautify(flowConfig, null, 2, 100));

  const [inputBoxData, setInputBoxData] = useState(boxData);
  const [inputCanvasData, setInputCanvasData] = useState(flowConfig);
  const [state] = useState({
    onChange: v => setInputCanvasData(v)
  });

  const [dataProvider, setDataProvider] = useState({
    getFlowBoxNodes: () => {
      return inputBoxData;
    },
    getAtTimeNodesData: () => {
      console.log('getAtTimeNodesData1');
      return { 8: 1608715337439 }
    }
  });

  useEffect(() => {
    setDataProvider({
      getFlowBoxNodes: () => {
        return inputBoxData;
      }
    });
  }, [inputBoxData]);

  const onSubmitBoxData = () => {
    try {
      const v = JSON.parse(jsonBoxData);
      setInputBoxData(v);
    }
    catch (err) {
      console.log(err);
    }
  };

  const onSubmitJsonCanvasData = () => {
    try {
      const v = JSON.parse(jsonCanvasData);
      setInputCanvasData(v);
    }
    catch (err) {
    }
  };

  useEffect(() => {
    setJsonCanvasData(beautify(inputCanvasData, null, 2, 100));
  }, [inputCanvasData])

  const onEditNode = (node) => {
    console.log('editing', node);
  };

  // console.log('FlowEditor before render');

  return (
    <div>
      <FlowEditor
        value={inputCanvasData}
        dataProvider={dataProvider}
        onChange={state.onChange}
        mode="edit"
        onEditNode={onEditNode}
        onClickNode={(v) => console.log('click', v)}
        debug
        // grabbing
        grabbing={{
          initScale: 100,
          maxScale: 100,
          minScale: 20,
          scaleStep: 1,
          scaleButtonsPosition: 'right bottom',
          buttonStyle: {
            position: 'fixed'
          }
        }}
      />
      <div>
        <div style={{ width: '100%', height: 600, border: '1px solid red' }}>
          <h2>画布详情页</h2>
          <FlowCanvas dataProvider={dataProvider} value={[...inputCanvasData]} mode="preview" grabbing={{
            initScale: 100,
            maxScale: 100,
            minScale: 20,
            scaleStep: 1,
            scaleButtonsPosition: 'right bottom',
            buttonTipProps: {
            }
          }} />
        </div>
        <div style={{ width: '100%', height: 600, border: '1px solid red' }}>
          <FlowCanvas dataProvider={dataProvider} value={[...inputCanvasData]} mode="detail" />
        </div>
        <div style={{ display: 'inline-block', width: '48%' }}>
          <Button onClick={onSubmitBoxData}>Submit Box Data</Button>
          <TextArea
            value={jsonBoxData}
            rows={30}
            onChange={e => setJsonBoxData(e.target.value)}
          />
        </div>
        <div style={{ display: 'inline-block', width: '48%' }}>
          <Button onClick={onSubmitJsonCanvasData}>Submit Canvas Data</Button>
          <TextArea
            value={jsonCanvasData}
            rows={30}
            onChange={e => setJsonCanvasData(e.target.value)}
          />
        </div>
      </div>
      <div>
        <div
          style={{ width: 100, height: 100, border: '1px solid #aaa' }}
          draggable
          onDrag={e => console.log(e)}
        >
          drag me
        </div>
      </div>
    </div>
  );
}
