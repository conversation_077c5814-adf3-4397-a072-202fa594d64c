import React, { useState, useRef, useEffect } from 'react';
import _ from 'lodash';
import { Button, Input } from 'antd';
import { Customize } from 'wolf-static-cpnt';
import 'react-json-pretty/themes/monikai.css';
import JSONPretty from 'react-json-pretty';

const { TextArea } = Input;

const tableId = 41;
const propertyItemList = [{
  tableId,
  schemaId: 79,
  items: [

    {
      "name": "15",
      "value": "15"
    },
    {
      "name": "16",
      "value": "16"
    },
    {
      "name": "17",
      "value": "17"
    }
  ]
}
];
const propertyList = [
  {
    "field": "dt_id",
    "fieldType": "STRING",
    "tableId": 41,
    "schemaId": 74,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "画龙id",
    "isEnum": false
  },
  {
    "field": "scenario_code",
    "fieldType": "STRING",
    "tableId": 41,
    "schemaId": 75,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "运营场景code",
    "isEnum": false
  },
  {
    "field": "name",
    "fieldType": "STRING",
    "tableId": 41,
    "schemaId": 76,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "名称",
    "isEnum": false
  },
  {
    "field": "phone",
    "fieldType": "STRING",
    "tableId": 41,
    "schemaId": 77,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "手机号",
    "isEnum": false
  },
  {
    "field": "gender",
    "fieldType": "STRING",
    "tableId": 41,
    "schemaId": 78,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "性别",
    "isEnum": false
  },
  {
    "field": "age",
    "fieldType": "INT",
    "tableId": 41,
    "schemaId": 79,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "年龄",
    "isEnum": true
  },
  {
    "field": "test",
    "fieldType": "STRING",
    "tableId": 41,
    "schemaId": 110,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "测试",
    "isEnum": false
  },
  {
    "field": "test01",
    "fieldType": "STRING",
    "tableId": 41,
    "schemaId": 111,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "测试01",
    "isEnum": false
  },
  {
    "field": "test02",
    "fieldType": "STRING",
    "tableId": 41,
    "schemaId": 112,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "测试02",
    "isEnum": false
  },
  {
    "field": "test03",
    "fieldType": "STRING",
    "tableId": 41,
    "schemaId": 113,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "测试03",
    "isEnum": false
  },
  {
    "field": "birthday",
    "fieldType": "DATE",
    "tableId": 41,
    "schemaId": 114,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "生日",
    "isEnum": false
  },
  {
    "field": "timestamp",
    "fieldType": "TIMESTAMP",
    "tableId": 41,
    "schemaId": 274,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "timestamp",
    "isEnum": false
  },
  {
    "field": "country",
    "fieldType": "STRING",
    "tableId": 41,
    "schemaId": 1080,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "国家",
    "isEnum": false
  },
  {
    "field": "province",
    "fieldType": "STRING",
    "tableId": 41,
    "schemaId": 1081,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "省份",
    "isEnum": false
  },
  {
    "field": "city",
    "fieldType": "STRING",
    "tableId": 41,
    "schemaId": 1082,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "市",
    "isEnum": false
  },
  {
    "field": "education",
    "fieldType": "STRING",
    "tableId": 41,
    "schemaId": 1083,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "学历",
    "isEnum": false
  },
  {
    "field": "vip_level",
    "fieldType": "STRING",
    "tableId": 41,
    "schemaId": 1084,
    "level1": "用户属性",
    "level2": "",
    "fieldName": "会员等级",
    "isEnum": false
  }
]

const filterValue =
  [
    {
      groupId: 60,
      connector: 'AND',
      filters: [
        {
          connector: 'OR',
          filters: [
            {
              tableId,
              schemaId: 4,
              fieldName: '年龄',
              field: 'age',
              fieldType: 'INT',
              level1: 'level-has-level2',
              level2: '',
              operator: 'EQ',
              isEnum: true,
              value: '11'
            },
            {
              tableId,
              schemaId: 5,
              fieldName: '城市很长很长很长很长很长很长很长很长很长很长很长',
              field: 'country',
              fieldType: 'STRING',
              level1: 'level-long',
              level2: '',
              operator: 'EQ',
              isEnum: true,
              value: '北京'
            }
          ]
        },
        {
          connector: 'OR',
          filters: [
            {
              tableId,
              schemaId: 2,
              fieldName: '事件名称',
              field: 'eventName',
              fieldType: 'STRING',
              level1: 'level-has-level2',
              level2: 'level2-1',
              isEnum: true,
              operator: 'EQ',
              value: '页面浏览'
            },
            {
              tableId,
              schemaId: 4,
              fieldName: '年龄',
              field: 'age',
              fieldType: 'INT',
              level1: 'level-has-level2',
              level2: '',
              operator: 'BETWEEN',
              value: [0, 100]
            }
          ]
        }
      ]
    }
  ]

const groupList = { "header": { "code": 0 }, "body": { "content": [{ "createTime": 1593417294000, "updateTime": 1593569976000, "createUserId": 2, "updateUserId": 2, "createUserName": "analyzer", "updateUserName": "analyzer", "projectId": "qvAD1jk8q0hA0Oxm", "id": 54, "name": "zhao-test02_副本", "customerCount": 0, "status": "DRAFT", "calcStatus": "NOTRUN", "type": "CONDITIONAL", "display": true }, { "createTime": 1593497654000, "updateTime": 1593497828000, "createUserId": 2, "updateUserId": 2, "createUserName": "analyzer", "updateUserName": "analyzer", "projectId": "qvAD1jk8q0hA0Oxm", "id": 63, "name": "384", "customerCount": 384, "status": "NORMAL", "calcStatus": "SUC", "calcMemo": "", "type": "CONDITIONAL", "lastCalcTime": 1593497820000, "calcRule": "ONCE", "filterInfo": { "connector": "AND", "filters": [{ "connector": "AND", "filters": [{ "field": "email", "fieldType": "STRING", "operator": "EQ", "value": "<EMAIL>", "tableId": 62, "schemaId": 392, "level1": "", "level2": "", "fieldName": "email", "isEnum": false }] }] }, "display": true }, { "createTime": 1593420329000, "updateTime": 1593486004000, "createUserId": 2, "updateUserId": 7, "createUserName": "analyzer", "updateUserName": "新增成员", "projectId": "qvAD1jk8q0hA0Oxm", "id": 60, "name": "zhao-test02_副本1", "customerCount": 1, "status": "NORMAL", "calcStatus": "SUC", "calcMemo": "", "type": "CONDITIONAL", "lastCalcTime": 1593486000000, "calcRule": "ONCE", "filterInfo": { "connector": "AND", "filters": [{ "connector": "AND", "filters": [{ "field": "user_id", "fieldType": "STRING", "operator": "EQ", "value": "dbcf5914241a82fc", "tableId": 62, "schemaId": 390, "level1": "", "level2": "", "fieldName": "user_id", "isEnum": false }] }] }, "display": true }, { "createTime": 1593411470000, "updateTime": 1593485224000, "createUserId": 2, "updateUserId": 7, "createUserName": "analyzer", "updateUserName": "新增成员", "projectId": "qvAD1jk8q0hA0Oxm", "id": 53, "name": "zhao-test03", "customerCount": 2, "status": "NORMAL", "calcStatus": "SUC", "calcMemo": "", "type": "CONDITIONAL", "lastCalcTime": 1593485220000, "calcRule": "ONCE", "filterInfo": { "connector": "OR", "filters": [{ "connector": "AND", "filters": [{ "field": "user_id", "fieldType": "STRING", "operator": "EQ", "value": "dbcf5914241a82fc", "tableId": 62, "schemaId": 390, "level1": "", "level2": "", "fieldName": "user_id", "isEnum": false }] }, { "connector": "AND", "filters": [{ "field": "mobileNo", "fieldType": "STRING", "operator": "EQ", "value": "2522406311606", "tableId": 62, "schemaId": 389, "level1": "", "level2": "", "fieldName": "mobileNo", "isEnum": false }] }] }, "display": true }, { "createTime": 1593322962000, "updateTime": 1593410705000, "createUserId": 2, "updateUserId": 2, "createUserName": "analyzer", "updateUserName": "analyzer", "projectId": "qvAD1jk8q0hA0Oxm", "id": 50, "name": "zhao-test02", "customerCount": 9944, "status": "NORMAL", "calcStatus": "SUC", "calcMemo": "", "type": "CONDITIONAL", "lastCalcTime": 1593410640000, "calcRule": "ONCE", "filterInfo": { "connector": "AND", "filters": [{ "connector": "AND", "filters": [{ "field": "job", "fieldType": "STRING", "operator": "EQ", "value": "UI设计师/顾问", "tableId": 62, "schemaId": 401, "level1": "", "level2": "", "fieldName": "job", "showValue": "UI设计师/顾问", "isEnum": true }] }] }, "display": true }, { "createTime": 1593000165000, "updateTime": 1593000185000, "createUserId": 2, "updateUserId": 2, "createUserName": "analyzer", "updateUserName": "analyzer", "projectId": "qvAD1jk8q0hA0Oxm", "id": 49, "name": "zhao-test01", "customerCount": 200000, "status": "NORMAL", "calcStatus": "SUC", "calcMemo": "", "type": "CONDITIONAL", "lastCalcTime": 1593000180000, "calcRule": "ONCE", "filterInfo": { "connector": "AND", "filters": [{ "connector": "AND", "filters": [{ "field": "resolution", "fieldType": "STRING", "operator": "EQ", "value": "1080*1080", "tableId": 62, "schemaId": 379, "level1": "", "level2": "", "fieldName": "resolution", "showValue": "1080*1080", "isEnum": true }] }] }, "display": true }], "number": 0, "size": 10, "totalElements": 6 } };

const TestFilter = () => {
  const [value, setValue] = useState({ connector: 'OR', filters: [{ filter: { connector: 'AND', filters: [] } }] });
  const [mode, setMode] = useState('edit');
  const [jsonData, setJsonData] = useState('{}');
  const ref = useRef(null);

  const dataProvider = {
    getPropertyList: (name) => {
      return propertyList;
    },
    getPropertyEnumList: (tableId, schemaId) => {
      const propertyItem = _.find(propertyItemList, (v) => v.tableId === tableId && v.schemaId === schemaId);
      return propertyItem ? propertyItem.items : [];
    }
  };

  const onChange = (value) => {
    setValue(value);
  };

  const onSubmit = () => {
    if (ref.current) {
      console.log(ref.current.isValid(), 'isValid----------------------------------');
    }
  };

  useEffect(() => {
    setJsonData(JSON.stringify(value));
  }, [value])


  return (
    <div>
      <div style={{ margin: 20, width: 900, padding: 20, border: '1px solid #000', display: 'inline-block' }} >
        <Customize selectList={groupList.body.content.filter(n => n.status === "NORMAL")} dataProvider={dataProvider} value={value} onChange={onChange} mode={mode} ref={ref} />
        <Button onClick={() => setValue({})}>清空</Button>
        <Button onClick={() => setMode(mode === 'edit' ? 'detail' : 'edit')}>
          只读切换
        </Button>
        <Button onClick={() => onSubmit()}>submit</Button>
        <hr />
        <div>
          <JSONPretty data={value} />
        </div>
      </div>
      <div style={{ display: 'inline-block', width: 500 }}>
        <Button onClick={() => setValue(JSON.parse(jsonData))}>Submit Data</Button>
        <TextArea value={jsonData} rows={30} onChange={e => setJsonData(e.target.value)} />
      </div>
    </div>
  );
};

export default TestFilter;
