export default {
  boxData: [
    {
      id: 1,
      name: 'ENTRY',
      displayName: '活动开始',
      remark: '请补充说明',
      orders: 1,
      children: [
        {
          createTime: 1580458045000,
          updateTime: 1587041105000,
          id: 1,
          groupId: 1,
          name: '用户分群',
          displayName: '用户分群',
          busiType: 'UserEntryNode',
          editorTitle: '用户分群',
          editorWidth: 600,
          editorTip:
            '可选择多个分群发送活动，合并后系统会对用户进行去重。最多选择5个分群。',
          fakeable: true,
          displayInBox: true,
          color: '#1EC78B',
          icon: '#iconicon_yonghu_',
          shape: 'CIRCLE',
          remark: '',
          orders: 1,
          joinType: 'ENTRY',
          status: 'NORMAL'
        },
        {
          createTime: 1580458077000,
          updateTime: 1587041163000,
          id: 2,
          groupId: 1,
          name: '起始事件',
          displayName: '触发事件',
          busiType: 'EventEntryNode',
          editorTitle: '起始事件',
          editorWidth: 600,
          editorTip: '至多设置5个起始事件，用户满足以下任意事件即触发该活动。',
          fakeable: false,
          displayInBox: true,
          color: '#1EC78B',
          icon: '#iconyonghuzhongxin-2',
          shape: 'CIRCLE',
          remark: '',
          orders: 2,
          joinType: 'ENTRY',
          status: 'NORMAL'
        }
      ]
    },
    {
      id: 2,
      name: 'TRIGGER',
      displayName: '触达渠道',
      remark: '请补充说明',
      orders: 2,
      children: [
        {
          createTime: 1580458136000,
          updateTime: 1587041192000,
          id: 3,
          groupId: 2,
          name: '发送短信',
          displayName: '发送短信',
          busiType: 'SMSPushNode',
          editorTitle: '短信推送',
          editorWidth: 600,
          editorTip: '发送短信',
          fakeable: true,
          displayInBox: true,
          color: '#0099FF',
          icon: '#iconicon_shouji',
          shape: 'SQUARE',
          remark: '',
          orders: 1,
          joinType: 'FUNCTION',
          status: 'NORMAL'
        },
        {
          createTime: 1580458178000,
          updateTime: 1587041208000,
          id: 4,
          groupId: 2,
          name: 'APP推送',
          displayName: 'APP推送',
          busiType: 'AppPushNode',
          editorTitle: 'app推送',
          editorWidth: 600,
          editorTip: 'app推送',
          fakeable: true,
          displayInBox: true,
          color: '#0099FF',
          icon: '#iconapp',
          shape: 'SQUARE',
          remark: '',
          orders: 2,
          joinType: 'FUNCTION',
          status: 'NORMAL'
        },
        {
          createTime: 1580458191000,
          updateTime: 1587041232000,
          id: 5,
          groupId: 2,
          name: '微信推送',
          displayName: '微信推送',
          busiType: 'WxPushNode',
          editorTitle: '微信推送',
          editorWidth: 600,
          editorTip: '微信推送',
          fakeable: true,
          displayInBox: true,
          color: '#0099FF',
          icon: '#iconweixincopy',
          shape: 'SQUARE',
          remark: '',
          orders: 3,
          joinType: 'FUNCTION',
          status: 'NORMAL'
        }
      ]
    },
    {
      id: 3,
      name: 'TIME_CONTROL',
      displayName: '时间控制',
      remark: '请补充说明',
      orders: 3,
      children: [
        {
          createTime: 1580458217000,
          updateTime: 1587041258000,
          id: 6,
          groupId: 3,
          name: '等待时长',
          displayName: '等待时长',
          busiType: 'WaitTimerNode',
          editorTitle: '等待时间',
          editorWidth: 600,
          editorTip: '等待时长最高为60天',
          fakeable: true,
          displayInBox: true,
          color: '#0099FF',
          icon: '#iconicon_lishijilu',
          shape: 'SQUARE',
          remark: '',
          orders: 1,
          joinType: 'FUNCTION',
          status: 'NORMAL'
        },
        {
          createTime: 1580458217000,
          updateTime: 1587041258000,
          id: 20,
          groupId: 3,
          name: '指定时刻',
          displayName: '指定时刻',
          busiType: 'AtTimeNode',
          editorTitle: '指定时刻',
          editorWidth: 600,
          editorTip: '指定时刻',
          fakeable: true,
          displayInBox: true,
          color: '#0099FF',
          icon: '#iconicon_lishijilu',
          shape: 'SQUARE',
          remark: '',
          orders: 20,
          joinType: 'FUNCTION',
          status: 'NORMAL'
        },
        {
          createTime: 1580458217000,
          updateTime: 1587041258000,
          id: 21,
          groupId: 3,
          name: '标签标记',
          displayName: '标签标记',
          busiType: 'MarkTagNode',
          editorTitle: '标签标记',
          editorWidth: 600,
          editorTip: '标签标记',
          fakeable: true,
          displayInBox: true,
          color: '#BD10E0',
          icon: '#iconicon_lishijilu',
          shape: 'SQUARE',
          remark: '',
          orders: 21,
          joinType: 'FUNCTION',
          status: 'NORMAL'
        }
      ]
    },
    {
      id: 4,
      name: 'SPLIT',
      displayName: '流程控制',
      remark: '请补充说明',
      orders: 4,
      children: [
        {
          createTime: 1580458243000,
          updateTime: 1587041280000,
          id: 7,
          groupId: 4,
          name: '触发事件分支',
          displayName: '触发事件分支',
          busiType: 'EventTriggerSplitNode',
          editorTitle: '触发事件分支',
          editorWidth: 600,
          editorTip: '最多可添加5个触发事件。完成任意事件即满足触发。',
          fakeable: false,
          displayInBox: true,
          color: '#FCA400',
          icon: '#iconyonghuzhongxin-2',
          shape: 'DIAMOND',
          remark: '',
          orders: 1,
          joinType: 'SPLIT',
          status: 'NORMAL'
        },
        {
          createTime: 1583221152000,
          updateTime: 1587041397000,
          id: 10,
          groupId: 4,
          name: '合并分支',
          displayName: '合并分支',
          busiType: 'JoinNode',
          editorTitle: '',
          editorWidth: 0,
          editorTip: '',
          fakeable: false,
          displayInBox: true,
          color: '#FCA400',
          icon: '#icongaibanxianxingtubiao-',
          shape: 'DIAMOND',
          remark: '',
          orders: 1,
          joinType: 'FUNCTION',
          status: 'NORMAL'
        },
        {
          createTime: 1583221152000,
          updateTime: 1587041439000,
          id: 11,
          groupId: 4,
          name: '合并辅助分支',
          displayName: '合并辅助分支',
          busiType: 'JoinHelperNode',
          editorTitle: '',
          editorWidth: 0,
          editorTip: '',
          fakeable: false,
          displayInBox: true,
          color: '#FCA400',
          icon: '#iconicon_xiaochengxu',
          shape: 'LIT_DIAMOND',
          remark: '',
          orders: 1,
          joinType: 'JOIN',
          status: 'NORMAL'
        },
        {
          createTime: 1586428380000,
          updateTime: 1587041474000,
          id: 13,
          groupId: 4,
          name: '嵌个baidu试试',
          displayName: '嵌个baidu试试',
          busiType: 'IFrameNode',
          iframeUrl: 'https://www.baidu.com',
          editorTitle: 'iframe型测试',
          editorWidth: 600,
          editorTip: '随便嵌入',
          fakeable: false,
          displayInBox: true,
          color: '#0099FF',
          icon: '#iconicon_shezhi_',
          shape: 'SQUARE',
          remark: '',
          orders: 1,
          joinType: 'FUNCTION',
          status: 'NORMAL'
        },
        {
          createTime: 1580458266000,
          updateTime: 1587041372000,
          id: 8,
          groupId: 4,
          name: '人群条件分支',
          displayName: '人群条件分支',
          busiType: 'UserFilterSplitNode',
          editorTitle: '人群条件分支',
          editorWidth: 600,
          editorTip:
            '请按条件配置不同的人群分支，最多5分支。优先满足前一个分支的用户，将不参与后面分支的判断。',
          fakeable: false,
          displayInBox: true,
          color: '#FCA400',
          icon: '#iconicon_shaixuan',
          shape: 'DIAMOND',
          remark: '',
          orders: 2,
          joinType: 'SPLIT',
          status: 'NORMAL'
        },
        {
          createTime: 1580458077000,
          updateTime: 1587041447000,
          id: 12,
          groupId: 4,
          name: '退出节点',
          displayName: '退出节点',
          busiType: 'ExitHelperNode',
          editorTitle: '',
          editorWidth: 0,
          editorTip: '',
          fakeable: false,
          displayInBox: true,
          color: '#1EC78B',
          icon: '#iconyonghuzhongxin-1',
          shape: 'CIRCLE',
          remark: '',
          orders: 2,
          joinType: 'END',
          status: 'NORMAL'
        },
        {
          createTime: 1580458282000,
          updateTime: 1587041389000,
          id: 9,
          groupId: 4,
          name: '随机人群分支',
          displayName: '随机人群分支',
          busiType: 'RandomUserSplitNode',
          editorTitle: 'A/B 测试',
          editorWidth: 600,
          editorTip: '可将人群随机拆分成2～5个分组，请保证百分比相加等于100',
          fakeable: false,
          displayInBox: true,
          color: '#FCA400',
          icon: '#iconsuijifenqun',
          shape: 'DIAMOND',
          remark: '',
          orders: 3,
          joinType: 'SPLIT',
          status: 'NORMAL'
        }
      ]
    }
  ],
  // canvasData: [
  //   {
  //     id: 1,
  //     groupId: 1,
  //     name: '用户分群',
  //     busiType: 'UserEntryNode',
  //     fakeable: true,
  //     displayInBox: true,
  //     color: '#1EC78B',
  //     icon: '#iconicon_yonghu_',
  //     shape: 'CIRCLE',
  //     orders: 1,
  //     joinType: 'ENTRY',
  //     status: 'NORMAL',
  //     displayName: '用户分群',
  //     nodeId: 1,
  //     fatherIds: [],
  //     childrenIds: [8],
  //     branchIndex: 0,
  //     x: 0,
  //     y: 0
  //   },
  //   {
  //     name: '退出',
  //     busiType: 'ExitHelperNode',
  //     color: '#FCA400',
  //     icon: '#iconyonghuzhongxin-1',
  //     shape: 'SQUARE',
  //     joinType: 'END',
  //     nodeId: 2,
  //     fatherIds: [3],
  //     childrenIds: [],
  //     branchIndex: 0,
  //     x: 3,
  //     y: 0
  //   },
  //   {
  //     id: 8,
  //     groupId: 4,
  //     name: '人群条件分支',
  //     busiType: 'UserFilterSplitNode',
  //     fakeable: false,
  //     displayInBox: true,
  //     color: '#FCA400',
  //     icon: '#iconicon_shaixuan',
  //     shape: 'DIAMOND',
  //     orders: 2,
  //     joinType: 'SPLIT',
  //     status: 'NORMAL',
  //     displayName: '人群条件分支',
  //     nodeId: 3,
  //     fatherIds: [8],
  //     childrenIds: [2, 4, 6],
  //     branchIndex: 0,
  //     x: 2,
  //     y: 0,
  //     detail: {
  //       branchList: [
  //         { branchName: 'test branch 0' },
  //         { branchName: 'test branch 1' },
  //         { branchName: 'test branch 2' }
  //       ]
  //     }
  //   },
  //   {
  //     id: 21,
  //     groupId: 3,
  //     name: '标签标记',
  //     busiType: 'MarkTagNode',
  //     fakeable: false,
  //     displayInBox: true,
  //     color: '#BD10E0',
  //     icon: '#iconicon_shaixuan',
  //     shape: 'DIAMOND',
  //     orders: 21,
  //     joinType: 'FUNCTION',
  //     status: 'NORMAL',
  //     displayName: '标签标记',
  //     nodeId: 8,
  //     fatherIds: [1],
  //     childrenIds: [3],
  //     branchIndex: 0,
  //     x: 1,
  //     y: 0,
  //     detail: {
  //       nodeLabelList: [
  //         { nodeLabel: '测试1', value: '1' },
  //         { nodeLabel: '测试2', value: '2' },
  //         { nodeLabel: '测试3测试3测试3测试3测试3测试3测试', value: '测试3测试3测试3测试3测试3测试3测试' },
  //         { nodeLabel: '测试4测试3测试3测试3测试3测试3测试', value: '测试4测试3测试3测试3测试3测试3测试' },
  //         { nodeLabel: '测试5测试3测试3测试3测试3测试3测试', value: '测试5测试3测试3测试3测试3测试3测试' },
  //       ]
  //     }
  //   },
  //   {
  //     name: '退出',
  //     busiType: 'ExitHelperNode',
  //     color: '#FCA400',
  //     icon: '#iconyonghuzhongxin-1',
  //     shape: 'SQUARE',
  //     joinType: 'END',
  //     nodeId: 4,
  //     fatherIds: [3],
  //     childrenIds: [],
  //     branchIndex: 1,
  //     x: 3,
  //     y: 1
  //   },
  //   {
  //     id: 10,
  //     groupId: 4,
  //     name: '合并分支',
  //     busiType: 'JoinNode',
  //     fakeable: false,
  //     displayInBox: true,
  //     color: '#FCA400',
  //     icon: '#icongaibanxianxingtubiao-',
  //     shape: 'DIAMOND',
  //     orders: 1,
  //     joinType: 'JOIN',
  //     status: 'NORMAL',
  //     displayName: '合并分支',
  //     nodeId: 6,
  //     fatherIds: [3],
  //     childrenIds: [7],
  //     branchIndex: 2,
  //     x: 3,
  //     y: 2
  //   },
  //   {
  //     name: '',
  //     busiType: 'JoinHelperNode',
  //     color: '#FCA400',
  //     icon: '#iconicon_xiaochengxu',
  //     shape: 'LIT_DIAMOND',
  //     joinType: 'JOIN',
  //     nodeId: 7,
  //     fatherIds: [6],
  //     childrenIds: [],
  //     branchIndex: 0,
  //     x: 4,
  //     y: 2
  //   }
  // ]
  canvasData: []
  // canvasData: [
  //   {
  //     id: 68,
  //     name: '起始事件',
  //     displayName: '触发事件',
  //     busiType: 'EventEntryNode',
  //     editorWidth: 0,
  //     fakeable: true,
  //     displayInBox: true,
  //     color: '#1EC78B',
  //     icon: '#iconyonghuzhongxin-2',
  //     shape: 'CIRCLE',
  //     orders: 2,
  //     joinType: 'ENTRY',
  //     status: 'NORMAL',
  //     nodeId: 3,
  //     fatherIds: [],
  //     childrenIds: [12],
  //     branchIndex: 0,
  //     x: 0,
  //     y: 0,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'EventEntryNode',
  //       eventList: [
  //         {
  //           event: {
  //             createTime: 1615341951000,
  //             updateTime: 1615341951000,
  //             createUserId: 1,
  //             updateUserId: 1,
  //             createUserName: 'admin',
  //             updateUserName: 'admin',
  //             projectId: 'K6CtZloY7rrzgqwL',
  //             id: 12,
  //             name: 'pv_01',
  //             eventNameValue: 'pv_01',
  //             specialPropertyMappingList: [
  //               {
  //                 displayName: '事件类型',
  //                 propertySchema: 'event_body.type',
  //                 dataType: 'INT',
  //                 index: 0
  //               },
  //               {
  //                 displayName: '事件分辨率',
  //                 propertySchema: 'event_body.resolution',
  //                 dataType: 'STRING',
  //                 index: 0
  //               },
  //               {
  //                 displayName: '事件语言',
  //                 propertySchema: 'event_body.language',
  //                 dataType: 'LONG',
  //                 index: 0
  //               },
  //               {
  //                 displayName: '事件代理',
  //                 propertySchema: 'event_body.user_agent',
  //                 dataType: 'DOUBLE',
  //                 index: 0
  //               }
  //             ],
  //             eventType: 'BURIED_POINT_EVENT',
  //             level1: '',
  //             level2: '',
  //             remark: ''
  //           },
  //           filter: {
  //             connector: 'OR',
  //             filters: [
  //               {
  //                 connector: 'AND',
  //                 filters: [
  //                   {
  //                     field: 'i_var0',
  //                     fieldType: 'INT',
  //                     operator: 'EQ',
  //                     value: '1',
  //                     tableId: 32,
  //                     level1: '事件专有属性',
  //                     level2: '',
  //                     fieldName: '事件类型',
  //                     isEnum: false
  //                   }
  //                 ],
  //                 empty: false
  //               },
  //               {
  //                 connector: 'AND',
  //                 filters: [
  //                   {
  //                     field: 's_var0',
  //                     fieldType: 'STRING',
  //                     operator: 'EQ',
  //                     value: '2',
  //                     tableId: 32,
  //                     level1: '事件专有属性',
  //                     level2: '',
  //                     fieldName: '事件分辨率',
  //                     isEnum: false
  //                   }
  //                 ],
  //                 empty: false
  //               },
  //               {
  //                 connector: 'AND',
  //                 filters: [
  //                   {
  //                     field: 'i_var0',
  //                     fieldType: 'LONG',
  //                     operator: 'EQ',
  //                     value: '3',
  //                     tableId: 32,
  //                     level1: '事件专有属性',
  //                     level2: '',
  //                     fieldName: '事件语言',
  //                     isEnum: false
  //                   }
  //                 ],
  //                 empty: false
  //               },
  //               {
  //                 connector: 'AND',
  //                 filters: [
  //                   {
  //                     field: 'd_var0',
  //                     fieldType: 'DOUBLE',
  //                     operator: 'EQ',
  //                     value: '4',
  //                     tableId: 32,
  //                     level1: '事件专有属性',
  //                     level2: '',
  //                     fieldName: '事件代理',
  //                     isEnum: false
  //                   }
  //                 ],
  //                 empty: false
  //               }
  //             ],
  //             empty: false
  //           }
  //         },
  //         {
  //           event: {
  //             createTime: 1615341988000,
  //             updateTime: 1615342005000,
  //             createUserId: 1,
  //             updateUserId: 1,
  //             createUserName: 'admin',
  //             updateUserName: 'admin',
  //             projectId: 'K6CtZloY7rrzgqwL',
  //             id: 13,
  //             name: 'pv_02',
  //             filter: {
  //               connector: 'AND',
  //               filters: [
  //                 {
  //                   connector: 'AND',
  //                   filters: [
  //                     {
  //                       field: 'eventName',
  //                       fieldType: 'STRING',
  //                       operator: 'EQ',
  //                       value: 'pv_02',
  //                       tableId: 32,
  //                       schemaId: 14,
  //                       level1: '',
  //                       level2: '',
  //                       fieldName: 'eventName',
  //                       isEnum: false
  //                     }
  //                   ],
  //                   empty: false
  //                 }
  //               ],
  //               empty: false
  //             },
  //             specialPropertyMappingList: [],
  //             eventType: 'CUSTOM',
  //             level1: '',
  //             level2: '',
  //             remark: ''
  //           },
  //           filter: {
  //             connector: 'AND',
  //             filters: [
  //               {
  //                 connector: 'AND',
  //                 filters: [
  //                   {
  //                     field: 'eventName',
  //                     fieldType: 'STRING',
  //                     operator: 'IS_NOT_NULL',
  //                     tableId: 32,
  //                     schemaId: 14,
  //                     level1: '事件属性',
  //                     level2: '',
  //                     fieldName: 'eventName',
  //                     isEnum: false
  //                   }
  //                 ],
  //                 empty: false
  //               }
  //             ],
  //             empty: false
  //           }
  //         }
  //       ],
  //       isValid: true,
  //       isInit: true,
  //       hasData: true
  //     }
  //   },
  //   {
  //     id: 78,
  //     name: '退出节点',
  //     displayName: '退出节点',
  //     busiType: 'ExitHelperNode',
  //     editorWidth: 0,
  //     fakeable: false,
  //     displayInBox: false,
  //     color: '#1EC78B',
  //     icon: '#iconyonghuzhongxin-1',
  //     shape: 'CIRCLE',
  //     orders: 2,
  //     joinType: 'END',
  //     status: 'NORMAL',
  //     nodeId: 4,
  //     fatherIds: [9],
  //     childrenIds: [],
  //     branchIndex: 0,
  //     x: 7,
  //     y: 0,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'ExitHelperNode',
  //       isValid: false,
  //       isInit: false,
  //       hasData: false
  //     }
  //   },
  //   {
  //     id: 72,
  //     name: '等待时长',
  //     displayName: '等待时长',
  //     busiType: 'WaitTimerNode',
  //     editorWidth: 0,
  //     fakeable: true,
  //     displayInBox: true,
  //     color: '#0099FF',
  //     icon: '#iconicon_lishijilu',
  //     shape: 'SQUARE',
  //     orders: 1,
  //     joinType: 'FUNCTION',
  //     status: 'NORMAL',
  //     nodeId: 5,
  //     fatherIds: [13],
  //     childrenIds: [6],
  //     branchIndex: 0,
  //     x: 3,
  //     y: 0,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'WaitTimerNode',
  //       time: 4,
  //       timeUnit: 'MINUTE',
  //       isValid: true,
  //       isInit: true,
  //       hasData: true
  //     }
  //   },
  //   {
  //     id: 126,
  //     name: '多事件分支',
  //     displayName: '多事件分支',
  //     busiType: 'MultiEventSplitNode',
  //     editorWidth: 0,
  //     fakeable: false,
  //     displayInBox: true,
  //     color: '#FCA400',
  //     icon: '#iconduoshijian',
  //     shape: 'DIAMOND',
  //     orders: 1,
  //     joinType: 'SPLIT',
  //     status: 'NORMAL',
  //     nodeId: 6,
  //     fatherIds: [5],
  //     childrenIds: [10, 7, 8],
  //     branchIndex: 0,
  //     x: 4,
  //     y: 0,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'MultiEventSplitNode',
  //       branchList: [
  //         {
  //           event: {
  //             createTime: 1615341951000,
  //             updateTime: 1615345274000,
  //             createUserId: 1,
  //             updateUserId: 1,
  //             createUserName: 'admin',
  //             updateUserName: 'admin',
  //             projectId: 'K6CtZloY7rrzgqwL',
  //             id: 12,
  //             name: 'pv_01',
  //             eventNameValue: 'pv_01',
  //             specialPropertyMappingList: [
  //               {
  //                 displayName: '事件类型',
  //                 propertySchema: 'event_body.type',
  //                 dataType: 'INT',
  //                 index: 0
  //               },
  //               {
  //                 displayName: '事件分辨率',
  //                 propertySchema: 'event_body.resolution',
  //                 dataType: 'STRING',
  //                 index: 2
  //               },
  //               {
  //                 displayName: '事件语言',
  //                 propertySchema: 'event_body.language',
  //                 dataType: 'LONG',
  //                 index: 0
  //               },
  //               {
  //                 displayName: '事件代理',
  //                 propertySchema: 'event_body.user_agent',
  //                 dataType: 'DOUBLE',
  //                 index: 0
  //               }
  //             ],
  //             eventType: 'BURIED_POINT_EVENT',
  //             level1: '',
  //             level2: '',
  //             remark: ''
  //           },
  //           filter: {
  //             connector: 'OR',
  //             filters: [
  //               {
  //                 connector: 'AND',
  //                 filters: [
  //                   {
  //                     field: 'i_var0',
  //                     fieldType: 'INT',
  //                     operator: 'EQ',
  //                     value: '1',
  //                     tableId: 32,
  //                     level1: '事件专有属性',
  //                     level2: '',
  //                     fieldName: '事件类型',
  //                     isEnum: false
  //                   }
  //                 ],
  //                 empty: false
  //               },
  //               {
  //                 connector: 'AND',
  //                 filters: [
  //                   {
  //                     field: 's_var2',
  //                     fieldType: 'STRING',
  //                     operator: 'EQ',
  //                     value: '2',
  //                     tableId: 32,
  //                     level1: '事件专有属性',
  //                     level2: '',
  //                     fieldName: '事件分辨率',
  //                     isEnum: false
  //                   }
  //                 ],
  //                 empty: false
  //               },
  //               {
  //                 connector: 'AND',
  //                 filters: [
  //                   {
  //                     field: 'i_var0',
  //                     fieldType: 'LONG',
  //                     operator: 'EQ',
  //                     value: '3',
  //                     tableId: 32,
  //                     level1: '事件专有属性',
  //                     level2: '',
  //                     fieldName: '事件语言',
  //                     isEnum: false
  //                   }
  //                 ],
  //                 empty: false
  //               },
  //               {
  //                 connector: 'AND',
  //                 filters: [
  //                   {
  //                     field: 'd_var0',
  //                     fieldType: 'DOUBLE',
  //                     operator: 'EQ',
  //                     value: '4',
  //                     tableId: 32,
  //                     level1: '事件专有属性',
  //                     level2: '',
  //                     fieldName: '事件代理',
  //                     isEnum: false
  //                   }
  //                 ],
  //                 empty: false
  //               }
  //             ],
  //             empty: false
  //           },
  //           argList: [],
  //           branchName: 'pv_01',
  //           type: 'MAIN'
  //         },
  //         {
  //           event: {
  //             createTime: 1615341988000,
  //             updateTime: 1615342005000,
  //             createUserId: 1,
  //             updateUserId: 1,
  //             createUserName: 'admin',
  //             updateUserName: 'admin',
  //             projectId: 'K6CtZloY7rrzgqwL',
  //             id: 13,
  //             name: 'pv_02',
  //             filter: {
  //               connector: 'AND',
  //               filters: [
  //                 {
  //                   connector: 'AND',
  //                   filters: [
  //                     {
  //                       field: 'eventName',
  //                       fieldType: 'STRING',
  //                       operator: 'EQ',
  //                       value: 'pv_02',
  //                       tableId: 32,
  //                       schemaId: 14,
  //                       level1: '',
  //                       level2: '',
  //                       fieldName: 'eventName',
  //                       isEnum: false
  //                     }
  //                   ],
  //                   empty: false
  //                 }
  //               ],
  //               empty: false
  //             },
  //             specialPropertyMappingList: [],
  //             eventType: 'CUSTOM',
  //             level1: '',
  //             level2: '',
  //             remark: ''
  //           },
  //           filter: {
  //             connector: 'AND',
  //             filters: [
  //               {
  //                 connector: 'AND',
  //                 filters: [
  //                   {
  //                     field: 'eventName',
  //                     fieldType: 'STRING',
  //                     operator: 'IS_NOT_NULL',
  //                     tableId: 32,
  //                     schemaId: 14,
  //                     level1: '事件属性',
  //                     level2: '',
  //                     fieldName: 'eventName',
  //                     isEnum: false
  //                   }
  //                 ],
  //                 empty: false
  //               }
  //             ],
  //             empty: false
  //           },
  //           argList: [],
  //           branchName: 'pv_02',
  //           type: 'MAIN'
  //         },
  //         {
  //           branchName: '无',
  //           type: 'OTHER'
  //         }
  //       ],
  //       isValid: true,
  //       isInit: true,
  //       hasData: true
  //     }
  //   },
  //   {
  //     id: 78,
  //     name: '退出节点',
  //     displayName: '退出节点',
  //     busiType: 'ExitHelperNode',
  //     editorWidth: 0,
  //     fakeable: false,
  //     displayInBox: false,
  //     color: '#1EC78B',
  //     icon: '#iconyonghuzhongxin-1',
  //     shape: 'CIRCLE',
  //     orders: 2,
  //     joinType: 'END',
  //     status: 'NORMAL',
  //     nodeId: 7,
  //     fatherIds: [6],
  //     childrenIds: [],
  //     branchIndex: 1,
  //     x: 5,
  //     y: 2,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'ExitHelperNode',
  //       isValid: false,
  //       isInit: false,
  //       hasData: false
  //     }
  //   },
  //   {
  //     id: 78,
  //     name: '退出节点',
  //     displayName: '退出节点',
  //     busiType: 'ExitHelperNode',
  //     editorWidth: 0,
  //     fakeable: false,
  //     displayInBox: false,
  //     color: '#1EC78B',
  //     icon: '#iconyonghuzhongxin-1',
  //     shape: 'CIRCLE',
  //     orders: 2,
  //     joinType: 'END',
  //     status: 'NORMAL',
  //     nodeId: 8,
  //     fatherIds: [6],
  //     childrenIds: [],
  //     branchIndex: 2,
  //     x: 5,
  //     y: 3,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'ExitHelperNode',
  //       isValid: false,
  //       isInit: false,
  //       hasData: false
  //     }
  //   },
  //   {
  //     id: 73,
  //     name: '触发事件分支',
  //     displayName: '触发事件分支',
  //     busiType: 'EventTriggerSplitNode',
  //     editorWidth: 0,
  //     fakeable: true,
  //     displayInBox: true,
  //     color: '#FCA400',
  //     icon: '#iconyonghuzhongxin-2',
  //     shape: 'DIAMOND',
  //     orders: 1,
  //     joinType: 'SPLIT',
  //     status: 'NORMAL',
  //     nodeId: 9,
  //     fatherIds: [10],
  //     childrenIds: [4, 11],
  //     branchIndex: 0,
  //     x: 6,
  //     y: 0,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'EventTriggerSplitNode',
  //       branchList: [
  //         {
  //           branchName: '做过：pv_01',
  //           inCounts: 0
  //         },
  //         {
  //           branchName: '未做过：pv_01',
  //           inCounts: 0
  //         }
  //       ],
  //       eventAndFilter: {
  //         event: {
  //           createTime: 1615341951000,
  //           updateTime: 1615345274000,
  //           createUserId: 1,
  //           updateUserId: 1,
  //           createUserName: 'admin',
  //           updateUserName: 'admin',
  //           projectId: 'K6CtZloY7rrzgqwL',
  //           id: 12,
  //           name: 'pv_01',
  //           eventNameValue: 'pv_01',
  //           specialPropertyMappingList: [
  //             {
  //               displayName: '事件类型',
  //               propertySchema: 'event_body.type',
  //               dataType: 'INT',
  //               index: 0
  //             },
  //             {
  //               displayName: '事件分辨率',
  //               propertySchema: 'event_body.resolution',
  //               dataType: 'STRING',
  //               index: 2
  //             },
  //             {
  //               displayName: '事件语言',
  //               propertySchema: 'event_body.language',
  //               dataType: 'LONG',
  //               index: 0
  //             },
  //             {
  //               displayName: '事件代理',
  //               propertySchema: 'event_body.user_agent',
  //               dataType: 'DOUBLE',
  //               index: 0
  //             }
  //           ],
  //           eventType: 'BURIED_POINT_EVENT',
  //           level1: '',
  //           level2: '',
  //           remark: ''
  //         },
  //         filter: {
  //           filters: [],
  //           empty: true
  //         },
  //         eventCount: 1
  //       },
  //       isValid: true,
  //       isInit: true,
  //       hasData: true
  //     }
  //   },
  //   {
  //     id: 72,
  //     name: '等待时长',
  //     displayName: '等待时长',
  //     busiType: 'WaitTimerNode',
  //     editorWidth: 0,
  //     fakeable: true,
  //     displayInBox: true,
  //     color: '#0099FF',
  //     icon: '#iconicon_lishijilu',
  //     shape: 'SQUARE',
  //     orders: 1,
  //     joinType: 'FUNCTION',
  //     status: 'NORMAL',
  //     nodeId: 10,
  //     fatherIds: [6],
  //     childrenIds: [9],
  //     branchIndex: 0,
  //     x: 5,
  //     y: 0,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'WaitTimerNode',
  //       time: 4,
  //       timeUnit: 'MINUTE',
  //       isValid: true,
  //       isInit: true,
  //       hasData: true
  //     }
  //   },
  //   {
  //     id: 78,
  //     name: '退出节点',
  //     displayName: '退出节点',
  //     busiType: 'ExitHelperNode',
  //     editorWidth: 0,
  //     fakeable: false,
  //     displayInBox: false,
  //     color: '#1EC78B',
  //     icon: '#iconyonghuzhongxin-1',
  //     shape: 'CIRCLE',
  //     orders: 2,
  //     joinType: 'END',
  //     status: 'NORMAL',
  //     nodeId: 11,
  //     fatherIds: [9],
  //     childrenIds: [],
  //     branchIndex: 1,
  //     x: 7,
  //     y: 1,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'ExitHelperNode',
  //       isValid: false,
  //       isInit: false,
  //       hasData: false
  //     }
  //   },
  //   {
  //     id: 128,
  //     name: '标签标记',
  //     displayName: '标签标记',
  //     busiType: 'MarkTagNode',
  //     editorWidth: 0,
  //     fakeable: false,
  //     displayInBox: true,
  //     color: '#BD10E0',
  //     icon: '#icontag',
  //     shape: 'DIAMOND',
  //     orders: 1,
  //     joinType: 'FUNCTION',
  //     status: 'NORMAL',
  //     nodeId: 12,
  //     fatherIds: [3],
  //     childrenIds: [13],
  //     branchIndex: 0,
  //     x: 1,
  //     y: 0,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'MarkTagNode',
  //       nodeLabelList: [
  //         {
  //           nodeLabel: 'pv_01',
  //           value: '1'
  //         },
  //         {
  //           nodeLabel: 'pv_02',
  //           value: '2'
  //         }
  //       ],
  //       isValid: true,
  //       isInit: true,
  //       hasData: true
  //     }
  //   },
  //   {
  //     id: 127,
  //     name: '标签分支',
  //     displayName: '标签分支',
  //     busiType: 'TagSpiltNode',
  //     editorWidth: 0,
  //     fakeable: false,
  //     displayInBox: true,
  //     color: '#FCA400',
  //     icon: '#iconicon_dingyiyemian',
  //     shape: 'DIAMOND',
  //     orders: 1,
  //     joinType: 'SPLIT',
  //     status: 'NORMAL',
  //     nodeId: 13,
  //     fatherIds: [12],
  //     childrenIds: [5, 14, 22],
  //     branchIndex: 0,
  //     x: 2,
  //     y: 0,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'TagSpiltNode',
  //       branchList: [
  //         {
  //           branchName: 'pv_01',
  //           inCounts: 0,
  //           type: 'MAIN',
  //           connector: 'AND',
  //           filters: [
  //             {
  //               tagName: 'pv_01',
  //               dataType: 'STRING',
  //               operator: 'EQ',
  //               tagValue: '1'
  //             }
  //           ]
  //         },
  //         {
  //           branchName: 'pv_02',
  //           inCounts: 0,
  //           type: 'MAIN',
  //           connector: 'AND',
  //           filters: [
  //             {
  //               tagName: 'pv_02',
  //               dataType: 'STRING',
  //               operator: 'EQ',
  //               tagValue: '2'
  //             }
  //           ]
  //         },
  //         {
  //           branchName: '无',
  //           inCounts: 0,
  //           type: 'OTHER',
  //           connector: 'AND',
  //           filters: []
  //         }
  //       ],
  //       isValid: true,
  //       isInit: true,
  //       hasData: true
  //     }
  //   },
  //   {
  //     id: 78,
  //     name: '退出节点',
  //     displayName: '退出节点',
  //     busiType: 'ExitHelperNode',
  //     editorWidth: 0,
  //     fakeable: false,
  //     displayInBox: false,
  //     color: '#1EC78B',
  //     icon: '#iconyonghuzhongxin-1',
  //     shape: 'CIRCLE',
  //     orders: 2,
  //     joinType: 'END',
  //     status: 'NORMAL',
  //     nodeId: 14,
  //     fatherIds: [13],
  //     childrenIds: [],
  //     branchIndex: 1,
  //     x: 3,
  //     y: 4,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'ExitHelperNode',
  //       isValid: false,
  //       isInit: false,
  //       hasData: false
  //     }
  //   },
  //   {
  //     id: 78,
  //     name: '退出节点',
  //     displayName: '退出节点',
  //     busiType: 'ExitHelperNode',
  //     editorWidth: 0,
  //     fakeable: false,
  //     displayInBox: false,
  //     color: '#1EC78B',
  //     icon: '#iconyonghuzhongxin-1',
  //     shape: 'CIRCLE',
  //     orders: 2,
  //     joinType: 'END',
  //     status: 'NORMAL',
  //     nodeId: 15,
  //     fatherIds: [24],
  //     childrenIds: [],
  //     branchIndex: 0,
  //     x: 7,
  //     y: 5,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'ExitHelperNode',
  //       isValid: false,
  //       isInit: false,
  //       hasData: false
  //     }
  //   },
  //   {
  //     id: 67,
  //     name: '用户分群',
  //     displayName: '用户分群',
  //     busiType: 'UserEntryNode',
  //     editorWidth: 0,
  //     fakeable: true,
  //     displayInBox: true,
  //     color: '#1EC78B',
  //     icon: '#iconicon_yonghu_',
  //     shape: 'CIRCLE',
  //     orders: 1,
  //     joinType: 'ENTRY',
  //     status: 'NORMAL',
  //     nodeId: 16,
  //     fatherIds: [],
  //     childrenIds: [20],
  //     branchIndex: 0,
  //     x: 0,
  //     y: 8,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'UserEntryNode',
  //       segmentList: [
  //         {
  //           id: 330,
  //           name: '0310测试复合',
  //           lastCalcTime: 1615342980000,
  //           customerCount: 200,
  //           valid: true
  //         }
  //       ],
  //       whiteSegmentList: [],
  //       blackSegmentList: [
  //         {
  //           id: 329,
  //           name: '0310测试上传',
  //           lastCalcTime: 1615342800000,
  //           customerCount: 1,
  //           valid: true
  //         }
  //       ],
  //       all: false,
  //       isValid: true,
  //       isInit: true,
  //       hasData: true
  //     }
  //   },
  //   {
  //     id: 75,
  //     name: '随机人群分支',
  //     displayName: '随机人群分支',
  //     busiType: 'RandomUserSplitNode',
  //     editorWidth: 0,
  //     fakeable: true,
  //     displayInBox: true,
  //     color: '#FCA400',
  //     icon: '#iconicon_shaixuan',
  //     shape: 'DIAMOND',
  //     orders: 3,
  //     joinType: 'SPLIT',
  //     status: 'NORMAL',
  //     nodeId: 18,
  //     fatherIds: [23],
  //     childrenIds: [24, 19],
  //     branchIndex: 0,
  //     x: 5,
  //     y: 5,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'RandomUserSplitNode',
  //       branchList: [
  //         {
  //           branchName: 'A分组',
  //           inCounts: 0,
  //           branchPercent: 50
  //         },
  //         {
  //           branchName: 'B分组',
  //           inCounts: 0,
  //           branchPercent: 50
  //         }
  //       ],
  //       avg: false,
  //       isValid: true,
  //       isInit: true,
  //       hasData: true
  //     }
  //   },
  //   {
  //     id: 78,
  //     name: '退出节点',
  //     displayName: '退出节点',
  //     busiType: 'ExitHelperNode',
  //     editorWidth: 0,
  //     fakeable: false,
  //     displayInBox: false,
  //     color: '#1EC78B',
  //     icon: '#iconyonghuzhongxin-1',
  //     shape: 'CIRCLE',
  //     orders: 2,
  //     joinType: 'END',
  //     status: 'NORMAL',
  //     nodeId: 19,
  //     fatherIds: [18],
  //     childrenIds: [],
  //     branchIndex: 1,
  //     x: 6,
  //     y: 7,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'ExitHelperNode',
  //       isValid: false,
  //       isInit: false,
  //       hasData: false
  //     }
  //   },
  //   {
  //     id: 76,
  //     name: '合并分支',
  //     displayName: '合并分支',
  //     busiType: 'JoinNode',
  //     editorWidth: 0,
  //     fakeable: false,
  //     displayInBox: true,
  //     color: '#FCA400',
  //     icon: '#icongaibanxianxingtubiao-',
  //     shape: 'DIAMOND',
  //     orders: 1,
  //     joinType: 'JOIN',
  //     status: 'NORMAL',
  //     nodeId: 20,
  //     fatherIds: [16],
  //     childrenIds: [23],
  //     branchIndex: 0,
  //     x: 1,
  //     y: 8,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'JoinNode',
  //       isValid: false,
  //       isInit: false,
  //       hasData: false
  //     }
  //   },
  //   {
  //     id: 69,
  //     name: '发送短信',
  //     displayName: '发送短信',
  //     busiType: 'SMSPushNode',
  //     editorWidth: 0,
  //     fakeable: true,
  //     displayInBox: true,
  //     color: '#0099FF',
  //     icon: '#iconicon_shouji',
  //     shape: 'SQUARE',
  //     orders: 7,
  //     joinType: 'FUNCTION',
  //     status: 'NORMAL',
  //     nodeId: 22,
  //     fatherIds: [13],
  //     childrenIds: [23],
  //     branchIndex: 2,
  //     x: 3,
  //     y: 5,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'SMSPushNode',
  //       isValid: false,
  //       isInit: false,
  //       hasData: false
  //     }
  //   },
  //   {
  //     id: 77,
  //     name: '合并分支1',
  //     displayName: '合并分支1',
  //     busiType: 'JoinHelperNode',
  //     editorWidth: 0,
  //     fakeable: false,
  //     displayInBox: false,
  //     color: '#FCA400',
  //     icon: '#iconicon_xiaochengxu',
  //     shape: 'LIT_DIAMOND',
  //     orders: 1,
  //     joinType: 'FUNCTION',
  //     status: 'NORMAL',
  //     nodeId: 23,
  //     fatherIds: [22, 20],
  //     childrenIds: [18],
  //     branchIndex: 0,
  //     x: 4,
  //     y: 5,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'JoinHelperNode',
  //       isValid: false,
  //       isInit: false,
  //       hasData: false
  //     }
  //   },
  //   {
  //     id: 74,
  //     name: '人群条件分支',
  //     displayName: '人群条件分支',
  //     busiType: 'UserFilterSplitNode',
  //     editorWidth: 0,
  //     fakeable: true,
  //     displayInBox: true,
  //     color: '#FCA400',
  //     icon: '#iconicon_shaixuan',
  //     shape: 'DIAMOND',
  //     orders: 2,
  //     joinType: 'SPLIT',
  //     status: 'NORMAL',
  //     nodeId: 24,
  //     fatherIds: [18],
  //     childrenIds: [15, 25],
  //     branchIndex: 0,
  //     x: 6,
  //     y: 5,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'UserFilterSplitNode',
  //       branchList: [
  //         {
  //           branchName: '1',
  //           inCounts: 0,
  //           filter: {
  //             connector: 'AND',
  //             filters: [
  //               {
  //                 connector: 'AND',
  //                 filters: [
  //                   {
  //                     field: 'device_id',
  //                     fieldType: 'STRING',
  //                     operator: 'IS_NOT_NULL',
  //                     tableId: 74,
  //                     schemaId: 404,
  //                     level1: '用户属性',
  //                     level2: '',
  //                     fieldName: 'device_id',
  //                     isEnum: false
  //                   }
  //                 ],
  //                 empty: false
  //               }
  //             ],
  //             empty: false
  //           }
  //         },
  //         {
  //           branchName: '2',
  //           inCounts: 0,
  //           filter: {
  //             connector: 'AND',
  //             filters: [
  //               {
  //                 connector: 'AND',
  //                 filters: [
  //                   {
  //                     field: 'name',
  //                     fieldType: 'STRING',
  //                     operator: 'IS_NULL',
  //                     tableId: 74,
  //                     schemaId: 405,
  //                     level1: '用户属性',
  //                     level2: '',
  //                     fieldName: 'name',
  //                     isEnum: false
  //                   }
  //                 ],
  //                 empty: false
  //               }
  //             ],
  //             empty: false
  //           }
  //         }
  //       ],
  //       isValid: true,
  //       isInit: true,
  //       hasData: true
  //     }
  //   },
  //   {
  //     id: 78,
  //     name: '退出节点',
  //     displayName: '退出节点',
  //     busiType: 'ExitHelperNode',
  //     editorWidth: 0,
  //     fakeable: false,
  //     displayInBox: false,
  //     color: '#1EC78B',
  //     icon: '#iconyonghuzhongxin-1',
  //     shape: 'CIRCLE',
  //     orders: 2,
  //     joinType: 'END',
  //     status: 'NORMAL',
  //     nodeId: 25,
  //     fatherIds: [24],
  //     childrenIds: [],
  //     branchIndex: 1,
  //     x: 7,
  //     y: 6,
  //     retryTime: 0,
  //     inCounts: 0,
  //     holdupCounts: 0,
  //     detail: {
  //       busiType: 'ExitHelperNode',
  //       isValid: false,
  //       isInit: false,
  //       hasData: false
  //     }
  //   }
  // ]
};
