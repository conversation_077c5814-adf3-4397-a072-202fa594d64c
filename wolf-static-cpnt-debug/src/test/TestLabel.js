import React, { useState, useRef, useEffect } from 'react';
import _ from 'lodash';
import { Button, Input } from 'antd';
import { Label, Customize } from 'wolf-static-cpnt';
import 'react-json-pretty/themes/monikai.css';
import JSONPretty from 'react-json-pretty';

const { TextArea } = Input;

const tableId = 1;
const propertyItemList = [{
  tableId,
  schemaId: 1,
  items: ['北京', '上海', '广州']
}, {
  tableId,
  schemaId: 2,
  items: [{ name: '页面浏览', value: 'pageview' }, { name: '登录', value: 'login' }, { name: '注册', value: 'register' }, { name: '点击', value: 'click' }, { name: '购买', value: 'pay' }, { name: '下单', value: 'order' }]
}, {
  tableId,
  schemaId: 4,
  items: [20,80,40]
}

];
const propertyList= [
  {
    tableId,
    schemaId: 1,
    fieldName: '城市',
    field: 'city',
    fieldType: 'HIVE_DATE',
    level1: '',
    level2: '',
    isEnum: true
  },
  {
    tableId,
    schemaId: 1,
    fieldName: '城市城市城市城',
    field: 'city',
    fieldType: 'HIVE_TIMESTAMP',
    level1: 'level-has-level2',
    level2: '',
    isEnum: true
  },
  {
    tableId,
    schemaId: 1,
    fieldName: '城市很长很长很长很长很长很长很长很长很长很长很长',
    field: 'city',
    fieldType: 'STRING',
    level1: 'level-has-level2',
    level2: 'level2-1',
    isEnum: true
  },
  {
    tableId,
    schemaId: 2,
    fieldName: '事件名称',
    field: 'eventName',
    fieldType: 'STRING',
    level1: 'level-has-level2',
    level2: 'level2-1',
    isEnum: true
  },
  {
    tableId,
    schemaId: 3,
    fieldName: '生日时间',
    field: 'birthDay',
    fieldType: 'DATETIME',
    level1: 'level-has-level2',
    level2: 'level2-1'
  },
  {
    tableId,
    schemaId: 3,
    fieldName: '生日',
    field: 'birthDay',
    fieldType: 'DATE',
    level1: 'level-has-level2',
    level2: 'level2-1'
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'BOOL',
    level1: 'level-long',
    level2: ''
  }
];

// const filterValue = {
//   connector: 'AND',
//   filters: [
//     {
//       connector: 'OR',
//       filters: [
//         {
//           tableId,
//           schemaId: 4,
//           fieldName: '年龄',
//           field: 'age',
//           fieldType: 'INT',
//           level1: 'level-has-level2',
//           level2: '',
//           operator: 'EQ',
//           isEnum: true,
//           value: '11'
//         },
//         {
//           tableId,
//           schemaId: 5,
//           fieldName: '城市很长很长很长很长很长很长很长很长很长很长很长',
//           field: 'country',
//           fieldType: 'STRING',
//           level1: 'level-long',
//           level2: '',
//           operator: 'EQ',
//           isEnum: true,
//           value: '北京'
//         }
//       ]
//     },
//     {
//       connector: 'OR',
//       filters: [
//         {
//           tableId,
//           schemaId: 2,
//           fieldName: '事件名称',
//           field: 'eventName',
//           fieldType: 'STRING',
//           level1: 'level-has-level2',
//           level2: 'level2-1',
//           isEnum: true,
//           operator: 'EQ',
//           value: '页面浏览'
//         },
//         {
//           tableId,
//           schemaId: 4,
//           fieldName: '年龄',
//           field: 'age',
//           fieldType: 'INT',
//           level1: 'level-has-level2',
//           level2: '',
//           operator: 'BETWEEN',
//           value: [0, 100]
//         }
//       ]
//     }
//   ]
// };

const filterValue = {
  // filterInfo:{
  //   "connector": "AND",
  //   "filters": [
  //     {
  //       "connector": "AND",
  //       "filters": [
  //         {
  //           "tableId": 1,
  //           "schemaId": 3,
  //           "field": "birthDay",
  //           "fieldName": "生日时间",
  //           "fieldType": "DATETIME",
  //           "level1": "level-has-level2",
  //           "level2": "level2-1",
  //           "operator": "BETWEEN",
  //           "value": [
  //             1610432060231,
  //             1611987260231
  //           ],
  //           "showValue": null
  //         },
  //       ]
  //     },
  //   ]
  // },
  // label:{
    "connector": "AND",
    "filters": [
      {
        "connector": "AND",
        "filters": [
          {
            "id": "6",
            "label": "活跃度1",
            "operator": "BETWEEN",
            "value": [
              1609833121268,
              1611388321268
            ],
            "times": 1609776000000,
            "fieldType": "DATE",
            "dateType": "ABSOLUTE"
          }
        ]
      },
      
    ]
  // },
  // "connector": "AND"
}

const datas = {"header":{"code":0},"body":{"categoryList":[{"createTime":1640938896000,"updateTime":1640938896000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":1,"name":"wdd","type":"FIRST_LEVEL","parentId":0,"code":"0,"},{"createTime":1646297189000,"updateTime":1646297189000,"createUserId":2,"updateUserId":2,"projectId":"Jevf4ghaKT091r5E","id":3,"name":"wdd01","type":"FIRST_LEVEL","parentId":0,"code":"0,"}],"userLabels":[{"createTime":1646362987000,"updateTime":1646362987000,"createUserId":2,"updateUserId":2,"projectId":"Jevf4ghaKT091r5E","id":13,"name":"有效时间更新","displayName":"每天更新","remark":"","type":"SQL_CREATION","subType":"CUSTOM_SQL","validDateType":"TEMPORARY","validBeginTime":1646323200000,"validEndTime":1646755200000,"calcRule":{"sql":"select name as `value`, dt_id from `wolf`.`test_user`"},"lastCalcStatus":"NOTRUN","executeStatus":"NORMAL","scheduleType":"SCHEDULE","scheduleRate":"DAY","scheduleConf":{"days":1,"weekTerm":"MONDAY"},"dataType":"STRING","status":"NORMAL","categoryId":0,"scenario":{"createTime":1640856903000,"updateTime":1640932615000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":2,"name":"场景0","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"order_id"}},{"createTime":1646363414000,"updateTime":1646363913000,"createUserId":2,"updateUserId":2,"projectId":"Jevf4ghaKT091r5E","id":15,"name":"two","displayName":"2","remark":"","type":"SQL_CREATION","subType":"CUSTOM_SQL","validDateType":"TEMPORARY","validBeginTime":1646323200000,"validEndTime":1646496000000,"calcRule":{"sql":"select name as `value`, dt_id from `wolf`.`test_user`"},"lastCalcStatus":"NOTRUN","executeStatus":"NORMAL","scheduleType":"MANUAL","scheduleRate":"DAY","scheduleConf":{"days":1,"weekTerm":"MONDAY"},"dataType":"STRING","status":"NORMAL","categoryId":0,"scenario":{"createTime":1640856903000,"updateTime":1640932615000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":2,"name":"场景0","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"order_id"}},{"createTime":1646373232000,"updateTime":1646373756000,"createUserId":2,"updateUserId":2,"projectId":"Jevf4ghaKT091r5E","id":20,"name":"12","displayName":"34","remark":"1111111111111111111111111111111222222222222222222222222222222222222222222222223333333333333333333333333333333333333333333333344444444444444444444444444444444444455555555555555555566666666666666666677","type":"SQL_CREATION","subType":"CUSTOM_SQL","validDateType":"FOREVER","calcRule":{"sql":"select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10"},"lastCalcStatus":"FAIL","lastCalcTime":1646373303000,"executeStatus":"NORMAL","scheduleType":"MANUAL","scheduleRate":"DAY","scheduleConf":{"days":1,"weekTerm":"MONDAY"},"dataType":"LONG","status":"NORMAL","categoryId":0,"scenario":{"createTime":1640856903000,"updateTime":1640932615000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":2,"name":"场景0","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"order_id"}}]}}
const tags = {"header":{"code":0},"body":[{"createTime":1640932336000,"updateTime":1646363358000,"createUserId":2,"updateUserId":2,"createUserName":"超级管理员","updateUserName":"超级管理员","projectId":"Jevf4ghaKT091r5E","id":1,"name":"手动更新","displayName":"手动更新01","remark":" ","type":"SQL_CREATION","subType":"CUSTOM_SQL","validDateType":"FOREVER","calcRule":{"sql":"select name as `value`, dt_id from `wolf`.`test_user`"},"lastCalcStatus":"SUC","lastCalcTime":1646363127000,"executeStatus":"NORMAL","scheduleType":"MANUAL","scheduleRate":"DAY","scheduleConf":{"days":1,"weekTerm":"MONDAY"},"dataType":"STRING","status":"NORMAL","categoryId":1,"scenario":{"createTime":1640856903000,"updateTime":1640932615000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":2,"name":"场景0","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"order_id"},"labelCategories":"/wdd/"},{"createTime":1642496367000,"updateTime":1646363358000,"createUserId":2,"updateUserId":2,"createUserName":"超级管理员","updateUserName":"超级管理员","projectId":"Jevf4ghaKT091r5E","id":3,"name":"自动更新_时间","displayName":"自动更新_时间","remark":" ","type":"SQL_CREATION","subType":"CUSTOM_SQL","validDateType":"FOREVER","calcRule":{"sql":"select birthday as `value`, dt_id from `wolf`.`test_user`"},"lastCalcStatus":"SUC","lastCalcTime":1646337907000,"executeStatus":"NORMAL","scheduleType":"SCHEDULE","scheduleRate":"DAY","scheduleConf":{"days":1,"weekTerm":"MONDAY"},"dataType":"DATE","status":"NORMAL","categoryId":1,"scenario":{"createTime":1640856903000,"updateTime":1640932615000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":2,"name":"场景0","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"order_id"},"labelCategories":"/wdd/"},{"createTime":1644897196000,"updateTime":1646294586000,"createUserId":2,"updateUserId":1,"createUserName":"超级管理员","updateUserName":"admin","projectId":"Jevf4ghaKT091r5E","id":5,"name":"自动标签_TIMESTAMP_2022_02_15","displayName":"自动标签_TIMESTAMP_2022_02_15","type":"EXTERNAL_IMPORT","validDateType":"FOREVER","lastCalcStatus":"NOTRUN","executeStatus":"NORMAL","dataType":"TIMESTAMP","status":"NORMAL","categoryId":1,"scenario":{"createTime":1640856903000,"updateTime":1640932615000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":2,"name":"场景0","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"order_id"},"labelCategories":"/wdd/"},{"createTime":1644897196000,"updateTime":1646294586000,"createUserId":2,"updateUserId":2,"createUserName":"超级管理员","updateUserName":"超级管理员","projectId":"Jevf4ghaKT091r5E","id":6,"name":"自动标签_STRING_2022_02_15","displayName":"自动标签_STRING_2022_02_15","type":"EXTERNAL_IMPORT","validDateType":"FOREVER","dataType":"STRING","categoryId":1,"scenario":{"createTime":1640856903000,"updateTime":1640932615000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":2,"name":"场景0","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"order_id"},"labelCategories":"/wdd/"},{"createTime":1644897196000,"updateTime":1646294586000,"createUserId":2,"updateUserId":1,"createUserName":"超级管理员","updateUserName":"admin","projectId":"Jevf4ghaKT091r5E","id":7,"name":"自动标签_LONG_2022_02_15","displayName":"自动标签_LONG_2022_02_15","type":"EXTERNAL_IMPORT","validDateType":"FOREVER","lastCalcStatus":"NOTRUN","executeStatus":"NORMAL","dataType":"LONG","status":"NORMAL","categoryId":1,"scenario":{"createTime":1640856903000,"updateTime":1640932615000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":2,"name":"场景0","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"order_id"},"labelCategories":"/wdd/"},{"createTime":1646295196000,"updateTime":1646362802000,"createUserId":2,"updateUserId":2,"createUserName":"超级管理员","updateUserName":"超级管理员","projectId":"Jevf4ghaKT091r5E","id":9,"name":"test01_Qq你好","displayName":"test01_Qq你好11111111111111111111111111111111111111111111111","remark":"11111111111111111111111111111111111111111111111111","type":"SQL_CREATION","subType":"CUSTOM_SQL","validDateType":"FOREVER","calcRule":{"sql":"select name as `value`, dt_id from `wolf`.`test_user`"},"lastCalcStatus":"SUC","lastCalcTime":1646296601000,"executeStatus":"NORMAL","scheduleType":"MANUAL","scheduleRate":"DAY","scheduleConf":{"days":1,"weekTerm":"MONDAY"},"dataType":"STRING","status":"NORMAL","categoryId":3,"scenario":{"createTime":1640856903000,"updateTime":1640932615000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":2,"name":"场景0","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"order_id"},"labelCategories":"/wdd01/"},{"createTime":1646295460000,"updateTime":1646362802000,"createUserId":2,"updateUserId":1,"createUserName":"超级管理员","updateUserName":"admin","projectId":"Jevf4ghaKT091r5E","id":10,"name":"test01_Qq你好会好失手i从埃松加哦i923434590UOIEUFIOIDVHOI电厂征地v回到UDsfds","displayName":"test01_Qq你好会好失手i从123","remark":"test01_Qq你好会好失手i从埃松加哦i923434590UOIEUFIOIDVHOI电厂征地v回到UDsfdstest01_Qq你好会好失手i从埃松加哦i923434590UOIEUFIOIDVHOI电厂征地v回到UDsfds","type":"SQL_CREATION","subType":"CUSTOM_SQL","validDateType":"TEMPORARY","validBeginTime":1646236800000,"validEndTime":1646323200000,"calcRule":{"sql":"select name as `value`, dt_id from `wolf`.`test_user`"},"lastCalcStatus":"NOTRUN","executeStatus":"NORMAL","scheduleType":"SCHEDULE","scheduleRate":"DAY","scheduleConf":{"days":1,"weekTerm":"MONDAY"},"dataType":"STRING","status":"NORMAL","categoryId":3,"scenario":{"createTime":1640856903000,"updateTime":1640932615000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":2,"name":"场景0","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"order_id"},"labelCategories":"/wdd01/"},{"createTime":1646296777000,"updateTime":1646362802000,"createUserId":2,"updateUserId":2,"createUserName":"超级管理员","updateUserName":"超级管理员","projectId":"Jevf4ghaKT091r5E","id":11,"name":"123","displayName":"123","remark":" ","type":"SQL_CREATION","subType":"CUSTOM_SQL","validDateType":"TEMPORARY","validBeginTime":1646236800000,"validEndTime":1646323200000,"calcRule":{"sql":"select name as `value`, dt_id from `wolf`.`test_user`"},"lastCalcStatus":"SUC","lastCalcTime":1646297264000,"executeStatus":"NORMAL","scheduleType":"MANUAL","scheduleRate":"DAY","scheduleConf":{"days":1,"weekTerm":"MONDAY"},"dataType":"STRING","status":"NORMAL","categoryId":3,"scenario":{"createTime":1640856903000,"updateTime":1640932615000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":2,"name":"场景0","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"order_id"},"labelCategories":"/wdd01/"},{"createTime":1646297080000,"updateTime":1646363363000,"createUserId":2,"updateUserId":2,"createUserName":"超级管理员","updateUserName":"超级管理员","projectId":"Jevf4ghaKT091r5E","id":12,"name":"Automatic","displayName":"每日更新","remark":" ","type":"SQL_CREATION","subType":"CUSTOM_SQL","validDateType":"FOREVER","calcRule":{"sql":"select name as `value`, dt_id from `wolf`.`test_user`"},"lastCalcStatus":"SUC","lastCalcTime":1646337909000,"executeStatus":"NORMAL","scheduleType":"SCHEDULE","scheduleRate":"DAY","scheduleConf":{"days":1,"weekTerm":"MONDAY"},"dataType":"STRING","status":"NORMAL","categoryId":3,"scenario":{"createTime":1640856903000,"updateTime":1640932615000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":2,"name":"场景0","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"order_id"},"labelCategories":"/wdd01/"},{"createTime":1646362987000,"updateTime":1646362987000,"createUserId":2,"updateUserId":2,"createUserName":"超级管理员","updateUserName":"超级管理员","projectId":"Jevf4ghaKT091r5E","id":13,"name":"有效时间更新","displayName":"每天更新","remark":"","type":"SQL_CREATION","subType":"CUSTOM_SQL","validDateType":"TEMPORARY","validBeginTime":1646323200000,"validEndTime":1646755200000,"calcRule":{"sql":"select name as `value`, dt_id from `wolf`.`test_user`"},"lastCalcStatus":"NOTRUN","executeStatus":"NORMAL","scheduleType":"SCHEDULE","scheduleRate":"DAY","scheduleConf":{"days":1,"weekTerm":"MONDAY"},"dataType":"STRING","status":"NORMAL","categoryId":0,"scenario":{"createTime":1640856903000,"updateTime":1640932615000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":2,"name":"场景0","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"order_id"}},{"createTime":1646363414000,"updateTime":1646363913000,"createUserId":2,"updateUserId":2,"createUserName":"超级管理员","updateUserName":"超级管理员","projectId":"Jevf4ghaKT091r5E","id":15,"name":"two","displayName":"2","remark":"","type":"SQL_CREATION","subType":"CUSTOM_SQL","validDateType":"TEMPORARY","validBeginTime":1646323200000,"validEndTime":1646496000000,"calcRule":{"sql":"select name as `value`, dt_id from `wolf`.`test_user`"},"lastCalcStatus":"NOTRUN","executeStatus":"NORMAL","scheduleType":"MANUAL","scheduleRate":"DAY","scheduleConf":{"days":1,"weekTerm":"MONDAY"},"dataType":"STRING","status":"NORMAL","categoryId":0,"scenario":{"createTime":1640856903000,"updateTime":1640932615000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":2,"name":"场景0","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"order_id"}},{"createTime":1646373232000,"updateTime":1646373756000,"createUserId":2,"updateUserId":2,"createUserName":"超级管理员","updateUserName":"超级管理员","projectId":"Jevf4ghaKT091r5E","id":20,"name":"12","displayName":"34","remark":"1111111111111111111111111111111222222222222222222222222222222222222222222222223333333333333333333333333333333333333333333333344444444444444444444444444444444444455555555555555555566666666666666666677","type":"SQL_CREATION","subType":"CUSTOM_SQL","validDateType":"FOREVER","calcRule":{"sql":"select city as `value`, device_id as `dt_id` from `wolf`.`user_sample` limit 10"},"lastCalcStatus":"FAIL","lastCalcTime":1646373303000,"executeStatus":"NORMAL","scheduleType":"MANUAL","scheduleRate":"DAY","scheduleConf":{"days":1,"weekTerm":"MONDAY"},"dataType":"LONG","status":"NORMAL","categoryId":0,"scenario":{"createTime":1640856903000,"updateTime":1640932615000,"createUserId":1,"updateUserId":1,"projectId":"Jevf4ghaKT091r5E","id":2,"name":"场景0","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"order_id"}}]}
const TestFilter = () => {
  const [value, setValue] = useState({});
  const [mode, setMode] = useState('edit');
  const [jsonData, setJsonData] = useState('{}');
  const ref = useRef(null);

  const dataProvider = {
    getPropertyList: (name) => {
      console.log('fetch propertyList 1', name)
      return propertyList;
    },
    getPropertyEnumList: (tableId, schemaId) => {
      const propertyItem = _.find(propertyItemList, (v) => v.tableId === tableId && v.schemaId === schemaId);
      return propertyItem ? propertyItem.items : [];
    },
    getTagList: async (data) => tags.body,
    findAllCategory: () => datas.body,
    findCategoryByProjectId: () => [],
    getTagValuesById: (id) => datas.body.userLabels.find(n => n.id == id).userLabelValues
  };

  const onChange = (value) => {
    setValue(value);
  };

  const onSubmit = () => {
    if (ref.current) {
      console.log(ref.current.isValid(), 'isValid----------------------------------');
    }
  };

  useEffect(() => {
    setJsonData(JSON.stringify(value));
  }, [value])

  return (
    <div>
      <div style={{ margin: 20, width: 1360, padding: 20, border: '1px solid #000', display: 'inline-block' }} >
        <Label showInitLine={false} dataProvider={dataProvider} hideInit value={value} onChange={onChange} mode={mode} ref={ref} />
        <Button onClick={() => setValue({})}>清空</Button>
        <Button onClick={() => setMode(mode === 'edit' ? 'detail' : 'edit')}>
          只读切换
        </Button>
        <Button onClick={() => onSubmit()}>submit</Button>
        <hr />
        <div>
          <JSONPretty data={value} />
        </div>
      </div>
      <div style={{ display: 'inline-block', width: 500 }}>
        <Button onClick={() => setValue(JSON.parse(jsonData))}>Submit Data</Button>
        <TextArea value={jsonData} rows={30} onChange={e => setJsonData(e.target.value)} />
      </div>
    </div>
  );
};

export default TestFilter;
