import React, { useState, useRef, useEffect } from 'react';
import _ from 'lodash';
import { Button, Input } from 'antd';
import {  Segment } from 'wolf-static-cpnt';
import 'react-json-pretty/themes/monikai.css';
import J<PERSON><PERSON>retty from 'react-json-pretty';

const { TextArea } = Input;

const filterInfo = {"connector":"AND","filters":[{"connector":"AND","filters":[{"type":"INCLUDE","segment":{"id":915,"name":"0331测试上传","lastCalcTime":1617181260000,"customerCount":1}}]}]}

const datas = {"header":{"code":0},"body":[{"createTime":1617002339000,"updateTime":1630656216000,"createUserId":2,"updateUserId":2,"createUserName":"analyzer","updateUserName":"analyzer","projectId":"qvAD1jk8q0hA0Oxm","id":873,"name":"0329","customerCount":265,"status":"NORMAL","calcStatus":"SUC","calcMemo":"","type":"CONDITIONAL","lastCalcTime":1630656180000,"calcRule":"ONCE","scheduleConf":{"calcRule":"ONCE"},"connector":"AND","filterInfo":{"connector":"AND","filters":[{"connector":"AND","filters":[{"field":"name","fieldType":"STRING","operator":"IN","value":["王峰"],"tableId":62,"schemaId":374,"level1":"用户属性","level2":"","fieldName":"姓名","isEnum":false}],"empty":false}]},"label":{"filters":[],"empty":true},"display":true,"scenario":{"createTime":1603944027000,"updateTime":1608203693000,"createUserId":1,"updateUserId":1,"projectId":"qvAD1jk8q0hA0Oxm","id":14,"name":"女性场景","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"user_id"},"whetherTest":true},{"createTime":1617181139000,"updateTime":1617181279000,"createUserId":2,"updateUserId":2,"createUserName":"analyzer","updateUserName":"analyzer","projectId":"qvAD1jk8q0hA0Oxm","id":915,"name":"0331测试上传","customerCount":1,"status":"NORMAL","calcStatus":"SUC","calcMemo":"","type":"UPLOAD","lastCalcTime":1617181260000,"calcRule":"ONCE","scheduleConf":{"calcRule":"ONCE"},"display":true,"uploadPath":"tmp/185391af-039e-4bc4-b089-29f4466b1951.csv","scenario":{"createTime":1603944027000,"updateTime":1608203693000,"createUserId":1,"updateUserId":1,"projectId":"qvAD1jk8q0hA0Oxm","id":14,"name":"女性场景","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"user_id"},"whetherTest":true},{"createTime":1617181159000,"updateTime":1617181459000,"createUserId":2,"updateUserId":2,"createUserName":"analyzer","updateUserName":"analyzer","projectId":"qvAD1jk8q0hA0Oxm","id":916,"name":"0331测试复合","customerCount":7742,"status":"NORMAL","calcStatus":"SUC","calcMemo":"","type":"COMPLEX","lastCalcTime":1617181440000,"calcRule":"ONCE","scheduleConf":{"calcRule":"ONCE"},"display":true,"limits":0,"includeSegments":{"connector":"OR","filters":[{"id":914,"name":"0331测试自定义","lastCalcTime":1617180900000,"filter":{"connector":"AND","filters":[]},"customerCount":7742,"valid":true},{"id":867,"name":"复合分群","lastCalcTime":1616493360000,"filter":{"connector":"AND","filters":[]},"customerCount":278,"valid":true}]},"excludeSegments":{"connector":"OR","filters":[]},"scenario":{"createTime":1603944027000,"updateTime":1608203693000,"createUserId":1,"updateUserId":1,"projectId":"qvAD1jk8q0hA0Oxm","id":14,"name":"女性场景","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"user_id"},"whetherTest":true},{"createTime":1617180801000,"updateTime":1617180913000,"createUserId":2,"updateUserId":2,"createUserName":"analyzer","updateUserName":"analyzer","projectId":"qvAD1jk8q0hA0Oxm","id":914,"name":"0331测试自定义","customerCount":7742,"status":"NORMAL","calcStatus":"SUC","calcMemo":"","type":"CONDITIONAL","lastCalcTime":1617180900000,"calcRule":"ONCE","scheduleConf":{"calcRule":"ONCE"},"connector":"AND","filterInfo":{"connector":"AND","filters":[{"connector":"AND","filters":[{"field":"name","fieldType":"STRING","operator":"END_WITH","value":"丽","tableId":62,"schemaId":374,"level1":"用户属性","level2":"","fieldName":"姓名","isEnum":false}],"empty":false}]},"label":{"filters":[],"empty":true},"display":true,"scenario":{"createTime":1603944027000,"updateTime":1608203693000,"createUserId":1,"updateUserId":1,"projectId":"qvAD1jk8q0hA0Oxm","id":14,"name":"女性场景","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"user_id"},"whetherTest":true},{"createTime":1626660355000,"updateTime":1626660545000,"createUserId":2,"updateUserId":2,"createUserName":"analyzer","updateUserName":"analyzer","projectId":"qvAD1jk8q0hA0Oxm","id":1064,"name":"0719试","customerCount":265,"status":"NORMAL","calcStatus":"SUC","calcMemo":"","type":"CONDITIONAL","lastCalcTime":1626660540000,"calcRule":"ONCE","scheduleConf":{"calcRule":"ONCE"},"connector":"AND","filterInfo":{"connector":"AND","filters":[{"connector":"AND","filters":[{"field":"name","fieldType":"STRING","operator":"LIKE","value":"王峰","tableId":62,"schemaId":374,"level1":"用户属性","level2":"","fieldName":"姓名","isEnum":false}],"empty":false}]},"label":{"filters":[],"empty":true},"display":true,"scenario":{"createTime":1603944027000,"updateTime":1608203693000,"createUserId":1,"updateUserId":1,"projectId":"qvAD1jk8q0hA0Oxm","id":14,"name":"女性场景","code":"0","joinEventColumnName":"userId","joinOrderColumnName":"user_id"},"whetherTest":true}]}
const TestEventFilter = () => {
  const [value, setValue] = useState(filterInfo);
  const [mode, setMode] = useState('edit');
  const [jsonData, setJsonData] = useState('{}');
  const ref = useRef(null);

  const dataProvider = {

    getSegmentList: () => datas.body
  };

  const onChange = (value) => {
    console.log('TestEventFilter', value);
    setValue(value);
  };

  const onSubmit = () => {
    if (ref.current) {
      console.log(ref.current.isValid(), 'isValid----------------------------------');
    }
  };

  useEffect(() => {
    setJsonData(JSON.stringify(value));
  }, [value])


  return (
    <div>
      <div style={{ margin: 20, width: 1360, padding: 20, border: '1px solid #000', display: 'inline-block' }} >
        <Segment dataProvider={dataProvider} showInitLine value={value} onChange={onChange} mode={mode} ref={ref} />
        <Button onClick={() => setValue({})}>清空</Button>
        <Button onClick={() => setMode(mode === 'edit' ? 'detail' : 'edit')}>
          只读切换
        </Button>
        <Button onClick={() => onSubmit()}>submit</Button>
        <hr />
        <div>
          <JSONPretty data={value} />
        </div>
      </div>
      <div style={{ display: 'inline-block', width: 500 }}>
        <Button onClick={() => setValue(JSON.parse(jsonData))}>Submit Data</Button>
        <TextArea value={jsonData} rows={30} onChange={e => setJsonData(e.target.value)} />
      </div>
    </div>
  );
};

export default TestEventFilter;
