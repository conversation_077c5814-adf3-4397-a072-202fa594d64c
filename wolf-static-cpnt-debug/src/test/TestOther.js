import React, { useState } from 'react';
import dayjs from 'dayjs';
import { Input } from 'antd';
import 'dayjs/locale/zh-cn';

dayjs.locale('zh-cn');

export default function TestOther() {
    const [value, setValue] = useState(null);
    const [count, setCount] = useState(0);
    const onChange = (e) => {
        setValue(e.target.value);
        setCount((data) => data + 1);
        setCount((data) => data + 1);
    };

    // console.log(`render${count}次`);
    return <div style={{ padding: 100 }}>
      {console.log('render')}
      <Input
        value={value}
        style={{ borderColor: 'red' }}
        onChange={onChange}
      />
      <div>{count}</div>
      <TestC />
    </div>;
}

const TestC = (props) => {
    console.log('Rendering TestC :', props);
    return (
      <div>
        { props.count }
      </div>
    );
};
// TestC = React.memo(TestC);
