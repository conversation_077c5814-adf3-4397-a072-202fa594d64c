import React, { useState, useRef, useEffect } from 'react';
import _ from 'lodash';
import { Button, Input } from 'antd';
import { Filter } from 'wolf-static-cpnt';
import 'react-json-pretty/themes/monikai.css';
import JSONPretty from 'react-json-pretty';

const { TextArea } = Input;

const tableId = 1;
const propertyItemList = [{
  tableId,
  schemaId: 1,
  items: ['北京', '上海', '广州']
}, {
  tableId,
  schemaId: 2,
  items: [{ name: '页面浏览', value: 'pageview' }, { name: '登录', value: 'login' }, { name: '注册', value: 'register' }, { name: '点击', value: 'click' }, { name: '购买', value: 'pay' }, { name: '下单', value: 'order' }]
}, {
  tableId,
  schemaId: 4,
  items: [20,80,40]
}

];
const propertyList= [
  {
    tableId,
    schemaId: 1,
    fieldName: '城市',
    field: 'city',
    fieldType: 'STRING',
    level1: 'ceshi1',
    level2: '',
    isEnum: true
  },
  {
    tableId,
    schemaId: 1,
    fieldName: '城市111',
    field: 'city',
    fieldType: 'STRING',
    level1: '',
    level2: '',
    isEnum: true
  },
  {
    tableId,
    schemaId: 1,
    fieldName: '城市城市城市城',
    field: 'city',
    fieldType: 'STRING',
    level1: 'level-has-level2',
    level2: '',
    isEnum: true
  },
  {
    tableId,
    schemaId: 1,
    fieldName: '城市很长很长很长很长很长很长很长很长很长很长很长',
    field: 'city',
    fieldType: 'STRING',
    level1: 'level-has-level2',
    level2: 'level2-1',
    isEnum: true
  },
  {
    tableId,
    schemaId: 2,
    fieldName: '事件名称',
    field: 'eventName',
    fieldType: 'STRING',
    level1: 'level-has-level2',
    level2: 'level2-1',
    isEnum: true
  },
  {
    tableId,
    schemaId: 3,
    fieldName: '生日时间',
    field: 'birthDay',
    fieldType: 'DATETIME',
    level1: 'level-has-level2',
    level2: 'level2-1'
  },
  {
    tableId,
    schemaId: 3,
    fieldName: '生日',
    field: 'birthDay',
    fieldType: 'DATE',
    level1: 'level-has-level2',
    level2: 'level2-1'
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  }
];

const filterValue = {
  connector: 'AND',
  filters: [
    {
      connector: 'OR',
      filters: [
        {
          tableId,
          schemaId: 4,
          fieldName: '年龄',
          field: 'age',
          fieldType: 'INT',
          level1: 'level-has-level2',
          level2: '',
          operator: 'EQ',
          isEnum: true,
          value: '11'
        },
        {
          tableId,
          schemaId: 5,
          fieldName: '城市很长很长很长很长很长很长很长很长很长很长很长',
          field: 'country',
          fieldType: 'STRING',
          level1: 'level-long',
          level2: '',
          operator: 'EQ',
          isEnum: true,
          value: '北京'
        }
      ]
    },
    {
      connector: 'OR',
      filters: [
        {
          tableId,
          schemaId: 2,
          fieldName: '事件名称',
          field: 'eventName',
          fieldType: 'STRING',
          level1: 'level-has-level2',
          level2: 'level2-1',
          isEnum: true,
          operator: 'EQ',
          value: '页面浏览'
        },
        {
          tableId,
          schemaId: 4,
          fieldName: '年龄',
          field: 'age',
          fieldType: 'INT',
          level1: 'level-has-level2',
          level2: '',
          operator: 'BETWEEN',
          value: [0, 100]
        }
      ]
    }
  ]
};

const TestFilter = () => {
  const [value, setValue] = useState(filterValue);
  // const [value, setValue] = useState(null);
  const [mode, setMode] = useState('edit');
  const [jsonData, setJsonData] = useState('{}');
  const ref = useRef(null);

  const dataProvider = {
    getPropertyList: (name) => {
      console.log('fetch propertyList 1', name)
      return propertyList;
    },
    getPropertyEnumList: (tableId, schemaId) => {
      const propertyItem = _.find(propertyItemList, (v) => v.tableId === tableId && v.schemaId === schemaId);
      return propertyItem ? propertyItem.items : [];
    }
  };

  const onChange = (value) => {
    setValue(value);
  };

  const onSubmit = () => {
    if (ref.current) {
      console.log(ref.current.isValid(), 'isValid----------------------------------');
    }
  };

  useEffect(() => {
    setJsonData(JSON.stringify(value));
  }, [value])

  return (
    <div>
      <div style={{ margin: 20, width: 900, padding: 20, border: '1px solid #000', display: 'inline-block' }} >
        <Filter hideInit dataProvider={dataProvider} value={value} onChange={onChange} mode={mode} ref={ref} />
        <Button onClick={() => setValue({})}>清空</Button>
        <Button onClick={() => setMode(mode === 'edit' ? 'detail' : 'edit')}>
          只读切换
        </Button>
        <Button onClick={() => onSubmit()}>submit</Button>
        <hr />
        <div>
          <JSONPretty data={value} />
        </div>
      </div>
      <div style={{ display: 'inline-block', width: 500 }}>
        <Button onClick={() => setValue(JSON.parse(jsonData))}>Submit Data</Button>
        <TextArea value={jsonData} rows={30} onChange={e => setJsonData(e.target.value)} />
      </div>
    </div>
  );
};

export default TestFilter;
