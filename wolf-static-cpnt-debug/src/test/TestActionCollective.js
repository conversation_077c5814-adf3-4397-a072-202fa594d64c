import React, { useState, useRef, useEffect, useCallback } from 'react';
import _ from 'lodash';
import { Button, Input } from 'antd';
import {
  Label,
  Customize,
  EventFilter,
  ActionCollective
} from 'wolf-static-cpnt';
import 'react-json-pretty/themes/monikai.css';
import JSONPretty from 'react-json-pretty';

const { TextArea } = Input;

const tableId = 1;
const propertyItemList = [
  {
    tableId,
    schemaId: 1,
    items: ['北京', '上海', '广州']
  },
  {
    tableId,
    schemaId: 2,
    items: [
      { name: '页面浏览', value: 'pageview' },
      { name: '登录', value: 'login' },
      { name: '注册', value: 'register' },
      { name: '点击', value: 'click' },
      { name: '购买', value: 'pay' },
      { name: '下单', value: 'order' }
    ]
  },
  {
    tableId,
    schemaId: 4,
    items: [20, 80, 40]
  }
];
const propertyList = [
  {
    tableId,
    schemaId: 1,
    fieldName: '城市',
    field: 'city',
    fieldType: 'HIVE_DATE',
    level1: '',
    level2: '',
    isEnum: true
  },
  {
    tableId,
    schemaId: 1,
    fieldName: '城市城市城市城',
    field: 'city',
    fieldType: 'HIVE_TIMESTAMP',
    level1: 'level-has-level2',
    level2: '',
    isEnum: true
  },
  {
    tableId,
    schemaId: 1,
    fieldName: '城市很长很长很长很长很长很长很长很长很长很长很长',
    field: 'city',
    fieldType: 'STRING',
    level1: 'level-has-level2',
    level2: 'level2-1',
    isEnum: true
  },
  {
    tableId,
    schemaId: 2,
    fieldName: '事件名称',
    field: 'eventName',
    fieldType: 'STRING',
    level1: 'level-has-level2',
    level2: 'level2-1',
    isEnum: true
  },
  {
    tableId,
    schemaId: 3,
    fieldName: '生日时间',
    field: 'birthDay',
    fieldType: 'DATETIME',
    level1: 'level-has-level2',
    level2: 'level2-1'
  },
  {
    tableId,
    schemaId: 3,
    fieldName: '生日',
    field: 'birthDay',
    fieldType: 'DATE',
    level1: 'level-has-level2',
    level2: 'level2-1'
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'STRING',
    level1: 'level-long',
    level2: ''
  },
  {
    tableId,
    schemaId: 4,
    fieldName: '年龄',
    field: 'age',
    fieldType: 'INT',
    level1: 'level-has-level2',
    level2: ''
  },
  {
    tableId,
    schemaId: 5,
    fieldName: '国家',
    field: 'country',
    fieldType: 'BOOL',
    level1: 'level-long',
    level2: ''
  }
];

// const filterValue = {
//   connector: 'AND',
//   filters: [
//     {
//       connector: 'OR',
//       filters: [
//         {
//           tableId,
//           schemaId: 4,
//           fieldName: '年龄',
//           field: 'age',
//           fieldType: 'INT',
//           level1: 'level-has-level2',
//           level2: '',
//           operator: 'EQ',
//           isEnum: true,
//           value: '11'
//         },
//         {
//           tableId,
//           schemaId: 5,
//           fieldName: '城市很长很长很长很长很长很长很长很长很长很长很长',
//           field: 'country',
//           fieldType: 'STRING',
//           level1: 'level-long',
//           level2: '',
//           operator: 'EQ',
//           isEnum: true,
//           value: '北京'
//         }
//       ]
//     },
//     {
//       connector: 'OR',
//       filters: [
//         {
//           tableId,
//           schemaId: 2,
//           fieldName: '事件名称',
//           field: 'eventName',
//           fieldType: 'STRING',
//           level1: 'level-has-level2',
//           level2: 'level2-1',
//           isEnum: true,
//           operator: 'EQ',
//           value: '页面浏览'
//         },
//         {
//           tableId,
//           schemaId: 4,
//           fieldName: '年龄',
//           field: 'age',
//           fieldType: 'INT',
//           level1: 'level-has-level2',
//           level2: '',
//           operator: 'BETWEEN',
//           value: [0, 100]
//         }
//       ]
//     }
//   ]
// };
const filterValue = {
  connector: 'AND',
  filters: [
    {
      connector: 'OR',
      userProperty: {
        connector: 'OR',
        filters: [
          {
            connector: 'AND',
            filters: [
              {
                field: 'city',
                fieldType: 'STRING',
                operator: 'EQ',
                value: 'bj',
                tableId: 200,
                isEnum: false
              }
            ],
            empty: false
          }
        ],
        empty: false
      },
      eventGroup: {
        connector: 'AND',
        filters: [
          {
            connector: 'AND',
            filters: [
              {
                action: 'DONE',
                eventInfo: {
                  id: 100,
                  eventType: 'BURIED_POINT_EVENT',
                  displayName: '事件0',
                  eventNameValue: 'nameValue0'
                },
                dateRange: [
                  { type: 'RELATIVE', times: 4, timeTerm: 'DAY', isPast: true },
                  { type: 'NOW', times: 0, isPast: false }
                ],
                eventFiilterProperty: {
                  connector: 'AND',
                  filters: [
                    {
                      connector: 'AND',
                      filters: [
                        {
                          field: 'email',
                          fieldType: 'STRING',
                          operator: 'EQ',
                          value: '<EMAIL>',
                          tableId: 200,
                          isEnum: false
                        }
                      ],
                      empty: false
                    }
                  ],
                  empty: false
                }
              },
              {
                action: 'NOT_DO',
                eventInfo: {
                  id: 101,
                  eventType: 'BURIED_POINT_EVENT',
                  displayName: '事件1',
                  eventNameValue: 'nameValue1'
                },
                dateRange: [
                  { type: 'RELATIVE', times: 4, timeTerm: 'DAY', isPast: true },
                  { type: 'NOW', times: 0, isPast: false }
                ],
                eventFiilterProperty: {
                  connector: 'AND',
                  filters: [
                    {
                      connector: 'AND',
                      filters: [
                        {
                          field: 'email',
                          fieldType: 'STRING',
                          operator: 'EQ',
                          value: '<EMAIL>',
                          tableId: 200,
                          isEnum: false
                        }
                      ],
                      empty: false
                    }
                  ],
                  empty: false
                }
              }
            ]
          }
        ]
      }
    },
    {
      connector: 'OR',
      userProperty: {
        connector: 'OR',
        filters: [
          {
            connector: 'AND',
            filters: [
              {
                field: 'city',
                fieldType: 'STRING',
                operator: 'EQ',
                value: 'bj',
                tableId: 200,
                isEnum: false
              }
            ],
            empty: false
          }
        ],
        empty: false
      },
      eventGroup: {
        connector: 'AND',
        filters: [
          {
            connector: 'AND',
            filters: [
              {
                action: 'DONE',
                eventInfo: {
                  id: 100,
                  eventType: 'BURIED_POINT_EVENT',
                  displayName: '事件0',
                  eventNameValue: 'nameValue0'
                },
                dateRange: [
                  { type: 'RELATIVE', times: 4, timeTerm: 'DAY', isPast: true },
                  { type: 'NOW', times: 0, isPast: false }
                ],
                eventFiilterProperty: {
                  connector: 'AND',
                  filters: [
                    {
                      connector: 'AND',
                      filters: [
                        {
                          field: 'email',
                          fieldType: 'STRING',
                          operator: 'EQ',
                          value: '<EMAIL>',
                          tableId: 200,
                          isEnum: false
                        }
                      ],
                      empty: false
                    }
                  ],
                  empty: false
                }
              },
              {
                action: 'NOT_DO',
                eventInfo: {
                  id: 101,
                  eventType: 'BURIED_POINT_EVENT',
                  displayName: '事件1',
                  eventNameValue: 'nameValue1'
                },
                dateRange: [
                  { type: 'RELATIVE', times: 4, timeTerm: 'DAY', isPast: true },
                  { type: 'NOW', times: 0, isPast: false }
                ],
                eventFiilterProperty: {
                  connector: 'AND',
                  filters: [
                    {
                      connector: 'AND',
                      filters: [
                        {
                          field: 'email',
                          fieldType: 'STRING',
                          operator: 'EQ',
                          value: '<EMAIL>',
                          tableId: 200,
                          isEnum: false
                        }
                      ],
                      empty: false
                    }
                  ],
                  empty: false
                }
              }
            ]
          }
        ]
      }
    },
    {
      connector: 'OR',
      userProperty: {
        connector: 'OR',
        filters: [
          {
            connector: 'AND',
            filters: [
              {
                field: 'city',
                fieldType: 'STRING',
                operator: 'EQ',
                value: 'bj',
                tableId: 200,
                isEnum: false
              }
            ],
            empty: false
          }
        ],
        empty: false
      },
      eventGroup: {
        connector: 'AND',
        filters: [
          {
            connector: 'AND',
            filters: [
              {
                action: 'DONE',
                eventInfo: {
                  id: 100,
                  eventType: 'BURIED_POINT_EVENT',
                  displayName: '事件0',
                  eventNameValue: 'nameValue0'
                },
                dateRange: [
                  { type: 'RELATIVE', times: 4, timeTerm: 'DAY', isPast: true },
                  { type: 'NOW', times: 0, isPast: false }
                ],
                eventFiilterProperty: {
                  connector: 'AND',
                  filters: [
                    {
                      connector: 'AND',
                      filters: [
                        {
                          field: 'email',
                          fieldType: 'STRING',
                          operator: 'EQ',
                          value: '<EMAIL>',
                          tableId: 200,
                          isEnum: false
                        }
                      ],
                      empty: false
                    }
                  ],
                  empty: false
                }
              },
              {
                action: 'NOT_DO',
                eventInfo: {
                  id: 101,
                  eventType: 'BURIED_POINT_EVENT',
                  displayName: '事件1',
                  eventNameValue: 'nameValue1'
                },
                dateRange: [
                  { type: 'RELATIVE', times: 4, timeTerm: 'DAY', isPast: true },
                  { type: 'NOW', times: 0, isPast: false }
                ],
                eventFiilterProperty: {
                  connector: 'AND',
                  filters: [
                    {
                      connector: 'AND',
                      filters: [
                        {
                          field: 'email',
                          fieldType: 'STRING',
                          operator: 'EQ',
                          value: '<EMAIL>',
                          tableId: 200,
                          isEnum: false
                        }
                      ],
                      empty: false
                    }
                  ],
                  empty: false
                }
              }
            ]
          }
        ]
      }
    }
  ]
};

// todo 以后改为displayName
const datas = {
  header: { code: 0 },
  body: {
    categoryList: [],
    userLabels: [
      {
        createTime: 1598338701000,
        updateTime: 1598957193000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 5,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '消费度1',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598338725000,
            updateTime: 1598428223000,
            createUserId: 2,
            updateUserId: 2,
            id: 4,
            labelId: 5,
            value: '低1'
          },
          {
            createTime: 1598338725000,
            updateTime: 1598428223000,
            createUserId: 2,
            updateUserId: 2,
            id: 5,
            labelId: 5,
            value: '高1'
          },
          {
            createTime: 1598338725000,
            updateTime: 1598428224000,
            createUserId: 2,
            updateUserId: 2,
            id: 6,
            labelId: 5,
            value: '中1'
          }
        ]
      },
      {
        createTime: 1598338725000,
        updateTime: 1598957202000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 6,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '活跃度1',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598338727000,
            updateTime: 1598428225000,
            createUserId: 2,
            updateUserId: 2,
            id: 7,
            labelId: 6,
            value: '低1'
          },
          {
            createTime: 1598338727000,
            updateTime: 1598428225000,
            createUserId: 2,
            updateUserId: 2,
            id: 8,
            labelId: 6,
            value: '高1'
          },
          {
            createTime: 1598344200000,
            updateTime: 1598428226000,
            createUserId: 2,
            updateUserId: 2,
            id: 10,
            labelId: 6,
            value: '中1'
          }
        ]
      },
      {
        createTime: 1598429904000,
        updateTime: 1598957207000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 8,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '活跃度',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598429904000,
            updateTime: 1598429904000,
            createUserId: 2,
            updateUserId: 2,
            id: 11,
            labelId: 8,
            value: '高'
          },
          {
            createTime: 1598429904000,
            updateTime: 1598429904000,
            createUserId: 2,
            updateUserId: 2,
            id: 12,
            labelId: 8,
            value: '中'
          },
          {
            createTime: 1598429904000,
            updateTime: 1598429904000,
            createUserId: 2,
            updateUserId: 2,
            id: 13,
            labelId: 8,
            value: '低'
          }
        ]
      },
      {
        createTime: 1598932608000,
        updateTime: 1598957214000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 11,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '消费度',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598932608000,
            updateTime: 1598932608000,
            createUserId: 2,
            updateUserId: 2,
            id: 14,
            labelId: 11,
            value: '高'
          },
          {
            createTime: 1598932608000,
            updateTime: 1598932608000,
            createUserId: 2,
            updateUserId: 2,
            id: 15,
            labelId: 11,
            value: '中'
          },
          {
            createTime: 1598932608000,
            updateTime: 1598932608000,
            createUserId: 2,
            updateUserId: 2,
            id: 16,
            labelId: 11,
            value: '低'
          }
        ]
      },
      {
        createTime: 1598933156000,
        updateTime: 1598957217000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 13,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name:
          '召唤兽召唤兽召唤兽召唤兽召唤兽召唤兽召唤兽召唤兽召唤兽召唤兽召唤兽召唤兽召唤兽召唤兽召唤兽召唤兽',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598933156000,
            updateTime: 1598933156000,
            createUserId: 2,
            updateUserId: 2,
            id: 10017,
            labelId: 13,
            value: '万蛇'
          },
          {
            createTime: 1598933156000,
            updateTime: 1598933156000,
            createUserId: 2,
            updateUserId: 2,
            id: 10018,
            labelId: 13,
            value: '青蛙'
          },
          {
            createTime: 1598933156000,
            updateTime: 1598933156000,
            createUserId: 2,
            updateUserId: 2,
            id: 10019,
            labelId: 13,
            value: '无'
          },
          {
            createTime: 1598933156000,
            updateTime: 1598933156000,
            createUserId: 2,
            updateUserId: 2,
            id: 10020,
            labelId: 13,
            value: '忍犬'
          }
        ]
      },
      {
        createTime: 1598933156000,
        updateTime: 1598957219000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 14,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '属性',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598933156000,
            updateTime: 1598933156000,
            createUserId: 2,
            updateUserId: 2,
            id: 10021,
            labelId: 14,
            value: '土'
          },
          {
            createTime: 1598933156000,
            updateTime: 1598933156000,
            createUserId: 2,
            updateUserId: 2,
            id: 10022,
            labelId: 14,
            value: '雷'
          },
          {
            createTime: 1598933156000,
            updateTime: 1598933156000,
            createUserId: 2,
            updateUserId: 2,
            id: 10023,
            labelId: 14,
            value: '风'
          },
          {
            createTime: 1598933156000,
            updateTime: 1598933156000,
            createUserId: 2,
            updateUserId: 2,
            id: 10024,
            labelId: 14,
            value: '火'
          },
          {
            createTime: 1598933156000,
            updateTime: 1598933156000,
            createUserId: 2,
            updateUserId: 2,
            id: 10025,
            labelId: 14,
            value: '电'
          }
        ]
      },
      {
        createTime: 1598933156000,
        updateTime: 1598957222000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 15,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '完成a级任务',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10026,
            labelId: 15,
            value: '多'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10027,
            labelId: 15,
            value: '非常多'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10028,
            labelId: 15,
            value: '少'
          }
        ]
      },
      {
        createTime: 1598933157000,
        updateTime: 1598957224000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 16,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '级别',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10029,
            labelId: 16,
            value: '上忍'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10030,
            labelId: 16,
            value: '下忍'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10031,
            labelId: 16,
            value: '中忍'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10032,
            labelId: 16,
            value: '影'
          }
        ]
      },
      {
        createTime: 1598933157000,
        updateTime: 1598957227000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 17,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '血继极限',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10033,
            labelId: 17,
            value: '嘴盾'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10034,
            labelId: 17,
            value: '木遁'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10035,
            labelId: 17,
            value: '写轮眼'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10036,
            labelId: 17,
            value: '无'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10037,
            labelId: 17,
            value: '白眼'
          }
        ]
      },
      {
        createTime: 1598933157000,
        updateTime: 1598957230000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 18,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '人柱力',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10038,
            labelId: 18,
            value: '一尾'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10039,
            labelId: 18,
            value: '否'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10040,
            labelId: 18,
            value: '二尾'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10041,
            labelId: 18,
            value: '七尾'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10042,
            labelId: 18,
            value: '八尾'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10043,
            labelId: 18,
            value: '五尾'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10044,
            labelId: 18,
            value: '四尾'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10045,
            labelId: 18,
            value: '六尾'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10046,
            labelId: 18,
            value: '九尾'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10047,
            labelId: 18,
            value: '十尾'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10048,
            labelId: 18,
            value: '三尾'
          }
        ]
      },
      {
        createTime: 1598933157000,
        updateTime: 1598957232000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 19,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '饭量',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10049,
            labelId: 19,
            value: '小'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10050,
            labelId: 19,
            value: '中'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10051,
            labelId: 19,
            value: '大'
          }
        ]
      },
      {
        createTime: 1598933157000,
        updateTime: 1598957238000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 20,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '村子',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10052,
            labelId: 20,
            value: '雷'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10053,
            labelId: 20,
            value: '雾'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10054,
            labelId: 20,
            value: '风'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10055,
            labelId: 20,
            value: '火'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10056,
            labelId: 20,
            value: '雨'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10057,
            labelId: 20,
            value: '砂'
          }
        ]
      },
      {
        createTime: 1598933157000,
        updateTime: 1598957241000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 21,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '完成c级任务',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10058,
            labelId: 21,
            value: '少'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10059,
            labelId: 21,
            value: '多'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10060,
            labelId: 21,
            value: '非常多'
          }
        ]
      },
      {
        createTime: 1598933157000,
        updateTime: 1598957244000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 22,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '查克拉量',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10061,
            labelId: 22,
            value: '少'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10062,
            labelId: 22,
            value: '越来越多'
          },
          {
            createTime: 1598933157000,
            updateTime: 1598933157000,
            createUserId: 2,
            updateUserId: 2,
            id: 10063,
            labelId: 22,
            value: '用不完'
          },
          {
            createTime: 1598933158000,
            updateTime: 1598933158000,
            createUserId: 2,
            updateUserId: 2,
            id: 10064,
            labelId: 22,
            value: '多'
          }
        ]
      },
      {
        createTime: 1598933158000,
        updateTime: 1598957246000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 23,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '完成b级任务',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598933158000,
            updateTime: 1598933158000,
            createUserId: 2,
            updateUserId: 2,
            id: 10065,
            labelId: 23,
            value: '非常多'
          },
          {
            createTime: 1598933158000,
            updateTime: 1598933158000,
            createUserId: 2,
            updateUserId: 2,
            id: 10066,
            labelId: 23,
            value: '少'
          },
          {
            createTime: 1598933158000,
            updateTime: 1598933158000,
            createUserId: 2,
            updateUserId: 2,
            id: 10067,
            labelId: 23,
            value: '多'
          }
        ]
      },
      {
        createTime: 1598933158000,
        updateTime: 1598957248000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 24,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '体术',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598933158000,
            updateTime: 1598933158000,
            createUserId: 2,
            updateUserId: 2,
            id: 10068,
            labelId: 24,
            value: '超强'
          },
          {
            createTime: 1598933158000,
            updateTime: 1598933158000,
            createUserId: 2,
            updateUserId: 2,
            id: 10069,
            labelId: 24,
            value: '一般'
          },
          {
            createTime: 1598933158000,
            updateTime: 1598933158000,
            createUserId: 2,
            updateUserId: 2,
            id: 10070,
            labelId: 24,
            value: '强'
          },
          {
            createTime: 1598933158000,
            updateTime: 1598933158000,
            createUserId: 2,
            updateUserId: 2,
            id: 10071,
            labelId: 24,
            value: '不会'
          }
        ]
      },
      {
        createTime: 1598933158000,
        updateTime: 1598957255000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        id: 25,
        projectId: 'qvAD1jk8q0hA0Oxm',
        name: '完成s级任务',
        remark: '',
        type: 'EXTERNAL_IMPORT',
        category1: '',
        category2: '',
        category3: '',
        userLabelValues: [
          {
            createTime: 1598933158000,
            updateTime: 1598933158000,
            createUserId: 2,
            updateUserId: 2,
            id: 10072,
            labelId: 25,
            value: '多'
          },
          {
            createTime: 1598933158000,
            updateTime: 1598933158000,
            createUserId: 2,
            updateUserId: 2,
            id: 10073,
            labelId: 25,
            value: '非常多'
          },
          {
            createTime: 1598933158000,
            updateTime: 1598933158000,
            createUserId: 2,
            updateUserId: 2,
            id: 10074,
            labelId: 25,
            value: '少'
          }
        ]
      }
    ]
  }
};
const segmentDatas = {
  header: { code: 0 },
  body: [
    {
      createTime: 1617002339000,
      updateTime: 1630656216000,
      createUserId: 2,
      updateUserId: 2,
      createUserName: 'analyzer',
      updateUserName: 'analyzer',
      projectId: 'qvAD1jk8q0hA0Oxm',
      id: 873,
      name: '0329',
      customerCount: 265,
      status: 'NORMAL',
      calcStatus: 'SUC',
      calcMemo: '',
      type: 'CONDITIONAL',
      lastCalcTime: 1630656180000,
      calcRule: 'ONCE',
      scheduleConf: { calcRule: 'ONCE' },
      connector: 'AND',
      filterInfo: {
        connector: 'AND',
        filters: [
          {
            connector: 'AND',
            filters: [
              {
                field: 'name',
                fieldType: 'STRING',
                operator: 'IN',
                value: ['王峰'],
                tableId: 62,
                schemaId: 374,
                level1: '用户属性',
                level2: '',
                fieldName: '姓名',
                isEnum: false
              }
            ],
            empty: false
          }
        ]
      },
      label: { filters: [], empty: true },
      display: true,
      scenario: {
        createTime: 1603944027000,
        updateTime: 1608203693000,
        createUserId: 1,
        updateUserId: 1,
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 14,
        name: '女性场景',
        code: '0',
        joinEventColumnName: 'userId',
        joinOrderColumnName: 'user_id'
      },
      whetherTest: true
    },
    {
      createTime: 1617181139000,
      updateTime: 1617181279000,
      createUserId: 2,
      updateUserId: 2,
      createUserName: 'analyzer',
      updateUserName: 'analyzer',
      projectId: 'qvAD1jk8q0hA0Oxm',
      id: 915,
      name: '0331测试上传',
      customerCount: 1,
      status: 'NORMAL',
      calcStatus: 'SUC',
      calcMemo: '',
      type: 'UPLOAD',
      lastCalcTime: 1617181260000,
      calcRule: 'ONCE',
      scheduleConf: { calcRule: 'ONCE' },
      display: true,
      uploadPath: 'tmp/185391af-039e-4bc4-b089-29f4466b1951.csv',
      scenario: {
        createTime: 1603944027000,
        updateTime: 1608203693000,
        createUserId: 1,
        updateUserId: 1,
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 14,
        name: '女性场景',
        code: '0',
        joinEventColumnName: 'userId',
        joinOrderColumnName: 'user_id'
      },
      whetherTest: true
    },
    {
      createTime: 1617181159000,
      updateTime: 1617181459000,
      createUserId: 2,
      updateUserId: 2,
      createUserName: 'analyzer',
      updateUserName: 'analyzer',
      projectId: 'qvAD1jk8q0hA0Oxm',
      id: 916,
      name: '0331测试复合',
      customerCount: 7742,
      status: 'NORMAL',
      calcStatus: 'SUC',
      calcMemo: '',
      type: 'COMPLEX',
      lastCalcTime: 1617181440000,
      calcRule: 'ONCE',
      scheduleConf: { calcRule: 'ONCE' },
      display: true,
      limits: 0,
      includeSegments: {
        connector: 'OR',
        filters: [
          {
            id: 914,
            name: '0331测试自定义',
            lastCalcTime: 1617180900000,
            filter: { connector: 'AND', filters: [] },
            customerCount: 7742,
            valid: true
          },
          {
            id: 867,
            name: '复合分群',
            lastCalcTime: 1616493360000,
            filter: { connector: 'AND', filters: [] },
            customerCount: 278,
            valid: true
          }
        ]
      },
      excludeSegments: { connector: 'OR', filters: [] },
      scenario: {
        createTime: 1603944027000,
        updateTime: 1608203693000,
        createUserId: 1,
        updateUserId: 1,
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 14,
        name: '女性场景',
        code: '0',
        joinEventColumnName: 'userId',
        joinOrderColumnName: 'user_id'
      },
      whetherTest: true
    },
    {
      createTime: 1617180801000,
      updateTime: 1617180913000,
      createUserId: 2,
      updateUserId: 2,
      createUserName: 'analyzer',
      updateUserName: 'analyzer',
      projectId: 'qvAD1jk8q0hA0Oxm',
      id: 914,
      name: '0331测试自定义',
      customerCount: 7742,
      status: 'NORMAL',
      calcStatus: 'SUC',
      calcMemo: '',
      type: 'CONDITIONAL',
      lastCalcTime: 1617180900000,
      calcRule: 'ONCE',
      scheduleConf: { calcRule: 'ONCE' },
      connector: 'AND',
      filterInfo: {
        connector: 'AND',
        filters: [
          {
            connector: 'AND',
            filters: [
              {
                field: 'name',
                fieldType: 'STRING',
                operator: 'END_WITH',
                value: '丽',
                tableId: 62,
                schemaId: 374,
                level1: '用户属性',
                level2: '',
                fieldName: '姓名',
                isEnum: false
              }
            ],
            empty: false
          }
        ]
      },
      label: { filters: [], empty: true },
      display: true,
      scenario: {
        createTime: 1603944027000,
        updateTime: 1608203693000,
        createUserId: 1,
        updateUserId: 1,
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 14,
        name: '女性场景',
        code: '0',
        joinEventColumnName: 'userId',
        joinOrderColumnName: 'user_id'
      },
      whetherTest: true
    },
    {
      createTime: 1626660355000,
      updateTime: 1626660545000,
      createUserId: 2,
      updateUserId: 2,
      createUserName: 'analyzer',
      updateUserName: 'analyzer',
      projectId: 'qvAD1jk8q0hA0Oxm',
      id: 1064,
      name: '0719试',
      customerCount: 265,
      status: 'NORMAL',
      calcStatus: 'SUC',
      calcMemo: '',
      type: 'CONDITIONAL',
      lastCalcTime: 1626660540000,
      calcRule: 'ONCE',
      scheduleConf: { calcRule: 'ONCE' },
      connector: 'AND',
      filterInfo: {
        connector: 'AND',
        filters: [
          {
            connector: 'AND',
            filters: [
              {
                field: 'name',
                fieldType: 'STRING',
                operator: 'LIKE',
                value: '王峰',
                tableId: 62,
                schemaId: 374,
                level1: '用户属性',
                level2: '',
                fieldName: '姓名',
                isEnum: false
              }
            ],
            empty: false
          }
        ]
      },
      label: { filters: [], empty: true },
      display: true,
      scenario: {
        createTime: 1603944027000,
        updateTime: 1608203693000,
        createUserId: 1,
        updateUserId: 1,
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 14,
        name: '女性场景',
        code: '0',
        joinEventColumnName: 'userId',
        joinOrderColumnName: 'user_id'
      },
      whetherTest: true
    }
  ]
};
const eventDatas = {
  header: { code: 0 },
  body: {
    content: [
      {
        createTime: 1628146745000,
        updateTime: 1631781141000,
        createUserId: 1,
        updateUserId: 1,
        createUserName: 'admin',
        updateUserName: 'admin',
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 119,
        name: 'pageview1',
        filter: {
          connector: 'AND',
          filters: [
            {
              connector: 'AND',
              filters: [
                {
                  field: 'eventName',
                  fieldType: 'STRING',
                  operator: 'EQ',
                  value: 'pageview',
                  tableId: 31,
                  schemaId: 8,
                  level1: '',
                  level2: '',
                  fieldName: 'eventName',
                  isEnum: false
                }
              ],
              empty: false
            }
          ],
          empty: false
        },
        specialPropertyMappingList: [],
        eventType: 'CUSTOM',
        level1: '',
        level2: '',
        remark: ''
      },
      {
        createTime: 1626405299000,
        updateTime: 1626405299000,
        createUserId: 1,
        updateUserId: 1,
        createUserName: 'admin',
        updateUserName: 'admin',
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 113,
        name: 'gaoyl_test_event',
        filter: {
          connector: 'AND',
          filters: [
            {
              connector: 'AND',
              filters: [
                {
                  field: 'city',
                  fieldType: 'STRING',
                  operator: 'IS_NOT_NULL',
                  tableId: 31,
                  schemaId: 3,
                  level1: '',
                  level2: '',
                  fieldName: '城市',
                  isEnum: false
                }
              ],
              empty: false
            }
          ],
          empty: false
        },
        specialPropertyMappingList: [],
        eventType: 'CUSTOM',
        level1: '',
        level2: '',
        remark: ''
      },
      {
        createTime: 1614910253000,
        updateTime: 1625798508000,
        createUserId: 1,
        updateUserId: 1,
        createUserName: 'admin',
        updateUserName: 'admin',
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 81,
        name: 'pv_02',
        eventNameValue: 'pv_02',
        specialPropertyMappingList: [
          {
            displayName: '事件类型',
            propertySchema: 'event_body.type',
            dataType: 'INT',
            index: 0
          }
        ],
        eventType: 'BURIED_POINT_EVENT',
        level1: '',
        level2: '',
        remark: ''
      },
      {
        createTime: 1615773996000,
        updateTime: 1623910180000,
        createUserId: 1,
        updateUserId: 2,
        createUserName: 'admin',
        updateUserName: 'analyzer',
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 91,
        name: 'test_04',
        eventNameValue: 'test_04',
        specialPropertyMappingList: [
          { displayName: 'i', propertySchema: 'i', dataType: 'LONG', index: 0 },
          {
            displayName: 'ii',
            propertySchema: 'ii',
            dataType: 'LONG',
            index: 1
          },
          {
            displayName: 'iii',
            propertySchema: 'iii',
            dataType: 'LONG',
            index: 2
          },
          {
            displayName: 'iiii',
            propertySchema: 'iiii',
            dataType: 'LONG',
            index: 3
          },
          {
            displayName: 'iiiii',
            propertySchema: 'iiiii',
            dataType: 'LONG',
            index: 4
          },
          { displayName: 'l', propertySchema: 'l', dataType: 'INT', index: 5 },
          {
            displayName: 'll',
            propertySchema: 'll',
            dataType: 'INT',
            index: 6
          },
          {
            displayName: 'lll',
            propertySchema: 'lll',
            dataType: 'INT',
            index: 7
          },
          {
            displayName: 'llll',
            propertySchema: 'llll',
            dataType: 'INT',
            index: 8
          },
          {
            displayName: 'lllll',
            propertySchema: 'lllll',
            dataType: 'INT',
            index: 9
          },
          {
            displayName: 'd',
            propertySchema: 'd',
            dataType: 'DOUBLE',
            index: 0
          },
          {
            displayName: 'dd',
            propertySchema: 'dd',
            dataType: 'DOUBLE',
            index: 1
          },
          {
            displayName: 'ddd',
            propertySchema: 'ddd',
            dataType: 'DOUBLE',
            index: 2
          },
          {
            displayName: 'dddd',
            propertySchema: 'dddd',
            dataType: 'DOUBLE',
            index: 3
          },
          {
            displayName: 'ddddd',
            propertySchema: 'ddddd',
            dataType: 'DOUBLE',
            index: 4
          },
          {
            displayName: 'dddddd',
            propertySchema: 'dddddd',
            dataType: 'DOUBLE',
            index: 5
          },
          {
            displayName: 'ddddddd',
            propertySchema: 'ddddddd',
            dataType: 'DOUBLE',
            index: 6
          },
          {
            displayName: 'dddddddd',
            propertySchema: 'dddddddd',
            dataType: 'DOUBLE',
            index: 7
          },
          {
            displayName: 'ddddddddd',
            propertySchema: 'ddddddddd',
            dataType: 'DOUBLE',
            index: 8
          },
          {
            displayName: 'dddddddddd',
            propertySchema: 'dddddddddd',
            dataType: 'DOUBLE',
            index: 9
          },
          {
            displayName: 's1',
            propertySchema: 's1',
            dataType: 'STRING',
            index: 0
          },
          {
            displayName: 's2',
            propertySchema: 's2',
            dataType: 'STRING',
            index: 1
          },
          {
            displayName: 's3',
            propertySchema: 's3',
            dataType: 'STRING',
            index: 2
          },
          {
            displayName: 's4',
            propertySchema: 's4',
            dataType: 'STRING',
            index: 3
          },
          {
            displayName: 's5',
            propertySchema: 's5',
            dataType: 'STRING',
            index: 4
          },
          {
            displayName: 's6',
            propertySchema: 's6',
            dataType: 'STRING',
            index: 5
          },
          {
            displayName: 's7',
            propertySchema: 's7',
            dataType: 'STRING',
            index: 6
          },
          {
            displayName: 's8',
            propertySchema: 's8',
            dataType: 'STRING',
            index: 7
          },
          {
            displayName: 's9',
            propertySchema: 's9',
            dataType: 'STRING',
            index: 8
          },
          {
            displayName: 's0',
            propertySchema: 's0',
            dataType: 'STRING',
            index: 9
          },
          {
            displayName: 'ss1',
            propertySchema: 'ss1',
            dataType: 'STRING',
            index: 10
          },
          {
            displayName: 'ss2',
            propertySchema: 'ss2',
            dataType: 'STRING',
            index: 11
          },
          {
            displayName: 'ss3',
            propertySchema: 'ss3',
            dataType: 'STRING',
            index: 12
          },
          {
            displayName: 'ss4',
            propertySchema: 'ss4',
            dataType: 'STRING',
            index: 13
          },
          {
            displayName: 'ss5',
            propertySchema: 'ss5',
            dataType: 'STRING',
            index: 14
          },
          {
            displayName: 'ss6',
            propertySchema: 'ss6',
            dataType: 'STRING',
            index: 15
          },
          {
            displayName: 'ss7',
            propertySchema: 'ss7',
            dataType: 'STRING',
            index: 16
          },
          {
            displayName: 'ss8',
            propertySchema: 'ss8',
            dataType: 'STRING',
            index: 17
          },
          {
            displayName: 'ss9',
            propertySchema: 'ss9',
            dataType: 'STRING',
            index: 18
          },
          {
            displayName: 'ss0',
            propertySchema: 'ss0',
            dataType: 'STRING',
            index: 19
          },
          {
            displayName: 'sss1',
            propertySchema: 'sss1',
            dataType: 'STRING',
            index: 20
          },
          {
            displayName: 'sss2',
            propertySchema: 'sss2',
            dataType: 'STRING',
            index: 21
          },
          {
            displayName: 'sss3',
            propertySchema: 'sss3',
            dataType: 'STRING',
            index: 22
          },
          {
            displayName: 'sss4',
            propertySchema: 'sss4',
            dataType: 'STRING',
            index: 23
          },
          {
            displayName: 'sss5',
            propertySchema: 'sss5',
            dataType: 'STRING',
            index: 24
          },
          {
            displayName: 'sss6',
            propertySchema: 'sss6',
            dataType: 'STRING',
            index: 25
          },
          {
            displayName: 'sss7',
            propertySchema: 'sss7',
            dataType: 'STRING',
            index: 26
          },
          {
            displayName: 'sss8',
            propertySchema: 'sss8',
            dataType: 'STRING',
            index: 27
          },
          {
            displayName: 'sss9',
            propertySchema: 'sss9',
            dataType: 'STRING',
            index: 28
          },
          {
            displayName: '他',
            propertySchema: '3二次',
            dataType: 'STRING',
            index: 29
          }
        ],
        eventType: 'BURIED_POINT_EVENT',
        level1: '',
        level2: '',
        remark: ''
      },
      {
        createTime: 1623744894000,
        updateTime: 1623745992000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 109,
        name: '0615测试事件',
        eventNameValue: '$0615_pv',
        specialPropertyMappingList: [
          {
            displayName: '事类型件01',
            propertySchema: 'event_body.type',
            dataType: 'STRING',
            index: 2
          },
          {
            displayName: '事件名称',
            propertySchema: 'event_body.name',
            dataType: 'STRING',
            index: 3
          }
        ],
        eventType: 'BURIED_POINT_EVENT',
        remark: '123'
      },
      {
        createTime: 1623740165000,
        updateTime: 1623744869000,
        createUserId: 2,
        updateUserId: 1,
        createUserName: 'analyzer',
        updateUserName: 'admin',
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 108,
        name: '2144',
        eventNameValue: '34',
        specialPropertyMappingList: [
          {
            displayName: '2',
            propertySchema: '2',
            dataType: 'STRING',
            index: 0
          }
        ],
        eventType: 'BURIED_POINT_EVENT',
        level1: '',
        level2: '',
        remark: '33'
      },
      {
        createTime: 1623739105000,
        updateTime: 1623739202000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 106,
        name: '222324',
        eventNameValue: '2',
        specialPropertyMappingList: [],
        eventType: 'BURIED_POINT_EVENT',
        remark: '44'
      },
      {
        createTime: 1623738506000,
        updateTime: 1623738506000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 105,
        name: '2113',
        eventNameValue: '21313',
        specialPropertyMappingList: [
          {
            displayName: '事类型件 ',
            propertySchema: 'event_body.type',
            dataType: 'STRING',
            index: 0
          }
        ],
        eventType: 'BURIED_POINT_EVENT',
        remark: '2131'
      },
      {
        createTime: 1623206922000,
        updateTime: 1623222639000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 102,
        name: 'sdf',
        eventNameValue: 'sdf1',
        specialPropertyMappingList: [
          {
            displayName: 'sdfds',
            propertySchema: 'sfds1',
            dataType: 'STRING',
            index: 1
          },
          {
            displayName: 'df',
            propertySchema: 'sfd',
            dataType: 'INT',
            index: 0
          }
        ],
        eventType: 'BURIED_POINT_EVENT',
        remark: 'sdf'
      },
      {
        createTime: 1623206421000,
        updateTime: 1623206884000,
        createUserId: 2,
        updateUserId: 2,
        createUserName: 'analyzer',
        updateUserName: 'analyzer',
        projectId: 'qvAD1jk8q0hA0Oxm',
        id: 101,
        name: 'name',
        eventNameValue: 'sdfs',
        specialPropertyMappingList: [
          {
            displayName: 'sdfs',
            propertySchema: 'sdfds',
            dataType: 'STRING',
            index: 0
          }
        ],
        eventType: 'BURIED_POINT_EVENT',
        remark: 'sf'
      }
    ],
    number: 0,
    size: 10,
    totalElements: 50
  }
};
const functions = {
  header: { code: 0 },
  body: [
    {
      name: 'ABS',
      title: 'ABS',
      usage: 'ABS(数值)',
      description: 'Returns the absolute value of the numeric value',
      sample: '> SELECT abs(-1);\n 1',
      createSql: 'ABS()'
    },
    {
      name: 'APPROX_COUNT_DISTINCT',
      title: 'APPROX_COUNT_DISTINCT',
      usage: 'APPROX_COUNT_DISTINCT(expr[, relativeSD])',
      description:
        'Returns the estimated cardinality by HyperLogLog++. relativeSD defines the maximum relative standard deviation allowed.',
      sample:
        'SELECT approx_count_distinct(col1) FROM VALUES (1), (1), (2), (2), (3) tab(col1);\n 3',
      createSql: 'APPROX_COUNT_DISTINCT()'
    },
    {
      name: 'AVG',
      title: '平均值',
      usage: 'AVG(表达式/数值字段)',
      description:
        '返回表达式或数值字段所有值的平均值，只适用于数值字段，空值不会计算',
      sample: 'AVG(销售额)，返回"销售额"字段对应的所有非空值的平均值',
      createSql: 'AVG()'
    },
    {
      name: 'BASE64_DECODE',
      title: 'BASE64_DECODE',
      usage: 'unbase64(表达式/字段)',
      description: '返回base64解码值',
      sample: 'UNBASE64([字段])，返回base64解码值',
      createSql: 'UNBASE64()'
    },
    {
      name: 'BASE64_ENCODE',
      title: 'BASE64_ENCODE',
      usage: 'BASE64(字段)',
      description: '返回base64解码值',
      sample: 'BASE64([字段])，返回base64解码值',
      createSql: 'BASE64()'
    },
    {
      name: 'CAST_TO',
      title: '转为其它类型',
      usage: 'CAST((表达式/字段 AS 类型)',
      description: '转化表达式/字段为相应类型，类型INT BIGINT DOUBLE等',
      sample: 'CAST([金额] AS INT)',
      createSql: 'CAST( AS )'
    },
    {
      name: 'CAST_TO_INT',
      title: '转为整型',
      usage: 'CAST((表达式/字段 AS INT)',
      description: '转化表达式/字段为整形数字',
      sample: 'CAST([金额] AS INT)',
      createSql: 'CAST( AS INT)'
    },
    {
      name: 'CEIL',
      title: 'CEIL',
      usage: 'CEIL(数值)',
      description: '返回不小于数值a的最小整数',
      sample: 'CEIL(4.12),返回5',
      createSql: 'CEIL()'
    },
    {
      name: 'COLLECT_LIST',
      title: '转集合列表',
      usage: 'COLLECT_LIST(表达式/字段)',
      description: '收集字段或表达式为不唯一的数组',
      sample: 'COLLECT_LIST([城市])',
      createSql: 'COLLECT_LIST()'
    },
    {
      name: 'CONCAT',
      title: '连接',
      usage: 'concat(col1, col2, ..., colN)',
      description: '连接字符型字段/表达式, 返回连接后的字符串',
      sample: "select CONCAT([姓], '先生')",
      createSql: 'CONCAT()'
    },
    {
      name: 'COUNT',
      title: '计数',
      usage: 'COUNT(表达式/字段)',
      description: '返回表达式所有有效字段的数据条目数，空值不会计算',
      sample: 'COUNT(销售额)，返回"销售额"字段对应的所有非空值的数据条目数',
      createSql: 'COUNT()'
    },
    {
      name: 'CURRENT_DATE',
      title: 'CURRENT_DATE',
      usage: 'CURRENT_DATE()',
      description: 'Returns the current date at the start of query evaluation.',
      sample: '> SELECT current_date();\n 2020-04-25',
      createSql: 'CURRENT_DATE()'
    },
    {
      name: 'CURRENT_TIMESTAMP',
      title: 'CURRENT_TIMESTAMP',
      usage: 'CURRENT_TIMESTAMP()',
      description:
        'Returns the current timestamp at the start of query evaluation',
      sample: '> SELECT current_timestamp();\n 2020-04-25 15:49:11.914',
      createSql: 'CURRENT_TIMESTAMP()'
    },
    {
      name: 'DATEDIFF',
      title: 'DATEDIFF',
      usage: 'DATEDIFF(日期字段1,日期字段2)',
      description: '返回两个日期相差的天数，只允许传入日期型字段',
      sample:
        'DATEDIFF([离职日期],[入职日期] )，返回同一行上"离职日期"至"入职日期"间隔天数',
      createSql: 'DATEDIFF()'
    },
    {
      name: 'DATE_ADD',
      title: 'DATE_ADD',
      usage: 'DATE_ADD(起始日期, 数值字段)',
      description: '返回从起始日期算起，数值字段对应天数之后的日期',
      sample: 'DATE_ADD([入库日期], 1)，返回货品入库第二天的日期',
      createSql: 'DATE_ADD()'
    },
    {
      name: 'DATE_FORMAT',
      title: 'DATE_FORMAT',
      usage: 'DATE_FORMAT(timestamp, fmt)',
      description:
        'Converts timestamp to a value of string in the format specified by the date format fmt.',
      sample: "> SELECT date_format('2016-04-08', 'y');\n 2016",
      createSql: 'DATE_FORMAT()'
    },
    {
      name: 'DATE_PART',
      title: 'DATE_PART',
      usage: 'DATE_PART(field, source)',
      description: 'Extracts a part of the date/timestamp or interval source.',
      sample:
        "> SELECT date_part('YEAR', TIMESTAMP '2019-08-12 01:00:00.123456');\n 2019\n > SELECT date_part('week', timestamp'2019-08-12 01:00:00.123456'); \n 33 \n > SELECT date_part('doy', DATE'2019-08-12'); \n 224 \n > SELECT date_part('SECONDS', timestamp'2019-10-01 00:00:01.000001'); \n 1.000001 \n > SELECT date_part('days', interval 1 year 10 months 5 days); \n 5 \n > SELECT date_part('seconds', interval 5 hours 30 seconds 1 milliseconds 1 microseconds); \n 30.001001",
      createSql: 'DATE_PART()'
    },
    {
      name: 'DATE_SUB',
      title: 'DATE_SUB',
      usage: 'DATE_SUB(起始日期, 数值字段)',
      description: '返回从起始日期算起，数值字段对应天数之前的日期',
      sample: 'DATE_SUB([出库日期], 1)，返回货品出库前一天的日期',
      createSql: 'DATE_SUB()'
    },
    {
      name: 'DATE_TRUNC',
      title: 'DATE_TRUNC',
      usage: 'DATE_TRUNC(fmt, ts)',
      description:
        'Returns timestamp ts truncated to the unit specified by the format model fmt.',
      sample:
        "> SELECT date_trunc('YEAR', '2015-03-05T09:32:05.359');\n 2015-01-01 00:00:00\n > SELECT date_trunc('MM', '2015-03-05T09:32:05.359');\n 2015-03-01 00:00:00\n > SELECT date_trunc('DD', '2015-03-05T09:32:05.359');\n 2015-03-05 00:00:00\n > SELECT date_trunc('HOUR', '2015-03-05T09:32:05.359');\n 2015-03-05 09:00:00\n > SELECT date_trunc('MILLISECOND', '2015-03-05T09:32:05.123456');\n 2015-03-05 09:32:05.123",
      createSql: 'DATE_TRUNC()'
    },
    {
      name: 'DAY',
      title: 'DAY',
      usage: 'DAY(日期字段)',
      description: '返回该日期对应的日的值。只允许传入日期型字段',
      sample: 'DAY([下单时间])，返回该行"下单时间"字段对应的日的值',
      createSql: 'DAY()'
    },
    {
      name: 'DAYOFMONTH',
      title: 'DAYOFMONTH',
      usage: 'DAYOFMONTH(date)',
      description: 'Returns the day of month of the date/timestamp.',
      sample: 'SELECT dayofmonth(‘2009-07-30’); 30',
      createSql: 'DAYOFMONTH()'
    },
    {
      name: 'DAYOFYEAR',
      title: 'DAYOFYEAR',
      usage: 'DAYOFYEAR(date)',
      description: 'Returns the day of year of the date/timestamp.',
      sample: "SELECT dayofyear('2016-04-09');\n 100",
      createSql: 'DAYOFYEAR()'
    },
    {
      name: 'DAY_OF_WEEK',
      title: 'DAY_OF_WEEK',
      usage: 'DAY_OF_WEEK(日期字段[, 类型])',
      description:
        '返回该日期在一周中的第几天,如果需要返回“星期一”的格式，第二参数传入0',
      sample:
        "DAY_OF_WEEK([下单时间])；DAY_OF_WEEK('2016-05-13')，返回5；DAY_OF_WEEK([下单时间]，0)；DAY_OF_WEEK('2016-05-13'，0)，返回星期五",
      createSql: 'DAYOFMONTH()'
    },
    {
      name: 'DISTINCT',
      title: '去重',
      usage: 'DISTINCT(表达式/字段)',
      description: '返回唯一值',
      sample: 'DISTINCT([字段])',
      createSql: 'DISTINCT()'
    },
    {
      name: 'EXP',
      title: 'EXP',
      usage: 'EXP(expr)',
      description: 'Returns e to the power of expr',
      sample: '> SELECT exp(0)',
      createSql: 'EXP()'
    },
    {
      name: 'EXPLODE',
      title: '数组炸开',
      usage: '',
      description: '',
      sample: '',
      createSql: 'EXPLODE(表达式/字段)'
    },
    {
      name: 'FIRST',
      title: '第一个',
      usage: 'FIRST(表达式/字段)',
      description: '返回第一个值',
      sample: 'FIRST([字段])',
      createSql: 'FIRST()'
    },
    {
      name: 'FLOOR',
      title: 'FLOOR',
      usage: 'FLOOR(数值)',
      description: '返回不大于数值a的最大整数',
      sample: 'FLOOR(4.12),返回4',
      createSql: 'FLOOR()'
    },
    {
      name: 'FROM_UNIXTIME',
      title: '格式化unix时间',
      usage: 'from_unixtime(unix_time, format)',
      description: 'Returns unix_time in the specified format',
      sample:
        "> SELECT from_unixtime(0, 'yyyy-MM-dd HH:mm:ss');\n 1969-12-31 16:00:00",
      createSql: "from_unixtime( , 'yyyy-MM-dd HH:mm:ss')"
    },
    {
      name: 'FUNNEL_COUNT',
      title: '漏斗计数',
      usage: 'FUNNEL_COUNT(表达式/字段)',
      description: '',
      sample: '',
      createSql: 'FUNNEL_COUNT()'
    },
    {
      name: 'HOUR',
      title: 'HOUR',
      usage: 'HOUR(日期字段)',
      description: '返回该日期对应的小时的值。只允许传入日期型字段',
      sample: 'HOUR([下单时间])，返回该行"下单时间"字段对应的小时的值',
      createSql: 'HOUR()'
    },
    {
      name: 'IF',
      title: 'IF',
      usage: 'IF(表达式，结果1，结果2)',
      description:
        'IF为判断函数，表达式为比较型或计算型语句。若表达式的计算结果正确，则返回"结果1"，否则，返回"结果2"',
      sample:
        'IF([订单数] &gt; 500, "合格", "不合格")。结果为若该行"订单数"字段对应值大于500，则返回"合格"，否则返回"不合格"',
      createSql: 'IF()'
    },
    {
      name: 'INSTR',
      title: 'INSTR',
      usage: 'INSTR(str, substr)',
      description:
        'Returns the (1-based) index of the first occurrence of substr in str',
      sample: "> select instr('datatist', 'tist'); 5",
      createSql: 'INSTR()'
    },
    {
      name: 'LAST',
      title: 'LAST',
      usage: 'LAST(expr[, isIgnoreNull])',
      description:
        'Returns the last value of expr for a group of rows. If isIgnoreNull is true, returns only non-null values',
      sample:
        '> SELECT last_value(col) FROM VALUES (10), (5), (20) AS tab(col); \n 20 \n > SELECT last_value(col) FROM VALUES (10), (5), (NULL) AS tab(col); \n NULL \n > SELECT last_value(col, true) FROM VALUES (10), (5), (NULL) AS tab(col); \n 5',
      createSql: 'LAST()'
    },
    {
      name: 'LAST_DAY_OF_MONTH',
      title: 'LAST_DAY_OF_MONTH',
      usage: 'LAST_DAY(日期字段)',
      description: '返回某月最后一天，函数参数为数值型字段，依次代表年，月',
      sample: "LAST_DAY('2009-01-12')，返回2009-01-31",
      createSql: 'LAST_DAY()'
    },
    {
      name: 'LENGTH',
      title: 'LENGTH',
      usage: 'LENGTH(字符串)',
      description: '返回字符串的长度',
      sample: 'LENGTH([货品名])，返回货品名的长度',
      createSql: 'LENGTH()'
    },
    {
      name: 'LN',
      title: 'LN',
      usage: 'LN(数值)',
      description: '求数值对数，ln(数值)',
      sample: 'LN(10),返回2.3025',
      createSql: 'LN()'
    },
    {
      name: 'LOG',
      title: 'LOG',
      usage: 'LOG(数值A,数值B)',
      description: '以A为底，求B的对数',
      sample: 'LOG(2,4),返回2.0',
      createSql: 'LOG()'
    },
    {
      name: 'LOWER',
      title: 'LOWER',
      usage: 'LOWER(表达式/字段)',
      description: '返回表达式或字段值全部小写形式的字符串',
      sample: 'LOWER("ABC")，返回"abc"',
      createSql: 'LOWER()'
    },
    {
      name: 'MAP',
      title: '生成映射',
      usage: 'map(key0, value0, key1, value1, ...)',
      description: '创建key value映射',
      sample: '',
      createSql: 'MAP()'
    },
    {
      name: 'MAX',
      title: '最大',
      usage: 'MAX(销售额)',
      description:
        '返回表达式或数值字段所有值的最大值，只适用于数值字段，空值不会计算',
      sample: 'MAX(销售额)',
      createSql: 'MAX()'
    },
    {
      name: 'MIN',
      title: '最小',
      usage: 'MIN(销售额)',
      description:
        '返回表达式或数值字段所有值的最大值，只适用于数值字段，空值不会计算',
      sample: 'MIN(销售额)',
      createSql: 'MIN()'
    },
    {
      name: 'MINUTE',
      title: 'MINUTE',
      usage: 'MINUTE(timestamp)',
      description: 'Returns the minute component of the string/timestamp.',
      sample: "> SELECT minute('2009-07-30 12:58:59'); \n 58",
      createSql: 'MINUTE()'
    },
    {
      name: 'MONTH',
      title: 'MONTH',
      usage: 'month(date)',
      description: 'Returns the month component of the date/timestamp.',
      sample: "> SELECT month('2016-07-30'); \n 7",
      createSql: 'MONTH()'
    },
    {
      name: 'NOW',
      title: 'NOW',
      usage: 'NOW()',
      description:
        'Returns the current timestamp at the start of query evaluation.',
      sample: '> SELECT now(); \n 2020-04-25 15:49:11.914',
      createSql: 'NOW()'
    },
    {
      name: 'NVL',
      title: '空值判断',
      usage: 'NVL(表达式1/字段1, 表达式2/字段2)',
      description:
        '当表达式1/字段1为空时返回表达式2/字段2, 否则返回表达式1/字段1',
      sample: "NVL([姓名], 'Sophia')",
      createSql: 'NVL()'
    },
    {
      name: 'NVL2',
      title: 'NVL2',
      usage: 'nvl2(expr1, expr2, expr3)',
      description: 'Returns expr2 if expr1 is not null, or expr3 otherwise',
      sample: '> SELECT nvl2(NULL, 2, 1);\n1',
      createSql: 'NVL2()'
    },
    {
      name: 'POW',
      title: 'POW',
      usage: 'POW(数值A,数值B)',
      description: '求数值A的数值B次方',
      sample: 'POW(4，2),返回16.0',
      createSql: 'POW()'
    },
    {
      name: 'RAND',
      title: 'RAND',
      usage: 'RAND()',
      description: '返回大于0小于1的随机小数',
      sample: 'RAND(),返回随机数',
      createSql: 'RAND()'
    },
    {
      name: 'REGEXP_EXTRACT',
      title: 'REGEXP_EXTRACT',
      usage: 'REGEXP_EXTRACT(字符串, 正则表达式[, 索引])',
      description:
        "返回字符串正则表达式解析结果,需要注意的是正则表达式的需要使用\\进行转义，即\\d)，'索引'是返回结果(0表示返回全部结果，1表示返回正则表达式中第一个() 对应的结果)",
      sample:
        "REGEXP_EXTRACT([货品ID],'[\\d+\\-]+', 0)，返回货品ID中的数字部分",
      createSql: 'REGEXP_EXTRACT()'
    },
    {
      name: 'REGEXP_REPLACE',
      title: 'REGEXP_REPLACE',
      usage: 'REGEXP_REPLACE(字符串A, 正则表达式, 字符串B)',
      description: '返回将字符串A中符合正则表达式的部分替换成字符串B后的结果',
      sample:
        "REGEXP_REPLACE([货品名], '[\\d＋]+', '')，返回将货品名中数字部分替换成空字符串后的结果",
      createSql: 'REGEXP_REPLACE()'
    },
    {
      name: 'REPEAT',
      title: 'REPEAT',
      usage: 'REPEAT(字符串, 数值)',
      description: '返回字符串重复对应数值次数后的新字符串结果',
      sample: 'REPEAT([货品名], 2)，返回货品名重复2次得到字符串',
      createSql: 'REPEAT()'
    },
    {
      name: 'REVERSE',
      title: 'REVERSE',
      usage: 'REVERSE(字符串)',
      description: '返回字符串倒转后的新字符串结果',
      sample: 'REVERSE([类型编号])，返回类型编号倒转后的字符串',
      createSql: 'REVERSE()'
    },
    {
      name: 'ROUND',
      title: 'ROUND',
      usage: 'ROUND(数值A[,整数D])',
      description: '返回数值A四舍五入到小数点后D位。不填时为0',
      sample: 'ROUND(4.12，1),返回4.1',
      createSql: 'ROUND()'
    },
    {
      name: 'SLICE',
      title: '数组切分',
      usage: 'slice(x, start, length)',
      description: '取数组的子数组',
      sample: '',
      createSql: 'SLICE()'
    },
    {
      name: 'SPLIT',
      title: 'SPLIT',
      usage: 'SPLIT(str, regex, limit)',
      description:
        'Splits str around occurrences that match regex and returns an array with a length of at most limit',
      sample:
        '> SELECT split(\'oneAtwoBthreeC\', \'[ABC]\'); \n ["one","two","three",""] \n > SELECT split(\'oneAtwoBthreeC\', \'[ABC]\', -1); \n ["one","two","three",""] \n > SELECT split(\'oneAtwoBthreeC\', \'[ABC]\', 2); \n ["one","twoBthreeC"]',
      createSql: 'SPLIT()'
    },
    {
      name: 'SQRT',
      title: 'SQRT',
      usage: 'SQRT(数值)',
      description: '求数值的根号，需要大于等于零',
      sample: 'SQRT(4),返回2.0',
      createSql: 'SQRT()'
    },
    {
      name: 'SUBSTR',
      title: 'SUBSTR',
      usage: 'SUBSTR(字符串, 起始位置[, 长度])',
      description: '返回从起始位置起对应长度的字符串的子字符串，长度为可选项',
      sample: 'SUBSTR([商品类型], 4)，返回商品类型的索引为4起至末尾的子字符串',
      createSql: 'SUBSTR()'
    },
    {
      name: 'SUM',
      title: '求和',
      usage: 'SUM(表达式/数值字段)',
      description:
        '返回表达式或数值字段所有值的和，只适用于数值字段，空值不会计算',
      sample: 'SUM(销售额)',
      createSql: 'SUM()'
    },
    {
      name: 'TO_DATE',
      title: 'TO_DATE',
      usage: 'TO_DATE(date_str[, fmt])',
      description:
        'Parses the date_str expression with the fmt expression to a date. Returns null with invalid input. By default, it follows casting rules to a date if the fmt is omitted.',
      sample:
        "> SELECT to_date('2009-07-30 04:17:52');\n 2009-07-30 \n > SELECT to_date('2016-12-31', 'yyyy-MM-dd'); \n 2016-12-31",
      createSql: 'TO_DATE()'
    },
    {
      name: 'TRIM',
      title: 'TRIM',
      usage: 'TRIM(表达式/字段)',
      description: '去除表达式或字段中数据两边的空格',
      sample: 'TRIM(" ABC "),返回"ABC"',
      createSql: 'TRIM()'
    },
    {
      name: 'UNIQUE_COUNT',
      title: '唯一计数',
      usage: 'COUNT(DISTINCT(表达式/字段))',
      description:
        '去重计数，返回表达式所有有效字段的不同数据条目数，空值不会计算',
      sample:
        'COUNT(DISTINCT(销售额))，返回"销售额"字段对应的所有非空值的不同数据条目数',
      createSql: 'COUNT(DISTINCT())'
    },
    {
      name: 'UNIX_TIMESTAMP',
      title: '转时间戳',
      usage: 'UNIX_TIMESTAMP(表达式/字段)',
      description: '转化时间类型的字段为时间戳',
      sample: 'UNIX_TIMESTAMP([生日])',
      createSql: 'UNIX_TIMESTAMP()'
    },
    {
      name: 'UPPER',
      title: 'UPPER',
      usage: 'UPPER(表达式/字段串)',
      description: '返回表达式或字段值全部大写形式的字符串',
      sample: 'UPPER("abc"),返回"ABC"',
      createSql: 'UPPER()'
    },
    {
      name: 'WEEKDAY',
      title: 'WEEKDAY',
      usage: 'WEEKDAY(date)',
      description:
        'Returns the day of the week for date/timestamp (0 = Monday, 1 = Tuesday, ..., 6 = Sunday).',
      sample: "> SELECT weekday('2009-07-30'); \n 3",
      createSql: 'WEEKDAY()'
    },
    {
      name: 'WEEKOFYEAR',
      title: 'WEEKOFYEAR',
      usage: 'WEEKOFYEAR(date)',
      description:
        'Returns the week of the year of the given date. A week is considered to start on a Monday and week 1 is the first week with >3 days.',
      sample: "SELECT weekofyear('2008-02-20'); \n 8",
      createSql: 'WEEKOFYEAR()'
    },
    {
      name: 'YEAR',
      title: 'YEAR',
      usage: 'YEAR(日期字段)',
      description: '返回该日期对应的年份。只允许传入日期型字段',
      sample: 'YEAR([下单时间])，返回该行"下单时间"字段对应的年份',
      createSql: 'YEAR()'
    }
  ]
};
const eventProperty = {
  header: { code: 0 },
  body: [
    {
      field: 'eventName',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 8,
      level1: '通用事件属性',
      level2: '',
      fieldName: '事件名称',
      isEnum: false
    },
    {
      field: 'plugin',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 18,
      level1: '通用事件属性',
      level2: '',
      fieldName: '插件',
      isEnum: false
    },
    {
      field: 'region',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 21,
      level1: '通用事件属性',
      level2: '',
      fieldName: '地区',
      isEnum: false
    },
    {
      field: 'lgt',
      fieldType: 'DOUBLE',
      tableId: 31,
      schemaId: 16,
      level1: '通用事件属性',
      level2: '',
      fieldName: '经度',
      isEnum: false
    },
    {
      field: 'lat',
      fieldType: 'DOUBLE',
      tableId: 31,
      schemaId: 15,
      level1: '通用事件属性',
      level2: '',
      fieldName: '纬度',
      isEnum: false
    },
    {
      field: 'uaOs',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 31,
      level1: '通用事件属性',
      level2: '',
      fieldName: 'ua操作系统',
      isEnum: false
    },
    {
      field: 'userId',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 36,
      level1: '通用事件属性',
      level2: '',
      fieldName: '用户ID',
      isEnum: false
    },
    {
      field: 'userAgent',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 35,
      level1: '通用事件属性',
      level2: '',
      fieldName: '用户代理',
      isEnum: false
    },
    {
      field: 'uaName',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 30,
      level1: '通用事件属性',
      level2: '',
      fieldName: 'ua名称',
      isEnum: false
    },
    {
      field: 'title',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 26,
      level1: '通用事件属性',
      level2: '',
      fieldName: '标题',
      isEnum: false
    },
    {
      field: 'url',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 34,
      level1: '通用事件属性',
      level2: '',
      fieldName: 'url',
      isEnum: false
    },
    {
      field: 'language',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 14,
      level1: '通用事件属性',
      level2: '',
      fieldName: '语言',
      isEnum: false
    },
    {
      field: 'eventTime',
      fieldType: 'TIMESTAMP',
      tableId: 31,
      schemaId: 10,
      level1: '通用事件属性',
      level2: '',
      fieldName: '事件时间',
      isEnum: false
    },
    {
      field: 'userTags',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 37,
      level1: '通用事件属性',
      level2: '',
      fieldName: '用户标签',
      isEnum: false
    },
    {
      field: 'sessionId',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 24,
      level1: '通用事件属性',
      level2: '',
      fieldName: '会话ID',
      isEnum: false
    },
    {
      field: 'netType',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 17,
      level1: '通用事件属性',
      level2: '',
      fieldName: '网络类型',
      isEnum: false
    },
    {
      field: 'key',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 13,
      level1: '通用事件属性',
      level2: '',
      fieldName: '值',
      isEnum: false
    },
    {
      field: 'deviceId',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 6,
      level1: '通用事件属性',
      level2: '',
      fieldName: '压力_设备ID',
      isEnum: false
    },
    {
      field: 'country',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 5,
      level1: '通用事件属性',
      level2: '',
      fieldName: '国家',
      isEnum: true
    },
    {
      field: 'date',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 38,
      level1: '通用事件属性',
      level2: '',
      fieldName: '日期',
      isEnum: false
    },
    {
      field: 'city',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 3,
      level1: '通用事件属性',
      level2: '',
      fieldName: '城市',
      isEnum: false
    },
    {
      field: 'projectId',
      fieldType: 'STRING',
      tableId: 31,
      schemaId: 39,
      level1: '通用事件属性',
      level2: '',
      fieldName: '通用_项目ID',
      isEnum: false
    },
    {
      field: 's_var2',
      fieldType: 'STRING',
      tableId: 31,
      level1: '事件专有属性',
      level2: '',
      fieldName: '事类型件01',
      isEnum: false
    },
    {
      field: 's_var3',
      fieldType: 'STRING',
      tableId: 31,
      level1: '事件专有属性',
      level2: '',
      fieldName: '事件名称',
      isEnum: false
    },
    {
      field: 'dt_id',
      fieldType: 'STRING',
      tableId: 228,
      schemaId: 1425,
      level1: 'test_user_78',
      level2: '',
      fieldName: 'dt_id',
      isEnum: false
    },
    {
      field: 'scenario_code',
      fieldType: 'STRING',
      tableId: 228,
      schemaId: 1426,
      level1: 'test_user_78',
      level2: '',
      fieldName: 'scenario_code',
      isEnum: false
    },
    {
      field: 'name',
      fieldType: 'STRING',
      tableId: 228,
      schemaId: 1428,
      level1: 'test_user_78',
      level2: '',
      fieldName: 'name',
      isEnum: false
    },
    {
      field: 'phone',
      fieldType: 'STRING',
      tableId: 228,
      schemaId: 1430,
      level1: 'test_user_78',
      level2: '',
      fieldName: 'phone',
      isEnum: false
    },
    {
      field: 'gender',
      fieldType: 'STRING',
      tableId: 228,
      schemaId: 1432,
      level1: 'test_user_78',
      level2: '',
      fieldName: 'gender',
      isEnum: false
    },
    {
      field: 'a',
      fieldType: 'STRING',
      tableId: 275,
      schemaId: 1458,
      level1: 'dd',
      level2: '',
      fieldName: 'a',
      isEnum: false
    },
    {
      field: 'b',
      fieldType: 'DOUBLE',
      tableId: 275,
      schemaId: 1459,
      level1: 'dd',
      level2: '',
      fieldName: 'b',
      isEnum: false
    },
    {
      field: 'c',
      fieldType: 'STRING',
      tableId: 275,
      schemaId: 1460,
      level1: 'dd',
      level2: '',
      fieldName: 'cd',
      isEnum: false
    },
    {
      field: 'w',
      fieldType: 'STRING',
      tableId: 275,
      schemaId: 1461,
      level1: 'dd',
      level2: '',
      fieldName: '1',
      isEnum: false
    },
    {
      field: 'q',
      fieldType: 'TIMESTAMP',
      tableId: 275,
      schemaId: 1462,
      level1: 'dd',
      level2: '',
      fieldName: 'q',
      isEnum: false
    },
    {
      field: '$1',
      fieldType: 'STRING',
      tableId: 275,
      schemaId: 1463,
      level1: 'dd',
      level2: '',
      fieldName: '12',
      isEnum: false
    },
    {
      field: 'w_01',
      fieldType: 'LONG',
      tableId: 276,
      schemaId: 1464,
      level1: '测试0902',
      level2: '',
      fieldName: 'w_01',
      isEnum: false
    },
    {
      field: 'w_02',
      fieldType: 'DOUBLE',
      tableId: 276,
      schemaId: 1465,
      level1: '测试0902',
      level2: '',
      fieldName: 'w_02',
      isEnum: false
    },
    {
      field: 'w_03',
      fieldType: 'STRING',
      tableId: 276,
      schemaId: 1466,
      level1: '测试0902',
      level2: '',
      fieldName: 'w_03',
      isEnum: false
    },
    {
      field: 'w_04',
      fieldType: 'STRING',
      tableId: 276,
      schemaId: 1467,
      level1: '测试0902',
      level2: '',
      fieldName: 'w_04',
      isEnum: false
    },
    {
      field: 'w_05',
      fieldType: 'STRING',
      tableId: 276,
      schemaId: 1468,
      level1: '测试0902',
      level2: '',
      fieldName: 'w_05',
      isEnum: false
    }
  ]
};
const TestActionCollective = () => {
  // const [value, setValue] = useState(filterValue);
  const [value, setValue] = useState({});
  const [mode, setMode] = useState('edit');
  const [jsonData, setJsonData] = useState('{}');
  const ref = useRef(null);

  const dataProvider = {
    getPropertyList: name => {
      console.log('fetch propertyList 1', name);
      return propertyList;
    },
    getPropertyEnumList: (tableId, schemaId) => {
      const propertyItem = _.find(
        propertyItemList,
        v => v.tableId === tableId && v.schemaId === schemaId
      );
      return propertyItem ? propertyItem.items : [];
    },
    getTagList: async data => [],
    findCategoryByProjectId: () => [],
    getEventList: () => eventDatas.body,
    getFunctionList: () => functions.body,
    getEventPropertyList: () => eventProperty.body,
    getSegmentList: () => segmentDatas.body,
    getTagList: async data => [],
    findAllCategory: () => datas.body,
    findCategoryByProjectId: () => [],
    getTagValuesById: id =>
      datas.body.userLabels.find(n => n.id == id).userLabelValues
  };

  // const onChange = (value) => {
  //   console.log('TestEventFilter', value);
  //   setValue(value);
  // };

  const onChange = useCallback(value => {
    setValue(value);
  }, []);

  const onSubmit = () => {
    if (ref.current) {
      console.log(
        ref.current.isValid(),
        'isValid----------------------------------'
      );
    }
  };

  useEffect(() => {
    setJsonData(JSON.stringify(value));
  }, [value]);

  return (
    <div>
      <div
        style={{
          margin: 20,
          width: 1360,
          padding: 20,
          border: '1px solid #000',
          display: 'inline-block'
        }}
      >
        <ActionCollective
          dataProvider={dataProvider}
          showInitLine={false}
          value={value}
          onChange={onChange}
          mode={mode}
          ref={ref}
        />
        <Button onClick={() => setValue({})}>清空</Button>
        <Button onClick={() => setMode(mode === 'edit' ? 'detail' : 'edit')}>
          只读切换
        </Button>
        <Button onClick={() => onSubmit()}>submit</Button>
        <hr />
        <div>
          <JSONPretty data={value} />
        </div>
      </div>
      <div style={{ display: 'inline-block', width: 500 }}>
        <Button onClick={() => setValue(JSON.parse(jsonData))}>
          Submit Data
        </Button>
        <TextArea
          value={jsonData}
          rows={30}
          onChange={e => setJsonData(e.target.value)}
        />
      </div>
    </div>
  );
};

export default TestActionCollective;
