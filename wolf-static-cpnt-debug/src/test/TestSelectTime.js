/*
 * @Author: Wxw
 * @Date: 2022-01-19 17:41:13
 * @LastEditTime: 2022-09-14 18:24:08
 * @LastEditors: Wxw
 * @Description:
 * @FilePath: \datatist-wolf-static-cpnt\example\src\test\TestSelectTime.js
 */
import React, { useState, useReducer } from 'react';
import { SelectTime } from 'wolf-static-cpnt';
import { Radio } from 'antd';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

dayjs.locale('zh-cn');
const shortcutOptions = {
  今天: [dayjs().startOf('day'), dayjs().endOf('day')],
  昨日: [
    dayjs()
      .subtract(1, 'days')
      .startOf('day'),
    dayjs()
      .subtract(1, 'days')
      .endOf('day')
  ],
  本周: [dayjs().startOf('week'), dayjs().endOf('week')],
  上周: [
    dayjs()
      .subtract(1, 'weeks')
      .startOf('week'),
    dayjs()
      .subtract(1, 'weeks')
      .endOf('week')
  ],
  本月: [dayjs().startOf('month'), dayjs().endOf('month')],
  上月: [
    dayjs()
      .subtract(1, 'months')
      .startOf('month'),
    dayjs()
      .subtract(1, 'months')
      .endOf('month')
  ],
  今年: [dayjs().startOf('year'), dayjs().endOf('year')],
  去年: [
    dayjs()
      .subtract(1, 'years')
      .startOf('year'),
    dayjs()
      .subtract(1, 'years')
      .endOf('year')
  ],
  过去的1小时: [
    dayjs()
      .subtract(1, 'hours')
      .startOf('hour'),
    dayjs()
      .subtract(1, 'hours')
      .endOf('hour')
  ],
  过去的24小时: [
    dayjs()
      .subtract(1, 'hours')
      .subtract(24, 'hours')
      .startOf('hour'),
    dayjs()
      .subtract(1, 'hours')
      .subtract(24, 'hours')
      .endOf('hour')
  ],
  过去的7天: [
    dayjs()
      .subtract(1, 'days')
      .subtract(7, 'days'),
    dayjs().subtract(1, 'days')
  ],
  过去的14天: [dayjs().subtract(14, 'days'), dayjs().subtract(1, 'days')],
  过去的30天: [dayjs().subtract(30, 'days'), dayjs().subtract(1, 'days')],
  过去的60天: [dayjs().subtract(60, 'days'), dayjs().subtract(1, 'days')],
  过去的90天: [dayjs().subtract(90, 'days'), dayjs().subtract(1, 'days')],
  过去的180天: [dayjs().subtract(180, 'days'), dayjs().subtract(1, 'days')]
};
const reducer = (state, action) => ({ ...state, ...action });

export default function TestSelectTime() {
  const [data, setData] = useState([
    {
      type: 'RELATIVE',
      timeTerm: 'DAY',
      isPast: true,
      times: 30,
      truncateAsDay: true
    },
    { type: 'NOW', timeTerm: 'DAY', isPast: true, truncateAsDay: true }
  ]);
  // const [value, setValue] = useState([
  //     {
  //         "type": "RELATIVE",
  //         "times": 11,
  //         "timeTerm": "day",
  //         //"startSpecificTime": "specificTime",
  //         "startSpecificTime": "inPast",
  //         "timestamp": 1657350189000
  //     },
  //     {
  //         "type": "RELATIVE",
  //         "times": 2,
  //         "timeTerm": "day",
  //         "endSpecificTime": "future"
  //     }
  // ]);
  const [value, setValue] = useState([]);
  const [radio, setRadio] = useState(1);
  const [state, dispatch] = useReducer(reducer, {
    dateRange2: [
      {
        type: 'RELATIVE',
        timeTerm: 'DAY',
        isPast: true,
        times: 7,
        truncateAsDay: true
      },
      { type: 'NOW', timeTerm: 'DAY', isPast: true, truncateAsDay: true }
    ]
  });

  const clickShortcut = name => {
    const range = shortcutOptions[name];
    const _info = [
      {
        type: 'ABSOLUTE',
        timestamp: dayjs(range[0]).valueOf()
      },
      {
        type: 'ABSOLUTE',
        timestamp: dayjs(range[1]).valueOf()
      }
    ];
    setData(_info);
    setValue(_info);
    // const range = shortcutOptions[name];
    // setValue(range);
  };

  const changeRadio = e => {
    setRadio(e.target.value);
  };

  const change = dayjs => {
    // setSelectTimeV2(dayjs)
    setValue(dayjs);
    console.log('触发了父组件的change', dayjs);
  };
  return (
    <div style={{ padding: 100 }}>
      123123
      <div className="contentRight ">
        {Object.keys(shortcutOptions).map(item => {
          return (
            <Radio.Group onChange={changeRadio} value={radio} key={item}>
              <Radio.Button
                value={item}
                key={item}
                className="shortcut"
                onClick={() => clickShortcut(item)}
              >
                {item}
              </Radio.Button>
            </Radio.Group>
          );
        })}
      </div>
      <div style={{ display: 'flex' }}>
        {/* <ShortcutTime
            dispatch={dispatch}
            dispatchName="dateRange2"
            setTime={setData}
            isAnalysis
            ></ShortcutTime> */}
        <SelectTime
          data={[]}
          style={{ borderColor: 'red' }}
          showTime
          isAnalysis
          // type={['ABSOLUTE']}
          onChange={(e, flag) => {
            dispatch({ dateRange2: e });
            // setData(e);
            console.log(e, flag, '校验是否通过');
          }}
        />
      </div>
      <br />
    </div>
  );
}
