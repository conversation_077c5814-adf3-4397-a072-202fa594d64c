export const flowConfig = [
  {
    "id": 969,
    "name": "起始事件",
    "busiType": "EventEntryNode",
    "fakeable": true,
    "displayInBox": true,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-2",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "ENTRY",
    "status": "NORMAL",
    "displayName": "起始事件",
    "nodeId": 1,
    "fatherIds": [],
    "childrenIds": [
      3
    ],
    "branchIndex": 0,
    "x": 0,
    "y": 0
  },
  {
    "id": 260,
    "name": "退出节点",
    "busiType": "ExitHelperNode",
    "fakeable": false,
    "displayInBox": false,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-1",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "END",
    "status": "NORMAL",
    "displayName": "退出节点",
    "nodeId": 2,
    "fatherIds": [
      3
    ],
    "childrenIds": [],
    "branchIndex": 0,
    "x": 2,
    "y": 0,
    "detail": {
      "busiType": "ExitHelperNode"
    }
  },
  {
    "id": 970,
    "name": "发送短信",
    "busiType": "SMSPushNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#0099FF",
    "icon": "#iconicon_shouji",
    "shape": "SQUARE",
    "orders": 1,
    "joinType": "FUNCTION",
    "status": "NORMAL",
    "displayName": "发送短信",
    "nodeId": 3,
    "fatherIds": [
      1
    ],
    "childrenIds": [
      2
    ],
    "branchIndex": 0,
    "x": 1,
    "y": 0,
    "detail": {
      "busiType": "SMSPushNode"
    }
  },
  {
    "id": 969,
    "name": "起始事件",
    "busiType": "EventEntryNode",
    "fakeable": true,
    "displayInBox": true,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-2",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "ENTRY",
    "status": "NORMAL",
    "displayName": "起始事件",
    "nodeId": 4,
    "fatherIds": [],
    "childrenIds": [
      5
    ],
    "branchIndex": 0,
    "x": 0,
    "y": 1
  },
  {
    "id": 260,
    "name": "退出节点",
    "busiType": "ExitHelperNode",
    "fakeable": false,
    "displayInBox": false,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-1",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "END",
    "status": "NORMAL",
    "displayName": "退出节点",
    "nodeId": 5,
    "fatherIds": [
      4
    ],
    "childrenIds": [],
    "branchIndex": 0,
    "x": 1,
    "y": 1,
    "detail": {
      "busiType": "ExitHelperNode"
    }
  },
  {
    "id": 969,
    "name": "起始事件",
    "busiType": "EventEntryNode",
    "fakeable": true,
    "displayInBox": true,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-2",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "ENTRY",
    "status": "NORMAL",
    "displayName": "起始事件",
    "nodeId": 6,
    "fatherIds": [],
    "childrenIds": [
      7
    ],
    "branchIndex": 0,
    "x": 0,
    "y": 2
  },
  {
    "id": 260,
    "name": "退出节点",
    "busiType": "ExitHelperNode",
    "fakeable": false,
    "displayInBox": false,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-1",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "END",
    "status": "NORMAL",
    "displayName": "退出节点",
    "nodeId": 7,
    "fatherIds": [
      6
    ],
    "childrenIds": [],
    "branchIndex": 0,
    "x": 1,
    "y": 2,
    "detail": {
      "busiType": "ExitHelperNode"
    }
  },
  {
    "id": 969,
    "name": "起始事件",
    "busiType": "EventEntryNode",
    "fakeable": true,
    "displayInBox": true,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-2",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "ENTRY",
    "status": "NORMAL",
    "displayName": "起始事件",
    "nodeId": 8,
    "fatherIds": [],
    "childrenIds": [
      9
    ],
    "branchIndex": 0,
    "x": 0,
    "y": 3
  },
  {
    "id": 260,
    "name": "退出节点",
    "busiType": "ExitHelperNode",
    "fakeable": false,
    "displayInBox": false,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-1",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "END",
    "status": "NORMAL",
    "displayName": "退出节点",
    "nodeId": 9,
    "fatherIds": [
      8
    ],
    "childrenIds": [],
    "branchIndex": 0,
    "x": 1,
    "y": 3,
    "detail": {
      "busiType": "ExitHelperNode"
    }
  },
  {
    "id": 969,
    "name": "起始事件",
    "busiType": "EventEntryNode",
    "fakeable": true,
    "displayInBox": true,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-2",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "ENTRY",
    "status": "NORMAL",
    "displayName": "起始事件",
    "nodeId": 10,
    "fatherIds": [],
    "childrenIds": [
      11
    ],
    "branchIndex": 0,
    "x": 0,
    "y": 4
  },
  {
    "id": 260,
    "name": "退出节点",
    "busiType": "ExitHelperNode",
    "fakeable": false,
    "displayInBox": false,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-1",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "END",
    "status": "NORMAL",
    "displayName": "退出节点",
    "nodeId": 11,
    "fatherIds": [
      10
    ],
    "childrenIds": [],
    "branchIndex": 0,
    "x": 1,
    "y": 4,
    "detail": {
      "busiType": "ExitHelperNode"
    }
  },
  {
    "id": 969,
    "name": "起始事件",
    "busiType": "EventEntryNode",
    "fakeable": true,
    "displayInBox": true,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-2",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "ENTRY",
    "status": "NORMAL",
    "displayName": "起始事件",
    "nodeId": 12,
    "fatherIds": [],
    "childrenIds": [
      13
    ],
    "branchIndex": 0,
    "x": 0,
    "y": 5
  },
  {
    "id": 260,
    "name": "退出节点",
    "busiType": "ExitHelperNode",
    "fakeable": false,
    "displayInBox": false,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-1",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "END",
    "status": "NORMAL",
    "displayName": "退出节点",
    "nodeId": 13,
    "fatherIds": [
      12
    ],
    "childrenIds": [],
    "branchIndex": 0,
    "x": 1,
    "y": 5,
    "detail": {
      "busiType": "ExitHelperNode"
    }
  },
  {
    "id": 969,
    "name": "起始事件",
    "busiType": "EventEntryNode",
    "fakeable": true,
    "displayInBox": true,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-2",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "ENTRY",
    "status": "NORMAL",
    "displayName": "起始事件",
    "nodeId": 14,
    "fatherIds": [],
    "childrenIds": [
      15
    ],
    "branchIndex": 0,
    "x": 0,
    "y": 6
  },
  {
    "id": 260,
    "name": "退出节点",
    "busiType": "ExitHelperNode",
    "fakeable": false,
    "displayInBox": false,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-1",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "END",
    "status": "NORMAL",
    "displayName": "退出节点",
    "nodeId": 15,
    "fatherIds": [
      14
    ],
    "childrenIds": [],
    "branchIndex": 0,
    "x": 1,
    "y": 6,
    "detail": {
      "busiType": "ExitHelperNode"
    }
  },
  {
    "id": 969,
    "name": "起始事件",
    "busiType": "EventEntryNode",
    "fakeable": true,
    "displayInBox": true,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-2",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "ENTRY",
    "status": "NORMAL",
    "displayName": "起始事件",
    "nodeId": 16,
    "fatherIds": [],
    "childrenIds": [
      17
    ],
    "branchIndex": 0,
    "x": 0,
    "y": 7
  },
  {
    "id": 260,
    "name": "退出节点",
    "busiType": "ExitHelperNode",
    "fakeable": false,
    "displayInBox": false,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-1",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "END",
    "status": "NORMAL",
    "displayName": "退出节点",
    "nodeId": 17,
    "fatherIds": [
      16
    ],
    "childrenIds": [],
    "branchIndex": 0,
    "x": 1,
    "y": 7,
    "detail": {
      "busiType": "ExitHelperNode"
    }
  },
  {
    "id": 969,
    "name": "起始事件",
    "busiType": "EventEntryNode",
    "fakeable": true,
    "displayInBox": true,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-2",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "ENTRY",
    "status": "NORMAL",
    "displayName": "起始事件",
    "nodeId": 18,
    "fatherIds": [],
    "childrenIds": [
      19
    ],
    "branchIndex": 0,
    "x": 0,
    "y": 8
  },
  {
    "id": 260,
    "name": "退出节点",
    "busiType": "ExitHelperNode",
    "fakeable": false,
    "displayInBox": false,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-1",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "END",
    "status": "NORMAL",
    "displayName": "退出节点",
    "nodeId": 19,
    "fatherIds": [
      18
    ],
    "childrenIds": [],
    "branchIndex": 0,
    "x": 1,
    "y": 8,
    "detail": {
      "busiType": "ExitHelperNode"
    }
  },
  {
    "id": 969,
    "name": "起始事件",
    "busiType": "EventEntryNode",
    "fakeable": true,
    "displayInBox": true,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-2",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "ENTRY",
    "status": "NORMAL",
    "displayName": "起始事件",
    "nodeId": 20,
    "fatherIds": [],
    "childrenIds": [
      36
    ],
    "branchIndex": 0,
    "x": 0,
    "y": 9
  },
  {
    "id": 260,
    "name": "退出节点",
    "busiType": "ExitHelperNode",
    "fakeable": false,
    "displayInBox": false,
    "color": "#1EC78B",
    "icon": "#iconyonghuzhongxin-1",
    "shape": "CIRCLE",
    "orders": 2,
    "joinType": "END",
    "status": "NORMAL",
    "displayName": "退出节点",
    "nodeId": 21,
    "fatherIds": [
      22
    ],
    "childrenIds": [],
    "branchIndex": 0,
    "x": 16,
    "y": 9,
    "detail": {
      "busiType": "ExitHelperNode"
    }
  },
  {
    "id": 970,
    "name": "发送短信",
    "busiType": "SMSPushNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#0099FF",
    "icon": "#iconicon_shouji",
    "shape": "SQUARE",
    "orders": 1,
    "joinType": "FUNCTION",
    "status": "NORMAL",
    "displayName": "发送短信",
    "nodeId": 22,
    "fatherIds": [
      23
    ],
    "childrenIds": [
      21
    ],
    "branchIndex": 0,
    "x": 15,
    "y": 9,
    "detail": {
      "busiType": "SMSPushNode"
    }
  },
  {
    "id": 1120,
    "name": "多事件分支",
    "busiType": "MultiEventSplitNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#FCA400",
    "icon": "#iconduoshijian",
    "shape": "DIAMOND",
    "orders": 1,
    "joinType": "SPLIT",
    "status": "NORMAL",
    "displayName": "多事件分支",
    "nodeId": 23,
    "fatherIds": [
      25
    ],
    "childrenIds": [
      22
    ],
    "branchIndex": 0,
    "x": 14,
    "y": 9
  },
  {
    "id": 1120,
    "name": "多事件分支",
    "busiType": "MultiEventSplitNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#FCA400",
    "icon": "#iconduoshijian",
    "shape": "DIAMOND",
    "orders": 1,
    "joinType": "SPLIT",
    "status": "NORMAL",
    "displayName": "多事件分支",
    "nodeId": 24,
    "fatherIds": [
      26
    ],
    "childrenIds": [
      25
    ],
    "branchIndex": 0,
    "x": 12,
    "y": 9
  },
  {
    "id": 1120,
    "name": "多事件分支",
    "busiType": "MultiEventSplitNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#FCA400",
    "icon": "#iconduoshijian",
    "shape": "DIAMOND",
    "orders": 1,
    "joinType": "SPLIT",
    "status": "NORMAL",
    "displayName": "多事件分支",
    "nodeId": 25,
    "fatherIds": [
      24
    ],
    "childrenIds": [
      23
    ],
    "branchIndex": 0,
    "x": 13,
    "y": 9
  },
  {
    "id": 1120,
    "name": "多事件分支",
    "busiType": "MultiEventSplitNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#FCA400",
    "icon": "#iconduoshijian",
    "shape": "DIAMOND",
    "orders": 1,
    "joinType": "SPLIT",
    "status": "NORMAL",
    "displayName": "多事件分支",
    "nodeId": 26,
    "fatherIds": [
      27
    ],
    "childrenIds": [
      24
    ],
    "branchIndex": 0,
    "x": 11,
    "y": 9
  },
  {
    "id": 1120,
    "name": "多事件分支",
    "busiType": "MultiEventSplitNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#FCA400",
    "icon": "#iconduoshijian",
    "shape": "DIAMOND",
    "orders": 1,
    "joinType": "SPLIT",
    "status": "NORMAL",
    "displayName": "多事件分支",
    "nodeId": 27,
    "fatherIds": [
      28
    ],
    "childrenIds": [
      26
    ],
    "branchIndex": 0,
    "x": 10,
    "y": 9
  },
  {
    "id": 1120,
    "name": "多事件分支",
    "busiType": "MultiEventSplitNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#FCA400",
    "icon": "#iconduoshijian",
    "shape": "DIAMOND",
    "orders": 1,
    "joinType": "SPLIT",
    "status": "NORMAL",
    "displayName": "多事件分支",
    "nodeId": 28,
    "fatherIds": [
      29
    ],
    "childrenIds": [
      27
    ],
    "branchIndex": 0,
    "x": 9,
    "y": 9
  },
  {
    "id": 1120,
    "name": "多事件分支",
    "busiType": "MultiEventSplitNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#FCA400",
    "icon": "#iconduoshijian",
    "shape": "DIAMOND",
    "orders": 1,
    "joinType": "SPLIT",
    "status": "NORMAL",
    "displayName": "多事件分支",
    "nodeId": 29,
    "fatherIds": [
      30
    ],
    "childrenIds": [
      28
    ],
    "branchIndex": 0,
    "x": 8,
    "y": 9
  },
  {
    "id": 7,
    "groupId": 4,
    "name": "触发事件分支",
    "busiType": "EventTriggerSplitNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#FCA400",
    "icon": "#iconyonghuzhongxin-2",
    "shape": "DIAMOND",
    "orders": 1,
    "joinType": "SPLIT",
    "status": "NORMAL",
    "displayName": "触发事件分支",
    "nodeId": 30,
    "fatherIds": [
      31
    ],
    "childrenIds": [
      29
    ],
    "branchIndex": 0,
    "x": 7,
    "y": 9
  },
  {
    "id": 9,
    "groupId": 4,
    "name": "随机人群分支",
    "busiType": "RandomUserSplitNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#FCA400",
    "icon": "#iconsuijifenqun",
    "shape": "DIAMOND",
    "orders": 3,
    "joinType": "SPLIT",
    "status": "NORMAL",
    "displayName": "随机人群分支",
    "nodeId": 31,
    "fatherIds": [
      32
    ],
    "childrenIds": [
      30
    ],
    "branchIndex": 0,
    "x": 6,
    "y": 9
  },
  {
    "id": 13,
    "groupId": 4,
    "name": "嵌个baidu试试",
    "busiType": "IFrameNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#0099FF",
    "icon": "#iconicon_shezhi_",
    "shape": "SQUARE",
    "orders": 1,
    "joinType": "FUNCTION",
    "status": "NORMAL",
    "displayName": "嵌个baidu试试",
    "nodeId": 32,
    "fatherIds": [
      33
    ],
    "childrenIds": [
      31
    ],
    "branchIndex": 0,
    "x": 5,
    "y": 9
  },
  {
    "id": 13,
    "groupId": 4,
    "name": "嵌个baidu试试",
    "busiType": "IFrameNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#0099FF",
    "icon": "#iconicon_shezhi_",
    "shape": "SQUARE",
    "orders": 1,
    "joinType": "FUNCTION",
    "status": "NORMAL",
    "displayName": "嵌个baidu试试",
    "nodeId": 33,
    "fatherIds": [
      34
    ],
    "childrenIds": [
      32
    ],
    "branchIndex": 0,
    "x": 4,
    "y": 9
  },
  {
    "id": 13,
    "groupId": 4,
    "name": "嵌个baidu试试",
    "busiType": "IFrameNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#0099FF",
    "icon": "#iconicon_shezhi_",
    "shape": "SQUARE",
    "orders": 1,
    "joinType": "FUNCTION",
    "status": "NORMAL",
    "displayName": "嵌个baidu试试",
    "nodeId": 34,
    "fatherIds": [
      35
    ],
    "childrenIds": [
      33
    ],
    "branchIndex": 0,
    "x": 3,
    "y": 9
  },
  {
    "id": 13,
    "groupId": 4,
    "name": "嵌个baidu试试",
    "busiType": "IFrameNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#0099FF",
    "icon": "#iconicon_shezhi_",
    "shape": "SQUARE",
    "orders": 1,
    "joinType": "FUNCTION",
    "status": "NORMAL",
    "displayName": "嵌个baidu试试",
    "nodeId": 35,
    "fatherIds": [
      36
    ],
    "childrenIds": [
      34
    ],
    "branchIndex": 0,
    "x": 2,
    "y": 9
  },
  {
    "id": 13,
    "groupId": 4,
    "name": "嵌个baidu试试",
    "busiType": "IFrameNode",
    "fakeable": false,
    "displayInBox": true,
    "color": "#0099FF",
    "icon": "#iconicon_shezhi_",
    "shape": "SQUARE",
    "orders": 1,
    "joinType": "FUNCTION",
    "status": "NORMAL",
    "displayName": "嵌个baidu试试",
    "nodeId": 36,
    "fatherIds": [
      20
    ],
    "childrenIds": [
      35
    ],
    "branchIndex": 0,
    "x": 1,
    "y": 9
  }
]