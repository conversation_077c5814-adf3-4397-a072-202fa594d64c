import React, { useState, useEffect } from 'react';
import { setTheme } from 'wolf-static-cpnt';
import { Select } from 'antd';
import TestFilter from './test/TestFilter';
import TestFlowEditor from './test/TestFlowEditor';
import TestComplex from './test/TestComplex';
import TestLabel from './test/TestLabel';
import TestEvent from './test/TestEvent';
import TestSelectTime from './test/TestSelectTime';
import TestActionCollective from './test/TestActionCollective';
import TestNormCollective from './test/TestNormCollective';
import TestSegment from './test/TestSegment';
import TestOther from './test/TestOther';
import TestCustomize from './test/TestCustomize';

import 'wolf-static-cpnt/dist/index.css';
import 'antd/dist/antd.variable.min.css';
import './App.css';

const Option = Select.Option;
setTheme('#faad14');

// eslint-disable-next-line no-undef
const COMPONENT_MAP: { [key: string]: JSX.Element } = {
  1: <TestComplex />,
  2: <TestFilter />,
  3: <TestFlowEditor />,
  4: <TestLabel />,
  5: <TestSelectTime />,
  6: <TestEvent />,
  7: <TestActionCollective />,
  8: <TestSegment />,
  9: <TestOther />,
  10: <TestNormCollective />,
  11: <TestCustomize />
};

function App() {
  const [flag, setFlag] = useState<string>('3');

  useEffect(() => {
    const storedFlag = localStorage.getItem('flag');
    if (storedFlag) {
      setFlag(storedFlag);
    }
    console.log('刷新成功');
  }, []);

  const handleChange = (value: string) => {
    localStorage.setItem('flag', value);
    setFlag(value);
  };

  return (
    <div>
      <Select value={flag} style={{ width: 220 }} onChange={handleChange}>
        {Object.keys(COMPONENT_MAP).map(key => (
          <Option key={key} value={key}>
            {COMPONENT_MAP[key].type.name}
          </Option>
        ))}
      </Select>
      <div>{COMPONENT_MAP[flag]}</div>
    </div>
  );
}

export default App;
