## <small>1.5.1 (2025-06-25)</small>

* chore: 兼容 调试 ([a988f4d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a988f4d))
* chore: 提升版本 ([ee2335f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ee2335f))
* feat: 分群 时间组件 ([ebd5aac](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ebd5aac))
* feat: 国际化 ([7251bfc](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7251bfc))
* feat: 国际化 ([24ccf06](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/24ccf06))
* feat: cpnt 国际化方案 ([080c953](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/080c953))
* feat: event国际化 ([d8b61d4](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d8b61d4))
* feat: filter label国际化 ([31cd563](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/31cd563))
* feat: grabbing滚动 ([81bddaf](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/81bddaf))
* feat: moment换成dayjs ([ac5a14e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ac5a14e))
* feat: newFilter newEvent ([973ff2b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/973ff2b))



## <small>1.4.47 (2025-02-06)</small>

* feat: grabbing组件改为滚动条 ([b6dc301](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b6dc301))
* fix: 拖动样式 ([edec81a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/edec81a))
* fix: 修复个别机器重复按ctrl ([50cdd18](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/50cdd18))
* chore: 提升45版本 ([5156476](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5156476))



## <small>1.4.44 (2024-12-16)</small>

* chore: 消除forwardRef报错 ([d3b010d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d3b010d))
* feat: 提升43版本 ([91a7aa3](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/91a7aa3))
* feat: 暂存 ([e7a9f4a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e7a9f4a))
* feat: 暂存 ([4dd73c1](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4dd73c1))
* feat: grabbing 性能 拖动限制区域 ([bab0d3b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/bab0d3b))
* feat: grabbing 增加阴影 ([4131d2e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4131d2e))



## <small>1.4.42 (2024-12-05)</small>

* chore: 删除多余目老目录 ([01848be](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/01848be))
* chore: 增加rsbuild调试项目 ([f8c58db](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/f8c58db))
* chore: changelog ([378da3b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/378da3b))
* chore: lock changeLog ([4da1f98](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4da1f98))
* fix: bug ([a37115e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a37115e))
* test ([9bd665d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9bd665d))



## <small>1.4.41 (2024-12-04)</small>

* chore: 代码格式统一prettier 提升版本号 ([5ee0467](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5ee0467))
* chore: 提升版本号 ([814817c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/814817c))
* fix: 标签点击过快导致tagInfo出错 ([be9fc16](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/be9fc16))
* fix: 标签bug ([0616753](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/0616753))
* fix: 分群label操作符缺失问题 ([4e581b0](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4e581b0))
* fix: 分群onChange报错 ([fa80b33](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/fa80b33))
* fix: 自定义分群老数据错误操作符问题 ([dd2c5d0](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/dd2c5d0))
* feat: 标签回显 ([d862d76](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d862d76))
* feat: 限制分群hover ([21b6a65](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/21b6a65))



## <small>1.4.33 (2024-12-04)</small>

* feat: 标签日历去重 ([e3adefc](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e3adefc))
* feat: 标签文案修改 ([3625181](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3625181))
* feat: 分群标签最新值交互调用接口 ([4c67b06](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4c67b06))
* feat: 修改标签过滤 只读交互逻辑，非空样式 ([319d108](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/319d108))
* chore: 提升版本 ([748d2bd](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/748d2bd))
* style: 标签过滤样式 ([5a9ea52](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5a9ea52))



## <small>1.4.29 (2024-11-27)</small>

* feat: 分群标签日历过滤 ([d3a36f8](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d3a36f8))



## <small>1.4.28 (2024-11-27)</small>

* chore: 提升版本到1.4.28 ([6e359d7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6e359d7))
* fix: 标签 过滤组件默认添加高级范围 ([3ac49e2](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3ac49e2))
* update: changelog ([c8ded3d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c8ded3d))



## <small>1.4.27 (2024-11-18)</small>

* fix: grabbing滑动bug ([6b47fb5](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6b47fb5))
* feat: 去除日志 生成changelog ([9ed08cd](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9ed08cd))
* feat: grabbing增加鼠标hover ([adc1db4](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/adc1db4))
* feat: grabbing组件 拖拽冲突、增加buttonTipProps,buttonStyle参数 ([0e7982e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/0e7982e))
* style: grabbing样式 ([b35b903](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b35b903))



## <small>1.4.22 (2024-11-13)</small>

* feat: 提升版本 ([45d112b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/45d112b))
* feat: 完善flowCanvas、flowEditor类型 ([9d5a0ab](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9d5a0ab))
* feat: 增加grabbing组件 画布增加缩放功能 ([d9b1155](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d9b1155))
* chore: 修改eslint 配置 parser 改为ts ([a16e53e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a16e53e))



## <small>1.4.21 (2024-10-22)</small>

* fix: 行为聚合分群-行为刷新问题 ([272fd5c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/272fd5c))



## <small>1.4.20 (2024-10-16)</small>

* feat: 隐藏标签计算 ([8ebcf61](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8ebcf61))



## <small>1.4.19 (2024-10-12)</small>

* feat: 标签搜索报错问题 ([568bd29](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/568bd29))



## <small>1.4.18 (2024-10-09)</small>

* fix:修复标签相对时间切换类型数值问题 ([af31879](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/af31879))



## <small>1.4.17 (2024-09-27)</small>

* fix:修改计算标签相对时间仅分群和画布 ([5fcd283](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5fcd283))
* fix:修改计算标签相对时间仅分群和画布 ([0eb3281](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/0eb3281))
* chore: 修改命令 ([88f6de7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/88f6de7))



## <small>1.4.16 (2024-09-26)</small>

* fix: 事件图表接口增加判空逻辑 ([1fc952c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1fc952c))



## <small>1.4.15 (2024-09-25)</small>

* fix:修复标签相对时间 ([349f1c3](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/349f1c3))



## <small>1.4.14 (2024-09-24)</small>

* fix:画布标签分支string类型标签符隐藏全部 ([521b898](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/521b898))



## <small>1.4.13 (2024-09-23)</small>

* fix:修复画布标签为非相对时间时timetype取值问题 ([31d5f3f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/31d5f3f))



## <small>1.4.12 (2024-09-23)</small>

* fix:修复画布标签分支标签计算传参问题 ([d70b769](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d70b769))
* fix: 农商行环境标签相对时间最大值设置问题 ([734a89c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/734a89c))



## <small>1.4.11 (2024-09-19)</small>

* fix: 复合分群hover复合分群循环引用 ([4807580](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4807580))
* fix: 聚合分群-添加分群筛选hover复合弹窗样式 ([d7aeae1](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d7aeae1))
* fix: 聚合分群-添加分群筛选hover复合分群弹窗-排除分群 ([2d22fdd](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/2d22fdd))
* fix: 聚合分群-添加分群筛选hover聚合弹窗修改 ([b84d71b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b84d71b))
* fix: 农商行环境标签相对时间最大值设置问题 ([76d4439](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/76d4439))
* fix: 事件弹窗图表接口修改为使用透传接口 ([9ed392a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9ed392a))
* fix: filterValue报错 ([3b0347e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3b0347e))
* fix: ml-matrix依赖问题 ([7c8ee51](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7c8ee51))
* chore: 修改版本号 ([faf8eb7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/faf8eb7))
* chore: 修改远程仓库地址 增加changelog ([cde9072](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/cde9072))
* chore: changelog ([b0a2923](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b0a2923))
* feat:标签相对时间修改展示 ([8154427](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8154427))
* feat:标签相对时间增加活动参数 ([71b48cf](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/71b48cf))
* feat:区分不同环境设置最大输入值 ([1ad8bf4](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1ad8bf4))
* feat:相对时间增加字段传递 ([a000c62](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a000c62))
* feat:相对时间增加n年n月 ([4089903](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4089903))
* feat:修改流程画布标签节点兼容相对时间 ([ff05d09](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ff05d09))
* feat:version up ([11172c4](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/11172c4))
* fix：修改内容且格式化代码 ([4ff8b8b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4ff8b8b))
* up version ([4f83d57](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4f83d57))
* up version ([35edb19](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/35edb19))
* version up^1 ([95d85bf](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/95d85bf))
* refactor: 优化时间切片显示逻辑、时间切片详情页回显 ([e8638f5](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e8638f5))
* feat: 事件过滤增加BOOL类型、增加types ([c674437](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c674437))
* feat: 提升版本 ([f289c1f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/f289c1f))
* feat: 修改Complex、ActionCollective类型 ([d75d424](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d75d424))
* feat: 引入 TypeScript ([5792eec](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5792eec))
* feat: 增加antCharts依赖 ([d0a0d28](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d0a0d28))
* feat: sass版本 ([826a1ac](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/826a1ac))
* eslint: fix ([60d48a6](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/60d48a6))
* lint: 格式化代码 ([9e1b851](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9e1b851))



## <small>1.3.76 (2024-08-22)</small>

* fix: 版本 ([7212ec1](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7212ec1))
* fix: 包含/不包含改为多选输入框 ([61585d7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/61585d7))
* fix: 标签 属性报错 ([490164e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/490164e))
* fix: 标签、过滤操作符map改为getter、解决children报错 ([bfbd303](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/bfbd303))
* fix: 标签分支抖动问题 ([f1b2c3e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/f1b2c3e))
* fix: 标签分支校验 ([b986e7b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b986e7b))
* fix: 标签过滤 ([db953e9](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/db953e9))
* fix: 标签过滤去掉多余字段 ([b5e95da](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b5e95da))
* fix: 标签后最新计算时间问题修改 ([5a1222e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5a1222e))
* fix: 标签接口code改为path ([67da86c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/67da86c))
* fix: 标签筛选增加showValue显示 ([bcc07b3](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/bcc07b3))
* fix: 标签筛选增加showValue显示 ([5760b1c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5760b1c))
* fix: 标签时间修改 ([cacd682](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/cacd682))
* fix: 标签组件更改老最近计算时间显示 ([d30eea9](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d30eea9))
* fix: 标签组件重复输入优化 ([a63a414](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a63a414))
* fix: 标签组件bug ([6941679](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6941679))
* fix: 标签最新值bug ([8fa958a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8fa958a))
* fix: 标签bug ([0e482bf](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/0e482bf))
* fix: 标签showValue bug ([c2eccf2](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c2eccf2))
* fix: 标签Tree组件虚拟高度性能优化 ([e65f67f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e65f67f))
* fix: 测试 ([d958ded](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d958ded))
* fix: 测试 ([7612e45](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7612e45))
* fix: 除流程画布隐藏标签计算 ([91b3031](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/91b3031))
* fix: 单独设置样式 ([b9b8c17](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b9b8c17))
* fix: 分隔符去掉句号 ([cb0d583](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/cb0d583))
* fix: 分隔符去重 ([01192a3](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/01192a3))
* fix: 分隔符增加分号 ([e80b50e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e80b50e))
* fix: 过滤组件多选Select下拉回弹问题 ([08154c6](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/08154c6))
* fix: 过滤组件多选Select下拉回弹问题 ([144d9cd](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/144d9cd))
* fix: 过滤组件优化 ([3625474](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3625474))
* fix: 过滤组件只读状态 回显bug ([e3a083f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e3a083f))
* fix: 还原 ([86ca7cf](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/86ca7cf))
* fix: 还原 ([055961e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/055961e))
* fix: 会话节点重复值显示问题 ([24f5ea0](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/24f5ea0))
* fix: 计算时间bug ([87d412b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/87d412b))
* fix: 解决指标组件更改条件时自定义名称被清空的bug ([ab6a879](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ab6a879))
* fix: 解决filter组件错误信息显示bug ([7feec1e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7feec1e))
* fix: 匹配等detail多个显示分割 ([5ce18b2](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5ce18b2))
* fix: 清除console ([46694a5](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/46694a5))
* fix: 去除50条限制 ([c93c8de](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c93c8de))
* fix: 去除次数默认值 ([b1b1e32](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b1b1e32))
* fix: 时间切片 移入不显示值 ([022761a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/022761a))
* fix: 时间切片缓存、标签计算样式 ([71e349a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/71e349a))
* fix: 时间切片children报错 ([fed4dce](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/fed4dce))
* fix: 提升版本 ([50da726](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/50da726))
* fix: 提升版本 ([a2abc6c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a2abc6c))
* fix: 文案修改 ([9fc9e02](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9fc9e02))
* fix: 修复事件属性验证问题 ([73bbc8e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/73bbc8e))
* fix: 修复自定义名称为空时能保存的bug ([97715d4](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/97715d4))
* fix: 修复filter组件范围时间控件不回显的bug ([6808d22](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6808d22))
* fix: 修改标签过滤组件 TIMESTAMP类型 ([b5bb40e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b5bb40e))
* fix: 修改传入bug ([3dcb1ef](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3dcb1ef))
* fix: 修改过滤组件 有值空值时无法触发change事件 ([c1e238f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c1e238f))
* fix: 修改活动流程组件 拿不到value ([a1e1bd5](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a1e1bd5))
* fix: 修改下拉箭头 ([0a3d2d6](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/0a3d2d6))
* fix: 修改select 下拉宽度 ([72c66c7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/72c66c7))
* fix: 用户标签保存后最新值回显bug ([6706980](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6706980))
* fix: 用户标签增加showValue显示 ([35624a3](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/35624a3))
* fix: 用户标签组件回显bug ([199bc9b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/199bc9b))
* fix: 用户标签bug ([0849d75](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/0849d75))
* fix: 用户分群标签最近计算时间回显bug ([e7c7a0e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e7c7a0e))
* fix: 用户分群时间切片 枚举值 ([2a76c16](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/2a76c16))
* fix: 用户分群详情detail最新计算时间问题 ([96b7e1d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/96b7e1d))
* fix: 增加Tooltip提示在分隔符输入选项中 ([a6fdc5c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a6fdc5c))
* fix: 指标bug修改测试 ([387baae](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/387baae))
* fix: 最近计算时间回显问题 ([b0601f7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b0601f7))
* fix: 最近计算时间默认显示- ([9b9b870](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9b9b870))
* fix: 最新值判断 ([24ac009](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/24ac009))
* fix: Ant Design 升级修改popover Modal 时间组件 ([e2108a8](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e2108a8))
* fix: babel ([7f4d233](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7f4d233))
* fix: bug ([a268053](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a268053))
* fix: bug ([968ed47](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/968ed47))
* fix: bug ([c70fddc](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c70fddc))
* fix: bug ([82970e0](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/82970e0))
* fix: bug ([ed06da5](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ed06da5))
* fix: bug ([69265d9](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/69265d9))
* fix: disable状态 ([9dd401d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9dd401d))
* fix: filter、label值验证优化 ([a2af49a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a2af49a))
* fix: Filter、Label组件匹配、包含等操作符下值情况验证问题优化 ([a2a398e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a2a398e))
* fix: fix ([adf1716](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/adf1716))
* fix: fix ([5606775](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5606775))
* fix: fix ([537ecdc](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/537ecdc))
* fix: fix标签筛选bug ([3eaf232](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3eaf232))
* fix: message ([3edca42](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3edca42))
* fix: tooltip方向 ([203290c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/203290c))
* fix: value回显 ([71412c6](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/71412c6))
* fix: value问题 ([bdfd2bf](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/bdfd2bf))
* fix: version ([389762c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/389762c))
* style: 标签更新时间文案修改 ([affd125](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/affd125))
* style: 标签加载loading位置 ([dc72b09](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/dc72b09))
* style: 标签组件样式 ([ab37600](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ab37600))
* style: 标签组件样式优化 ([087a32c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/087a32c))
* style: 标签组件样式优化 ([3813fa1](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3813fa1))
* style: 分隔符样式 ([1f5e5ab](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1f5e5ab))
* style: 分群hover样式问题 ([bdf9638](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/bdf9638))
* style: 去除console ([fea7cbd](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/fea7cbd))
* style: 去除console ([7ee845a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7ee845a))
* style: 去除console ([6b0d80d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6b0d80d))
* style: 文案 ([010147d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/010147d))
* style: 文案修改 ([ebbc42d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ebbc42d))
* style: 修改初始loading效果 ([f008ba7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/f008ba7))
* style: 样式修改 ([36e4445](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/36e4445))
* style: 用户标签多层级下文字过长断行问题 ([a6b4974](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a6b4974))
* style: 增加下padding ([1fae360](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1fae360))
* style: 指标button位置更改 ([9d2b29e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9d2b29e))
* style: 总合文案修改 ([329643b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/329643b))
* style: flex-start ([9b74414](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9b74414))
* style: icon更新 ([e5efbe5](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e5efbe5))
* feat: 版本提升 ([0a74006](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/0a74006))
* feat: 标签、属性增加分隔符 ([81bfc48](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/81bfc48))
* feat: 标签过滤组件增加时间切片 ([e08dd39](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e08dd39))
* feat: 标签计算覆盖人数 ([b625f49](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b625f49))
* feat: 标签计算结果校验 ([d367e38](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d367e38))
* feat: 标签增加分隔符 ([761ba1a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/761ba1a))
* feat: 标签组件增加日期显示 ([a806488](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a806488))
* feat: 分隔符 ([62b1412](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/62b1412))
* feat: 去除画布多次刷新依赖 ([9b6c256](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9b6c256))
* feat: 升级ant design版本 ([3167565](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3167565))
* feat: 时间切片 限制为用户分群 ([50d0023](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/50d0023))
* feat: 时间切片改为Dropdown ([a4bda7c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a4bda7c))
* feat: 时间切片枚举值 ([86a4cfc](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/86a4cfc))
* feat: 事件、分群增加信息弹窗 ([887949a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/887949a))
* feat: 行为聚合分群分群增加Popover ([028f6f1](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/028f6f1))
* feat: 行为聚合分群增加字段 ([d3103b8](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d3103b8))
* feat: 用户标签备注字段 ([e4b5c77](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e4b5c77))
* feat: 用户标签新增最新值即最近计算时间、下拉箭头点击优化 ([6d9315b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6d9315b))
* feat: 用户标签增加类型文案 ([ab0f6b7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ab0f6b7))
* feat: 用户分群时间切片 枚举值 ([22ba9a0](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/22ba9a0))
* feat: 用户属性时间切片、标签计算 ([1e1c010](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1e1c010))
* feat: 增加动态主题色 ([96178e6](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/96178e6))
* feat: 增加时间切片校验 ([fe9cd88](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/fe9cd88))
* feat: Filter组件 ([ce3613b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ce3613b))
* feat: switch 增加 主题色 ([fcefb5b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/fcefb5b))
* feat: version ([52bb0af](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/52bb0af))
* chore: 提升版本 ([2d30a82](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/2d30a82))
* chore: 提升版本 ([ea14d0e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ea14d0e))
* chore: 提升版本 ([38e8563](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/38e8563))
* chore: 提升版本 ([680c2ea](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/680c2ea))
* chore: 提升版本 ([e1fc54a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e1fc54a))
* chore: 提升版本 ([ec82632](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ec82632))
* chore: 修改版本号 ([3022290](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3022290))
* chore: 修改版本号 ([31dfd82](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/31dfd82))
* 把标签筛选搜索的结果自动展开 ([dee8421](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/dee8421))
* 版本升级 ([ea74779](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ea74779))
* 标签时间开发 ([bc55d4d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/bc55d4d))
* 测试 ([ac2a05c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ac2a05c))
* 测试提交 ([eb8f59a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/eb8f59a))
* 等待时长V2回显优化 ([eb41201](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/eb41201))
* 等待市场V2增加回显 ([da6da46](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/da6da46))
* 调试 ([1608881](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1608881))
* 改变标签筛选为默认不显示第一条 ([41b2880](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/41b2880))
* 跟新react到v3的最新版本 ([7b5be3d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7b5be3d))
* 更新 ([9d1ac48](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9d1ac48))
* 更新 ([e195178](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e195178))
* 更新了版本号 ([4765bfe](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4765bfe))
* 解决添加条件个数的限制 ([8443cef](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8443cef))
* 快捷选项添加dispatch ([9666ffb](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9666ffb))
* 去除console ([a5a0808](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a5a0808))
* 日志级别 ([37f3bf1](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/37f3bf1))
* 删除无用代码 ([0dd51da](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/0dd51da))
* 删除package-lock ([5acbcd7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5acbcd7))
* 升版本了 ([bf5e41e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/bf5e41e))
* 升级版本 ([20898d5](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/20898d5))
* 升级包版本 ([e463c8e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e463c8e))
* 升级了版本 ([80aed61](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/80aed61))
* 升了版本到173 ([c70568b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c70568b))
* 时间修改 ([b24a973](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b24a973))
* 时间组件 快捷选项封装交互 ([c8ceed3](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c8ceed3))
* 时间组件逻辑修改 ([c11be1a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c11be1a))
* 时间组件逻辑修改 ([9cac414](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9cac414))
* 时间组件校验 ([1526b7f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1526b7f))
* 时间组件修改 ([a8e8895](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a8e8895))
* 时间组件增加参数 ([1193709](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1193709))
* 时间组件增加change事件 ([00e0c86](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/00e0c86))
* 适配新的数据类型 ([dd50474](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/dd50474))
* 提交图片 ([c7bd09e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c7bd09e))
* 提升版本 ([24b9c7a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/24b9c7a))
* 提升版本 ([8572074](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8572074))
* 提升版本 ([d415224](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d415224))
* 提升版本 ([7dd64bb](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7dd64bb))
* 提升版本 ([0f5d70f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/0f5d70f))
* 提升版本 ([db96f96](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/db96f96))
* 提升版本 ([8463efd](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8463efd))
* 提升版本 ([c6009ec](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c6009ec))
* 提升版本 ([7c0e5e4](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7c0e5e4))
* 提升版本 ([f7e4a3d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/f7e4a3d))
* 提升版本 ([65591cd](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/65591cd))
* 提升版本 ([8d72494](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8d72494))
* 提升版本 ([248300c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/248300c))
* 提升版本 ([b4f1af2](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b4f1af2))
* 提升版本 ([aef8866](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/aef8866))
* 提示版本 ([ed7f0d3](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ed7f0d3))
* 添加了一个__fixFlows() 不过貌似没什么用 ([8a00b06](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8a00b06))
* 完成了流程配置组件的一些修改 ([ed0ece6](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ed0ece6))
* 新增加了label 和 customize 组件 ([c0d03e8](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c0d03e8))
* 修复0级下的分类靠左的问题 ([773fe04](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/773fe04))
* 修复报endsWith错误的问题 ([4e4d488](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4e4d488))
* 修复了Complex 的bug ([8d0e0af](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8d0e0af))
* 修复升级造成的bug ([884bb5c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/884bb5c))
* 修复时间戳精度的问题 ([9b7a0ed](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9b7a0ed))
* 修复图片不显示的bug ([f948668](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/f948668))
* 修复完成了选择时间组件 ([fea91e8](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/fea91e8))
* 修复下拉箭头 ([e171506](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e171506))
* 修复自定义筛选下拉框不收起的bug ([91fea73](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/91fea73))
* 修复自定义组件显示bug ([1e87553](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1e87553))
* 修复组件撤销操作错乱的bug ([b332545](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b332545))
* 修复bug ([c62306e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c62306e))
* 修改 ([5f74336](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5f74336))
* 修改 ([caaa14b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/caaa14b))
* 修改 ([4ef7e44](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4ef7e44))
* 修改 ([6268d95](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6268d95))
* 修改 ([3036c4d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3036c4d))
* 修改 ([b38a4af](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b38a4af))
* 修改 ([a25a28b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a25a28b))
* 修改 ([39d5c00](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/39d5c00))
* 修改 ([69a51d2](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/69a51d2))
* 修改 ([2264ad7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/2264ad7))
* 修改 ([ea4be71](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ea4be71))
* 修改 ([8bbf4bf](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8bbf4bf))
* 修改 ([2f8895c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/2f8895c))
* 修改 ([bfe2134](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/bfe2134))
* 修改 ([8410d0e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8410d0e))
* 修改 ([d35fcab](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d35fcab))
* 修改 ([4083f94](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4083f94))
* 修改 ([b0f4e83](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b0f4e83))
* 修改 ([570ea78](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/570ea78))
* 修改 ([3f0667f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3f0667f))
* 修改 ([263d49c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/263d49c))
* 修改 ([4b0dc7c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4b0dc7c))
* 修改 ([c0c37fc](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c0c37fc))
* 修改 ([d966524](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d966524))
* 修改 ([85a77dc](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/85a77dc))
* 修改 ([9e0f32d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9e0f32d))
* 修改 ([1bd1a88](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1bd1a88))
* 修改 ([a480f5b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a480f5b))
* 修改 ([e67e84e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e67e84e))
* 修改 ([1a73856](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1a73856))
* 修改 ([9d5262e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9d5262e))
* 修改 ([dc16751](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/dc16751))
* 修改 ([f133ae4](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/f133ae4))
* 修改 ([bbdacf5](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/bbdacf5))
* 修改 ([afd753e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/afd753e))
* 修改 ([0d19e44](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/0d19e44))
* 修改 ([35e1b3d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/35e1b3d))
* 修改版本 ([922e909](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/922e909))
* 修改版本 ([61a67c1](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/61a67c1))
* 修改版本 ([eafc3c4](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/eafc3c4))
* 修改标签标记节点的文本显示 ([4d452dd](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4d452dd))
* 修改标签标签文本的展示 ([b5dcf0b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b5dcf0b))
* 修改标签值可输入的问题 ([656503d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/656503d))
* 修改标签值显示过多的问题 ([cec8055](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/cec8055))
* 修改标签组件 ([ce72f38](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ce72f38))
* 修改标签组件 ([3b358d2](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3b358d2))
* 修改标签组件 ([74a0d74](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/74a0d74))
* 修改部分聚合分群bug ([8ca33e9](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8ca33e9))
* 修改多入口 ([bc71337](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/bc71337))
* 修改滚动条样式 ([6b5ff13](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6b5ff13))
* 修改过滤组件 ([2c16444](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/2c16444))
* 修改过滤组件 ([ff30f13](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ff30f13))
* 修改过滤组件 ([be8b44e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/be8b44e))
* 修改过滤组件，新增聚合过滤组件 ([008feb7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/008feb7))
* 修改过滤组件删除 ([051324a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/051324a))
* 修改画布合并辅助分支后节点删除导致的没有退出节点，增加在合并辅助分支前插入节点提供选择功能，修复其它bug ([384e4cb](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/384e4cb))
* 修改活动节点名称长显示不全的问题 ([726939c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/726939c))
* 修改交并集按钮 ([a93d7dd](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a93d7dd))
* 修改聚合分群 ([911578b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/911578b))
* 修改聚合分群 ([f0a95fc](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/f0a95fc))
* 修改聚合分群 ([2f53b53](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/2f53b53))
* 修改聚合分群 ([72a0bf7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/72a0bf7))
* 修改聚合分群相关功能 ([e5bd2f4](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e5bd2f4))
* 修改聚合分群样式 ([6937700](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6937700))
* 修改聚合分群组件 ([a28c9b0](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a28c9b0))
* 修改了几处关于验证的地方 ([587c66f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/587c66f))
* 修改了图片 ([f50fb1d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/f50fb1d))
* 修改了下拉框不跟着页面滑动的问题 ([c86901b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c86901b))
* 修改了package.json 的版本 ([578fd58](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/578fd58))
* 修改了package.json 的版本 ([fcc8388](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/fcc8388))
* 修改流程图历史问题 ([486068f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/486068f))
* 修改删除的问题 ([6f8c709](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6f8c709))
* 修改删除多个分支报错的问题 ([a015e2d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a015e2d))
* 修改时间组件弹出方式 ([0a6855c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/0a6855c))
* 修改显示问题 ([1ae1046](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1ae1046))
* 修改行为聚合组件 ([19d4155](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/19d4155))
* 修改样式 ([6c248fb](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6c248fb))
* 修改值不能为0的bug ([de388a3](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/de388a3))
* 修改自定义分群组件 ([72af84d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/72af84d))
* 修改bug ([5031cd6](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5031cd6))
* 修改bug ([def2811](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/def2811))
* 修改dropdown下拉箭头不能触发展开的问题 ([e2ced61](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e2ced61))
* 修改label ([6376ccd](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6376ccd))
* 修改label组件 ([3753c5c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3753c5c))
* 修改label组件变英文的问题 ([cbe9737](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/cbe9737))
* 优化自定义筛选组件 ([f4e210e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/f4e210e))
* 优化自定义筛选组件 ([eba2048](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/eba2048))
* 优化自定义组件 ([1fc26d1](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1fc26d1))
* 优化自定义组件样式 ([6d079ae](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6d079ae))
* 增加标签组件的displayName ([da700a8](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/da700a8))
* 增加箭头 ([5634b14](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5634b14))
* 增加节点两行文本限制 ([2ba603e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/2ba603e))
* 增加了 Complex ([3d187b1](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3d187b1))
* 增加了相对时间 ([adb50a9](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/adb50a9))
* 增加了选择时间组件 ([1c2339b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1c2339b))
* 增加了选择label组件 ([7eb2c40](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7eb2c40))
* 增加时间快捷选项,selectTime分析专用 ([374c902](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/374c902))
* 增加校验 ([042263b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/042263b))
* 增加指定时间结果展示 ([baf159c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/baf159c))
* 增加SelectTimeV2 ([da6b701](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/da6b701))
* 增加tagText的显示 ([03390ee](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/03390ee))
* 增加TestCustomize ([643d646](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/643d646))
* 指标组件开发完成-v1.0.248 ([cd39e5b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/cd39e5b))
* 自定义筛选草稿状态下下拉框不收起的bug ([51862fd](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/51862fd))
* 自定义筛选组件修复测试 ([1bcb964](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1bcb964))
* 自定义筛选组件placeholder优化 ([250ded7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/250ded7))
* ADD git ([de5b973](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/de5b973))
* ant ([c05cb4d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c05cb4d))
* antd 升级v4 ([2349750](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/2349750))
* antd版本升级测试 ([75dfbd5](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/75dfbd5))
* bug fix ([727d2c9](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/727d2c9))
* bug fix ([6c9e87b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6c9e87b))
* bug fix ([3e787a1](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3e787a1))
* bug fix ([6f7e150](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6f7e150))
* bug fix ([871c043](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/871c043))
* bug fix ([8f8a50b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8f8a50b))
* bug fix ([1c3ba7f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1c3ba7f))
* bug fix ([22daa5c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/22daa5c))
* bug fix ([e5863a9](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e5863a9))
* bug fix ([3fe8577](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3fe8577))
* bug fix ([fceb2db](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/fceb2db))
* bug fix ([de75764](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/de75764))
* bug fix ([27a3d50](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/27a3d50))
* bug fix ([d28a458](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d28a458))
* bug fix ([18e0d91](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/18e0d91))
* bug fix ([43bf090](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/43bf090))
* bug fix ([3eb180a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3eb180a))
* bug fix ([3b48f81](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3b48f81))
* bug fix ([9693f02](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/9693f02))
* bug fix ([4cf468d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4cf468d))
* BUG FIX ([3abfa1b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3abfa1b))
* COMPLETE ([a8dea03](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a8dea03))
* COMPLETE ([a130a28](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a130a28))
* COMPLETE ([19fb865](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/19fb865))
* COMPLETE ([63c0f4c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/63c0f4c))
* COMPLETE ([409c7fc](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/409c7fc))
* complex 组件做了一些调整， ([362af92](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/362af92))
* filter ([93d9e03](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/93d9e03))
* filter ([7e02177](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7e02177))
* filter ([4b4adf6](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4b4adf6))
* filter ([7f3a4f8](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7f3a4f8))
* filter ([3f0f2d9](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3f0f2d9))
* filter ([640324f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/640324f))
* fix bug ([33318c9](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/33318c9))
* FIX-BUG: 当过滤属性是枚举，操作符是包含时，showValue被置空的问题 ([6524c8a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6524c8a))
* FIX-BUG: 多次请求fileter ([84126d6](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/84126d6))
* FIX-BUG: 多次请求fileter ([41e7438](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/41e7438))
* FIX-BUG: 删除节点错误 ([200ffe5](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/200ffe5))
* FIX-BUG: 删除节点错误 ([0aa4908](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/0aa4908))
* FIX-BUG: 删除节点错误 ([bf4064b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/bf4064b))
* FIX-BUG: 修复过滤过短的问题 ([4d16848](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4d16848))
* FIX-BUG: field fieldName搞混 ([b5a9d9e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b5a9d9e))
* FIX-BUG: schemeId => schemaId ([470f397](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/470f397))
* fixbug ([c780ec0](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c780ec0))
* History ([5df34ac](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5df34ac))
* History ([3288a04](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3288a04))
* init ([b8b314e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b8b314e))
* init ([be6defd](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/be6defd))
* Reapply "feat: 标签分支默认值" ([109a79f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/109a79f))
* Reapply "fix: 标签分支默认值引用活动信息" ([dc6776a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/dc6776a))
* Reapply "fix: 标签依赖" ([f47effc](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/f47effc))
* Reapply "fix: bug" ([bcc9161](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/bcc9161))
* SelectTimeV2 优化 ([fbcb546](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/fbcb546))
* SelectTimeV2修改 ([f6dba45](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/f6dba45))
* SelectTimeV2增加快捷提示 ([d553938](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d553938))
* test ([208873e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/208873e))
* test ([d02b568](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d02b568))
* test ([b69c39b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b69c39b))
* test ([6bcc9d8](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6bcc9d8))
* test ([2f41a09](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/2f41a09))
* test ([e600f39](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e600f39))
* test ([dd64ce3](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/dd64ce3))
* test ([5805213](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5805213))
* test ([8a5cb22](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8a5cb22))
* test ([985b5e8](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/985b5e8))
* test ([74b9b47](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/74b9b47))
* test ([39c987f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/39c987f))
* test ([a381ad8](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a381ad8))
* test:调试 ([99ef113](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/99ef113))
* test：console测试 ([2335e4e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/2335e4e))
* test:test ([41606a1](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/41606a1))
* Update config.js ([e53cad4](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e53cad4))
* Update FilterValue.js ([14e6ea7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/14e6ea7))
* Update package.json ([2fbcc6d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/2fbcc6d))
* Update package.json ([6ccbe9d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6ccbe9d))
* Update package.json ([f9b868c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/f9b868c))
* Update package.json ([795d7a3](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/795d7a3))
* Update package.json ([3ffb05c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3ffb05c))
* cli: yarn改为用pnpm ([b171ed3](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b171ed3))
* test: 标签异步loading样式优化测试 ([a28bcc0](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a28bcc0))
* test: 测试 ([681efcd](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/681efcd))
* test: 测试 ([39fe10c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/39fe10c))
* test: 测试 ([8d9e21e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8d9e21e))
* test: 调试 ([ef8b9e7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ef8b9e7))
* test: 分群详情弹窗 ([2c40ab9](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/2c40ab9))
* test: 聚合分群筛选分群增加详情弹窗 ([5dd74ca](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5dd74ca))
* test: 增加showValue ([429f730](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/429f730))
* test: console ([0c16bcd](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/0c16bcd))
* test: console ([327737c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/327737c))
* test: console ([21d010c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/21d010c))
* test: console测试 ([6b567ab](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6b567ab))
* test: test ([7648ffc](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7648ffc))
* test: test ([3e68837](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3e68837))
* test: test ([103b0df](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/103b0df))
* test: test ([75145a3](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/75145a3))
* test: test ([45154e0](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/45154e0))
* test: test ([79a43c0](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/79a43c0))
* test: test ([86aa910](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/86aa910))
* test: test ([b1e8bfa](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b1e8bfa))
* test: test ([dee84a2](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/dee84a2))
* test: test ([5219725](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5219725))
* test: test ([fb79755](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/fb79755))
* test: test ([0f5c92b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/0f5c92b))
* test: test ([187e491](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/187e491))
* test: testing ([836f00f](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/836f00f))
* update: 计算人数取lastCalcTime ([f8f344c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/f8f344c))
* update: 匹配/不匹配多选值 ([cac1be7](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/cac1be7))
* update: 文字变更 ([7280dae](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7280dae))
* update: 文字变更 ([71c5e4e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/71c5e4e))
* update: 用户标签最新值排序修改 ([1654ee6](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1654ee6))
* update: base64图片改为 Switch ([1d1f014](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1d1f014))
* change: 标签值下拉数量限制50个 ([25cf822](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/25cf822))
* change: 更改 ([ceb3e75](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ceb3e75))
* change: 回复antd版本 ([d7489e5](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d7489e5))
* change: 去除console ([c28c617](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c28c617))
* change: 增加指标组件测试页面 ([61577f8](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/61577f8))
* change: 增加tooltip ([ab54230](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ab54230))
* change: 指标组件类型框优化 ([66eb83c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/66eb83c))
* change: 指标组件类型判断增加类型 ([caed9da](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/caed9da))
* change: 指标组件默认filter为空时显示添加指标按钮 ([ae0b89b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ae0b89b))
* change: 指标组件限制36字符 ([d460c15](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/d460c15))
* change: 指标组件指标项增加类型判断 ([926889d](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/926889d))
* change: change ([140ca80](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/140ca80))
* change: count更改为VALUE_COUNT ([790b471](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/790b471))
* feature: 标签值更改 ([6b8c9d4](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/6b8c9d4))
* feature: 增加指标组件 ([e3c668c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e3c668c))
* Feature: 标签值优先展示 ([25b56b9](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/25b56b9))
* Feature: 标签值优先展示 ([5203eeb](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5203eeb))
* Feature: 筛选值增加前后空格提示判断 ([50d17d3](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/50d17d3))
* FEATURE: 节点拖动到目标点，改变图表 ([5f18c53](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/5f18c53))
* FEATURE: 可以拖动一进一出的节点，并复制这个节点 ([3d90108](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/3d90108))
* FEATURE: 删除一出一进的节点，只删除自己，不再递归删除 ([33ff56c](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/33ff56c))
* FEATURE: 拖拽 ([8bafbe3](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/8bafbe3))
* OPT: 提升版本号 ([b69e626](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/b69e626))
* OPT: 优化过滤组件宽度自适应，优化流程组件的历史记录性能 ([e7ee31e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/e7ee31e))
* OPT: 优化流程节点组件显示nOPT:优化过滤组件获取属性列表 ([77ec88a](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/77ec88a))
* OPT: 优化流程节点组件显示nOPT:优化过滤组件获取属性列表 ([098b33e](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/098b33e))
* OPT: 优化流程节点组件显示nOPT:优化过滤组件获取属性列表 ([a200aba](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/a200aba))
* OPT: 优化流程节点组件显示nOPT:优化过滤组件获取属性列表 ([4667980](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/4667980))
* OPT: 优化组件宽度自适应 ([64602f8](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/64602f8))
* OPT: init ([0cf1bce](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/0cf1bce))
* OPT: init ([388a546](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/388a546))
* OPT: package 修改 ([81d77fc](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/81d77fc))
* OPT: package 修改 ([1eda916](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/1eda916))
* OPT: package 修改 ([972e2c8](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/972e2c8))
* FEAUTRE: 可以拖动合并后的节点 ([ced3c3b](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/ced3c3b))
* FEAUTRE: add, insert, split 节点 ([7b65857](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/7b65857))
* FEAUTRE: add, insert, split 节点 ([c50db62](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/c50db62))
* FEAUTRE: add, insert, split 节点 ([166c417](https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt/commit/166c417))



