import React, { useRef, useState, useEffect } from 'react';
import { Button, Tooltip } from 'antd';
import _ from 'lodash';
import './index.scss';
import { BoxData, GrabbingBoxProps } from './types';
import { AimOutlined, QuestionCircleOutlined } from '@ant-design/icons';

/**
 * 判断是否桌面端
 */
function isPC(): boolean {
  const userAgentInfo = navigator.userAgent;
  const agents = ['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod'];
  return !agents.some((n) => userAgentInfo.includes(n));
}

/**
 * 阻止移动端浏览器的默认滑动（如下拉刷新与左划回退）
 */
const disableDefaultBehavior = (event: Event) => {
  event.preventDefault();
};

const GrabbingBox = (props: Omit<GrabbingBoxProps, 'mode'>) => {
  const {
    maxScale = 200,
    minScale = 20,
    initScale = 100,
    scaleButtons = true,
    scaleButtonsPosition = 'top right',
    scaleButtonsSpaceX = '10px',
    scaleButtonsSpaceY = '20px',
    scaleStep = 10,
    limitDistance = 200,
    buttonStyle = {
      position: 'absolute'
    },
    buttonTipProps = {
      overlayClassName: 'grabbing-box-button-tip',
      getPopupContainer: () => document.querySelector('.grabbing-box-container') as HTMLElement,
      trigger: 'hover',
      overlayStyle: { maxWidth: '600px' },
      title: (
        <div>
          拖动画布：按住ctrl+ 鼠标左键; 按住ctrl+按住触摸板滑动;
          <br />
          放大缩小画布：鼠标滚轮+ctrl; 双指开合触摸板; 画布的【+】【-】按钮; <br />
          重置定位：点击画布右下角的重置按钮，将画布恢复到初始位置和比例。
        </div>
      )
    },
    onZoomIn,
    onZoomOut,
    onReset,
    onMove,
    onUpdate,
    children
    // throttleSpan = 50,
  } = props;
  const containerRef = useRef<any>(null);
  const contentBoxRef = useRef<any>(null);
  const [lastTransformData, setLastTransformData] = useState<BoxData>({ scale: 1, translateX: 0, translateY: 0 });
  const lastPositionRef = useRef({ x: 0, y: 0, distance: 0 });
  const [readyToDrag, setReadyToDrag] = useState<boolean>(false);
  const [dragging, setDragging] = useState<boolean>(false);
  const scaling = Math.round(lastTransformData.scale * 100);

  const [readyToScale, setReadyToScale] = useState<boolean>(false); // 移动端
  const [scaleTranslateProportion, setScaleTranslateProportion] = useState([0, 0]); // 缩放导致偏移的比例

  const safeScaleButtonsPosition = {
    top: scaleButtonsPosition.includes('top'),
    bottom: scaleButtonsPosition.includes('bottom'),
    left: scaleButtonsPosition.includes('left'),
    right: scaleButtonsPosition.includes('right')
  };

  // 将boundaryRef移到组件顶层
  const boundaryRef = useRef({
    rightDistance: 0,
    leftDistance: 0,
    bottomDistance: 0,
    topDistance: 0
  });

  // 写一个状态 防止个别键盘重复调用
  const ctrlPressedRef = useRef<boolean>(false);

  useEffect(() => {
    if (maxScale < minScale) {
      throw new Error(`[Props Error]maxScale should greater than minSacle,
          [Props 错误] maxScale应该大于minScale`);
    }
    let _initScale = _.cloneDeep(initScale);
    if (_initScale > maxScale) {
      _initScale = maxScale;
    } else if (_initScale < minScale) {
      _initScale = minScale;
    }
    setLastTransformData({
      ...lastTransformData,
      scale: ensureScaleInRange(_initScale / 100)
    });
  }, []);

  useEffect(() => {
    const container = containerRef.current;
    const _isPC = isPC();
    if (!container) return;
    if (_isPC) {
      container.addEventListener('mousedown', handleMouseDown);
      container.addEventListener('mousemove', handleMouseMove);
      container.addEventListener('mouseup', handleMouseUp);
      container.addEventListener('mouseleave', handleMouseLeave);
      container.addEventListener('wheel', handleWheel, { passive: false });
      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('keyup', handleKeyUp);
    } else {
      container.addEventListener('touchstart', onTouchStart);
      container.addEventListener('touchmove', onTouchMove);
      container.addEventListener('touchend', onTouchEnd);
    }

    return () => {
      container.removeEventListener('mousedown', handleMouseDown);
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseup', handleMouseUp);
      container.removeEventListener('mouseleave', handleMouseLeave);
      container.removeEventListener('wheel', handleWheel);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [readyToDrag, lastPositionRef.current]);

  useEffect(() => {
    const element = contentBoxRef.current;
    if (!element) return;
    const { scale, translateX, translateY } = lastTransformData;
    const safeScale = ensureScaleInRange(scale);
    element.style.transform = `matrix(${safeScale}, 0, 0, ${safeScale}, ${translateX}, ${translateY})`;
    element.style.transformOrigin = 'center center'; // 确保缩放中心是元素的中心
    if (onUpdate) onUpdate(lastTransformData);
  }, [lastTransformData, onUpdate]);

  useEffect(() => {
    const container = containerRef.current;
    const _isPC = isPC();
    if (!container || !_isPC) return;

    const handleContextMenu = (event: MouseEvent) => {
      event.preventDefault(); // 禁用鼠标右键菜单
    };

    container.addEventListener('contextmenu', handleContextMenu);

    return () => {
      container.removeEventListener('contextmenu', handleContextMenu);
    };
  }, []);

  // #region todo 通用函数

  /**
   * 确保缩放比例在范围内
   * @param scale
   * @returns
   */
  const ensureScaleInRange = (scale: number) => {
    if (scale * 100 > maxScale) {
      scale = maxScale / 100;
    } else if (scale * 100 < minScale) {
      scale = minScale / 100;
    }
    return scale;
  };

  /**
   * 放大
   */
  const zoomIn = (step?: number) => {
    setLastTransformData((prevData) => {
      const newScale = (prevData.scale * 100 + (step || scaleStep)) / 100;
      const scaleFactor = newScale / prevData.scale;
      const updatedData = {
        ...prevData,
        scale: Math.min(newScale, maxScale / 100),
        translateX: prevData.translateX * scaleFactor,
        translateY: prevData.translateY * scaleFactor
      };
      if (onZoomIn) onZoomIn(updatedData);
      return updatedData;
    });
  };

  /**
   * 缩小
   */
  const zoomOut = (step?: number) => {
    setLastTransformData((prevData) => {
      const newScale = (prevData.scale * 100 - (step || scaleStep)) / 100;
      const scaleFactor = newScale / prevData.scale;
      const updatedData = {
        ...prevData,
        scale: Math.max(newScale, minScale / 100),
        translateX: prevData.translateX * scaleFactor,
        translateY: prevData.translateY * scaleFactor
      };
      if (onZoomOut) onZoomOut(updatedData);
      return updatedData;
    });
  };

  /**
   * 重置
   */
  const reset = () => {
    setLastTransformData({ scale: 1, translateX: 0, translateY: 0 });
    if (onReset) onReset();
  };

  /**
   * 移动
   */
  const move = (deltaX: number, deltaY: number) => {
    setLastTransformData((prevData) => {
      // 计算边界距离
      const containerRect = containerRef.current.getBoundingClientRect();
      const contentRect = contentBoxRef.current.getBoundingClientRect();

      // 预判下一步位置的边界距离
      const nextContentRect = {
        left: contentRect.left + deltaX,
        right: contentRect.right + deltaX,
        top: contentRect.top + deltaY,
        bottom: contentRect.bottom + deltaY
      };

      // 计算当前边界距离
      const currentDistances = {
        right: containerRect.right - nextContentRect.left,
        left: nextContentRect.right - containerRect.left,
        bottom: containerRect.bottom - nextContentRect.top,
        top: nextContentRect.bottom - containerRect.top
      };

      // 限制移动距离，确保不会超出限制
      let adjustedDeltaX = deltaX;
      let adjustedDeltaY = deltaY;

      if (currentDistances.right < limitDistance) {
        adjustedDeltaX = containerRect.right - contentRect.left - limitDistance;
      }
      if (currentDistances.left < limitDistance) {
        adjustedDeltaX = containerRect.left + limitDistance - contentRect.right;
      }
      if (currentDistances.bottom < limitDistance) {
        adjustedDeltaY = containerRect.bottom - contentRect.top - limitDistance;
      }
      if (currentDistances.top < limitDistance) {
        adjustedDeltaY = containerRect.top + limitDistance - contentRect.bottom;
      }

      // 计算新位置
      const newTranslateX = prevData.translateX + adjustedDeltaX;
      const newTranslateY = prevData.translateY + adjustedDeltaY;

      // 更新boundaryRef
      boundaryRef.current = {
        rightDistance: currentDistances.right,
        leftDistance: currentDistances.left,
        bottomDistance: currentDistances.bottom,
        topDistance: currentDistances.top
      };

      const { rightDistance, leftDistance, bottomDistance, topDistance } = boundaryRef.current;

      // 使用逻辑运算符判断是否可以移动
      const canMoveX = (deltaX > 0 && rightDistance > limitDistance) || (deltaX < 0 && leftDistance > limitDistance);
      const canMoveY = (deltaY > 0 && bottomDistance > limitDistance) || (deltaY < 0 && topDistance > limitDistance);

      // 使用条件运算符替代对象展开，减少对象创建
      const updatedData = {
        scale: prevData.scale,
        translateX: canMoveX ? newTranslateX : prevData.translateX,
        translateY: canMoveY ? newTranslateY : prevData.translateY
      };

      // 批量更新回调
      if (onMove) {
        requestAnimationFrame(() => onMove(updatedData));
      }

      return updatedData;
    });
  };
  // #endregion 通用函数

  // #region todo PC端事件适配
  const handleMouseDown = (event: any) => {
    containerRef.current?.click();
    setReadyToDrag(true);
    lastPositionRef.current = {
      x: event.clientX,
      y: event.clientY,
      distance: 0
    };
  };

  const handleMouseMove = _.throttle((event: any) => {
    const flag = event.ctrlKey;
    if (readyToDrag && flag) {
      const { clientX, clientY } = event;
      const { x, y } = lastPositionRef.current;
      const deltaX = clientX - x;
      const deltaY = clientY - y;

      move(deltaX, deltaY);
      lastPositionRef.current = { x: clientX, y: clientY, distance: 0 };
    }
  }, 16); // 约60fps

  const handleMouseUp = () => {
    setReadyToDrag(false);
    setDragging(false);
  };

  /**
   * 鼠标滚轮事件、mac触摸板双指滑动
   * 12.6日把默认滚轮事件禁用改为上滑下滑 , 不按ctrl是放大缩小
   * @param event
   */
  const handleWheel = (event: WheelEvent) => {
    const flag = event.ctrlKey;
    if (flag) {
      disableDefaultBehavior(event);
      if (event.deltaY < 0) {
        zoomIn();
      } else {
        zoomOut();
      }
    }
    // disableDefaultBehavior(event);
    // const deltaX = -event.deltaX;
    // const deltaY = -event.deltaY;
    // move(deltaX, deltaY);
    // else {
    //   if (event.deltaY < 0) {
    //     zoomIn();
    //   } else {
    //     zoomOut();
    //   }
    // }
  };

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.ctrlKey && !ctrlPressedRef.current) {
      ctrlPressedRef.current = true;
      handleMouseUp(); // 防止抖动，先执行handleMouseUp
      containerRef.current?.classList.add('ctrl-pressed');
    }
  };

  const handleKeyUp = (event: KeyboardEvent) => {
    if (!event.ctrlKey) {
      ctrlPressedRef.current = false;
      handleMouseUp();
      containerRef.current?.classList.remove('ctrl-pressed');
    }
  };

  // #endregion PC端事件适配

  // #region  todo 移动端事件适配

  const onTouchStart = (event: TouchEvent) => {
    if (event.touches.length === 1) {
      const { clientX, clientY } = event.touches[0];
      lastPositionRef.current = { x: clientX, y: clientY, distance: 0 };
      setReadyToDrag(true);
      setDragging(false);
      setReadyToScale(false);
    } else if (event.touches.length === 2) {
      setReadyToScale(true);
      setReadyToDrag(false);
      setDragging(false);

      const [touch1, touch2] = Array.from(event.touches);
      const x = touch1.clientX - touch2.clientX;
      const y = touch1.clientY - touch2.clientY;
      lastPositionRef.current.distance = Math.sqrt(x * x + y * y);

      const scaleCenter = [
        (touch1.clientX + x / 2 - lastTransformData.translateX) / lastTransformData.scale,
        (touch1.clientY + y / 2 - lastTransformData.translateY) / lastTransformData.scale
      ];
      // 缩放导致偏移的比例
      setScaleTranslateProportion([
        scaleCenter[0] / contentBoxRef.current.offsetWidth,
        scaleCenter[1] / contentBoxRef.current.offsetHeight
      ]);
    }
  };
  const onTouchMove = _.throttle((event: TouchEvent) => {
    if (event.touches.length === 1 && readyToDrag) {
      setDragging(true);

      const { clientX, clientY } = event.touches[0];
      const { x, y } = lastPositionRef.current;
      const deltaX = clientX - x;
      const deltaY = clientY - y;

      move(deltaX, deltaY);
      lastPositionRef.current = {
        ...lastPositionRef.current,
        x: clientX,
        y: clientY
      };
    } else if (event.touches.length === 2) {
      if (!readyToScale) {
        onTouchStart(event);
        return;
      }

      const [touch1, touch2] = Array.from(event.touches);
      const distance = Math.sqrt(
        (touch1.clientX - touch2.clientX) * (touch1.clientX - touch2.clientX) +
          (touch1.clientY - touch2.clientY) * (touch1.clientY - touch2.clientY)
      );
      getScaling(distance / lastPositionRef.current.distance, false);
      lastPositionRef.current.distance = distance;
    }
  }, 16);
  const onTouchEnd = () => {
    setReadyToDrag(false);
    setDragging(false);
    setReadyToScale(false);
    lastPositionRef.current = { x: 0, y: 0, distance: 0 };
  };

  // 进行指定大小的缩放
  const getScaling = (scale: number, useCenter = true) => {
    // 为0或者为1就不进行缩放
    if (scale === 0 || scale === 1) return;

    // 缩放前的contentBoxRef大小
    const oldSize = [
      contentBoxRef.current.offsetWidth * lastTransformData.scale,
      contentBoxRef.current.offsetHeight * lastTransformData.scale
    ];
    let _scaleTranslateProportion = _.cloneDeep(scaleTranslateProportion);
    // 如果直接操作,不是双指进行缩放就置touchZoom中心是缩放中心
    if (useCenter) {
      // 外层容器的中心,
      const scaleCenter = [
        (containerRef.current.offsetWidth / 2 - lastTransformData.translateX) / lastTransformData.scale,
        (containerRef.current.offsetHeight / 2 - lastTransformData.translateY) / lastTransformData.scale
      ];
      // 缩放导致偏移的比例
      _scaleTranslateProportion = [
        scaleCenter[0] / containerRef.current.offsetWidth,
        scaleCenter[1] / containerRef.current.offsetHeight
      ];
    }
    // 设置缩放的偏移,之前结在使用两指的偏移位置来计算,实际上缩放后大小的变化不是两指间移动的距离 变化大小其实就是缩放的大小乘原来的大小
    lastTransformData.translateX += oldSize[0] * (1 - scale) * _scaleTranslateProportion[0] || 0;
    lastTransformData.translateY += oldSize[1] * (1 - scale) * _scaleTranslateProportion[1] || 0;
    // 设置缩放
    lastTransformData.scale *= scale;

    setLastTransformData(lastTransformData);
  };

  // #endregion 移动端事件适配

  // 添加鼠标离开容器的处理函数
  const handleMouseLeave = () => {
    setReadyToDrag(false);
    setDragging(false);
    lastPositionRef.current = { x: 0, y: 0, distance: 0 };
  };

  return (
    <div
      ref={containerRef}
      style={{
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative'
      }}
      className={`grabbing-box-container ${dragging ? 'cursor-grabbing' : 'cursor-grab'}`}
      // className={`grabbing-box-container w-full h-full overflow-hidden flex flex-col relative ${dragging ? 'cursor-grabbing' : 'cursor-grab'}`}
    >
      {scaleButtons && (
        <div
          className={`scale-button-group ${safeScaleButtonsPosition.top ? 'custom-position-top' : ''} ${
            safeScaleButtonsPosition.bottom ? 'custom-position-bottom' : ''
          } ${safeScaleButtonsPosition.left ? 'custom-position-left' : ''} ${
            safeScaleButtonsPosition.right ? 'custom-position-right' : ''
          }`}
          style={
            {
              '--scale-buttons-space-x': scaleButtonsSpaceX,
              '--scale-buttons-space-y': scaleButtonsSpaceY,
              ...buttonStyle
            } as any
          }
        >
          <Button onClick={() => zoomOut(10)} disabled={scaling <= minScale} type="text">
            -
          </Button>
          <span className="scale-button-text">{scaling}%</span>
          <Button onClick={() => zoomIn(10)} disabled={scaling >= maxScale} type="text">
            +
          </Button>
          <span className="scale-button-separator" />
          <Button onClick={reset} type="text">
            <Tooltip title="重置定位">
              <AimOutlined />
            </Tooltip>
          </Button>
          <Button type="text">
            <Tooltip {...buttonTipProps}>
              <QuestionCircleOutlined />
            </Tooltip>
          </Button>
        </div>
      )}
      <div
        ref={contentBoxRef}
        className="grabbing-box"
        style={{
          width: 'fit-content',
          height: 'fit-content',
          transformOrigin: 'center center',
          userSelect: 'none',
          transform: 'matrix(1, 0, 0, 1, 0, 0)'
          // boxShadow:
          //   '0px 0px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 16px 0px rgba(0, 0, 0, 0.04), 0px 0px 24px 0px rgba(0, 0, 0, 0.04)'
        }}
      >
        {children}
      </div>
    </div>
  );
};

const RenderBox = (props: GrabbingBoxProps) => {
  const { grabbing, children, ...rest } = props;
  if (!grabbing && _.isEmpty(rest)) {
    return children;
  }
  return <GrabbingBox {...rest}>{children}</GrabbingBox>;
};

export default RenderBox;
