.grabbing-box-container {

  .cursor-grab,
  .cursor-grab * :not(:hover) {
    cursor: grab;
  }

  .cursor-grabbing,
  .cursor-grabbing * {
    cursor: grabbing;
  }

  .scale-button-group {
    position: absolute;
    z-index: 999;
    user-select: none;

    &.custom-position-top {
      top: var(--scale-buttons-space-y);
      bottom: unset;
    }

    &.custom-position-bottom {
      bottom: var(--scale-buttons-space-y);
      top: unset;
    }

    &.custom-position-left {
      left: var(--scale-buttons-space-x);
      right: unset;
    }

    &.custom-position-right {
      right: var(--scale-buttons-space-x);
      left: unset;
    }
  }

  .scale-button-group {
    width: 205px;
    height: 40px;
    padding: 0 8px;
    line-height: 32px;
    display: flex;
    gap: 4px;
    text-align: center;
    align-items: center;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0px 6px 16px -8px rgba(0, 0, 0, 0.08), 0px 9px 28px 0px rgba(0, 0, 0, 0.05), 0px 12px 48px 16px rgba(0, 0, 0, 0.03);

    button {
      width: 32px;
      height: 32px;
      padding: 5px 10px;
      cursor: pointer;
    }

    .scale-button-text {
      width: 60px;
      height: 32px;
      padding: 0 8px;
      background-color: #f5f5f5;
    }

    .scale-button-separator {
      width: 1px;
      height: 24px;
      background-color: #e0e0e0;
    }
  }

  &.ctrl-pressed {
    cursor: pointer;
  }

  .grabbing-box-button-tip .ant-tooltip-arrow {
    display: none;
  }
}