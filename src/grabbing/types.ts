import React from 'react';
import { TooltipProps } from 'antd/es/tooltip';

export type BoxData = {
  scale: number;
  translateX: number;
  translateY: number;
};

/**
 * @interface GrabbingBoxProps
 * @description GrabbingBox 组件的属性参数 传入进来的组件会根据父组件的尺寸和比例进行缩放
 */
export interface GrabbingBoxProps {
  /**
   * @property {boolean} grabbing
   * @description 是否开启缩放
   */
  grabbing?: boolean;

  /**
   * @property {string} mode
   * @description 画布模式，edit | detail | preview | template
   */
  mode?: 'edit' | 'detail' | 'preview' | 'template';

  /**
   * @property {number} maxScale
   * @default 200
   * @description 放大最高比例限制，单位百分比
   */
  maxScale?: number;

  /**
   * @property {number} minScale
   * @default 20
   * @description 缩小的最小比例限制，单位百分比
   */
  minScale?: number;

  /**
   * @property {number} scaleStep
   * @default 10
   * @description 放大缩小的步长，点击按钮时用，单位同上
   */
  scaleStep?: number;

  /**
   * @property {number} initScale
   * @default 100
   * @description 初始化时的显示比例
   */
  initScale?: number;

  /**
   * @property {boolean} scaleButtons
   * @default true
   * @description 是否显示 scale 按钮组，包括放大（+）缩小（-）和重置
   */
  scaleButtons?: boolean;

  /**
   * @property {string} scaleButtonsPosition
   * @default 'top right'
   * @description 指定缩放按钮组的位置, top | right | bottom | left
   */
  scaleButtonsPosition?: string;

  /**
   * @property {string} scaleButtonsSpaceX
   * @default '10px'
   * @description 指定与边的横向距离
   */
  scaleButtonsSpaceX?: string;

  /**
   * @property {string} scaleButtonsSpaceY
   * @default '20px'
   * @description 指定与边的纵向距离
   */
  scaleButtonsSpaceY?: string;

  /**
   * @property {number} throttleSpan
   * @default 50
   * @description 事件节流时间间隔，单位毫秒
   */
  throttleSpan?: number;

  /**
   * @property {number} scrollSpeed
   * @default 1
   * @description PC端鼠标滚动轮上下滚动倍速 1-5
   */
  scrollSpeed?: number;

  /**
   * @property {number} limitDistance
   * @default 20
   * @description 限制的距离，单位px
   */
  limitDistance?: number;

  /**
   * @property {Object} buttonStyle
   * @description 按钮样式
   */
  buttonStyle?: React.CSSProperties;

  /**
   * @property {TooltipProps} buttonTipProps
   * @description 按钮提示属性配置
   * @default
   * {
   *   overlayClassName: 'grabbing-box-button-tip',
   *   getPopupContainer: () => document.querySelector('.grabbing-box-container') as HTMLElement,
   *   trigger: 'hover',
   *   overlayStyle: { maxWidth: '600px' },
   *   title: (
   *     <div>
   *       移动画布：触摸板双指滑动; 鼠标滚轮;;<br />
   *       放大缩小画布：鼠标滚轮+ctrl; 双指开合触摸板; 画布的【+】【-】按钮; <br />
   *       重置定位：点击画布右下角的重置按钮，将画布恢复到初始位置和比例。
   *     </div>
   *   )
   * }
   */
  buttonTipProps?: TooltipProps;

  /**
   * @property {function} onUpdate
   * @description 任何显示效果的变化都会触发（包括放大缩小拖拽和重置）
   * @param {Object} data - { scale, translateX, translateY }
   */
  onUpdate?: (data: BoxData) => void;

  /**
   * @property {function} onZoomIn
   * @description 放大显示效果的时候触发
   * @param {Object} data - { scale, translateX, translateY }
   */
  onZoomIn?: (data: BoxData) => void;

  /**
   * @property {function} onZoomOut
   * @description 缩小显示效果的时候触发
   * @param {Object} data - { scale, translateX, translateY }
   */
  onZoomOut?: (data: BoxData) => void;

  /**
   * @property {function} onMove
   * @description 改变横向或竖向位置的时候触发，也就是拖拽或者滚动轮滚动或者触摸板滚动
   * @param {Object} data
   */
  onMove?: (data: any) => void;

  /**
   * @property {function} onReset
   * @description 重置时触发
   */
  onReset?: () => void;

  /**
   * @property {React.ReactNode} children
   * @description 子组件
   */
  children?: React.ReactNode;
}
