export default {
  zh_CN: {
    'cpnt-UyokVVStOILI': '等于',
    'cpnt-c5yq30F6k2ne': '不等于',
    'cpnt-ikl511hKikDV': '大于',
    'cpnt-em4xZidQ1Syt': '大于等于',
    'cpnt-m4N15CcuKtB6': '小于',
    'cpnt-KB0PjPCggrvn': '小于等于',
    'cpnt-UHQsHhcH2Au6': '范围',
    'cpnt-sfg7RIfVFc8d': '高级范围',
    'cpnt-Q7Mlw36zw4Zm': '包含',
    'cpnt-HNWQ5EJOJZN8': '不包含',
    'cpnt-oChjvff1WVCI': '有值',
    'cpnt-De8GeqoBk6uJ': '空值',
    'cpnt-SvqIwWNy4idt': '全部',
    'cpnt-5bQDj3aTjp5t': '匹配',
    'cpnt-DSF8rpTcFZ7y': '不匹配',
    'cpnt-hHK8680FxGmG': '开头匹配',
    'cpnt-PDWqVMtXaxRO': '开头不匹配',
    'cpnt-OWLicrq1d9IG': '结尾匹配',
    'cpnt-PNbbl1hOallR': '结尾不匹配',
    'cpnt-PFrtOshhGKA1': '是',
    'cpnt-7LFTCvUyqCJR': '否',
    'cpnt-rAevvR3Vjdin': '且',
    'cpnt-EsO4TcHAI7wD': '或',
    'cpnt-eiwhz1qV1no2': '请输入',
    'cpnt-ytfMow9keXU8': '最大输入500个字符',
    'cpnt-sb6g8SkJhpJm': '最大长度11个字符',
    'cpnt-L15WIrPsqIbG': '请输入数字',
    'cpnt-9ZWLTMDAre63': '最大长度20个字符',
    'cpnt-x4VSu31cTBRW': '请输入浮点数字',
    'cpnt-Osu1pGbGmxSb': '请输入日期时间',
    'cpnt-dhgTSN1Ske8h': '请输入日期',
    'cpnt-s5FxIWuNrIPK': '添加过滤',
    'cpnt-PtSZHIiZV2LJ': '最多添加{{maxFilterCount}}条',
    'cpnt-gG6DhnUvOuyo': '指标',
    'cpnt-5C378gDxGa7h': '请选择过滤属性',
    'cpnt-YG92LJRUwxHv': '请输入操作符',
    'cpnt-HQkHA9NZFHBk': '开始时间',
    'cpnt-FwShS9cLTEdR': '结束时间',
    'cpnt-OGjUhEjFHKq4': '最小值',
    'cpnt-BXbukcBVYWBg': '最大值',
    'cpnt-d3V4YiM4aztS': '至',
    'cpnt-6z54TkYlrYFJ': '值',
    'cpnt-ybBAfr5w5mDg': '请选择',
    'cpnt-a51l12jsqcxa': '操作符',
    'cpnt-QT6Mwe7KWyOh': '请选择分群',
    'cpnt-cUVwbwqpeTZv': '添加分群'
  },
  en_US: {
    'cpnt-UyokVVStOILI': 'Equal',
    'cpnt-c5yq30F6k2ne': 'Not equal',
    'cpnt-ikl511hKikDV': 'Greater than',
    'cpnt-em4xZidQ1Syt': 'Greater than or equal to',
    'cpnt-m4N15CcuKtB6': 'Less than',
    'cpnt-KB0PjPCggrvn': 'Less than or equal to',
    'cpnt-UHQsHhcH2Au6': 'Range',
    'cpnt-sfg7RIfVFc8d': 'Advanced range',
    'cpnt-Q7Mlw36zw4Zm': 'Contains',
    'cpnt-HNWQ5EJOJZN8': 'Does not contain',
    'cpnt-oChjvff1WVCI': 'Has value',
    'cpnt-De8GeqoBk6uJ': 'Empty',
    'cpnt-SvqIwWNy4idt': 'All',
    'cpnt-5bQDj3aTjp5t': 'Match',
    'cpnt-DSF8rpTcFZ7y': 'Not match',
    'cpnt-hHK8680FxGmG': 'Starts with',
    'cpnt-PDWqVMtXaxRO': 'Does not start with',
    'cpnt-OWLicrq1d9IG': 'Ends with',
    'cpnt-PNbbl1hOallR': 'Does not end with',
    'cpnt-PFrtOshhGKA1': 'Yes',
    'cpnt-7LFTCvUyqCJR': 'No',
    'cpnt-rAevvR3Vjdin': 'AND',
    'cpnt-EsO4TcHAI7wD': 'OR',
    'cpnt-eiwhz1qV1no2': 'Please enter',
    'cpnt-ytfMow9keXU8': 'Maximum 500 characters',
    'cpnt-sb6g8SkJhpJm': 'Maximum 11 characters',
    'cpnt-L15WIrPsqIbG': 'Please enter a number',
    'cpnt-9ZWLTMDAre63': 'Maximum 20 characters',
    'cpnt-x4VSu31cTBRW': 'Please enter a decimal number',
    'cpnt-Osu1pGbGmxSb': 'Please enter date time',
    'cpnt-dhgTSN1Ske8h': 'Please enter date',
    'cpnt-s5FxIWuNrIPK': 'Add Filter',
    'cpnt-PtSZHIiZV2LJ': 'Add up to {{maxFilterCount}} filters',
    'cpnt-gG6DhnUvOuyo': 'Metric',
    'cpnt-5C378gDxGa7h': 'Please select filter property',
    'cpnt-YG92LJRUwxHv': 'Please enter operator',
    'cpnt-HQkHA9NZFHBk': 'Start Time',
    'cpnt-FwShS9cLTEdR': 'End Time',
    'cpnt-OGjUhEjFHKq4': 'Min Value',
    'cpnt-BXbukcBVYWBg': 'Max Value',
    'cpnt-d3V4YiM4aztS': 'To',
    'cpnt-6z54TkYlrYFJ': 'Value',
    'cpnt-ybBAfr5w5mDg': 'Please select',
    'cpnt-a51l12jsqcxa': 'Operator',
    'cpnt-QT6Mwe7KWyOh': 'Please select segment',
    'cpnt-cUVwbwqpeTZv': 'Add Segment'
  }
};
