import { t } from '../utils/translation';

export default {
  maxFilterCount: 20,
  operatorList: [{
    name: t('cpnt-UyokVVStOILI'),
    operator: 'EQ'
  }, {
    name: t('cpnt-c5yq30F6k2ne'),
    operator: 'NE'
  },
  {
    name: t('cpnt-ikl511hKikDV'),
    operator: 'GT'
  }, {
    name: t('cpnt-em4xZidQ1Syt'),
    operator: 'GTE'
  }, {
    name: t('cpnt-m4N15CcuKtB6'),
    operator: 'LT'
  }, {
    name: t('cpnt-KB0PjPCggrvn'),
    operator: 'LTE'
  }, {
    name: t('cpnt-UHQsHhcH2Au6'),
    operator: 'BETWEEN'
  }, {
    name: t('cpnt-sfg7RIfVFc8d'),
    operator: 'ADVANCED_BETWEEN'
  },
  {
    name: t('cpnt-Q7Mlw36zw4Zm'),
    operator: 'IN'
  }, {
    name: t('cpnt-HNWQ5EJOJZN8'),
    operator: 'NOT_IN'
  },
  {
    name: t('cpnt-oChjvff1WVCI'),
    operator: 'IS_NOT_NULL'
  }, {
    name: t('cpnt-De8GeqoBk6uJ'),
    operator: 'IS_NULL'
  },
  {
    name: t('cpnt-SvqIwWNy4idt'),
    operator: 'ALL'
  }, {
    name: t('cpnt-5bQDj3aTjp5t'),
    operator: 'LIKE'
  }, {
    name: t('cpnt-DSF8rpTcFZ7y'),
    operator: 'NOT_LIKE'
  }, {
    name: t('cpnt-hHK8680FxGmG'),
    operator: 'START_WITH'
  }, {
    name: t('cpnt-PDWqVMtXaxRO'),
    operator: 'NOT_START_WITH'
  }, {
    name: t('cpnt-OWLicrq1d9IG'),
    operator: 'END_WITH'
  }, {
    name: t('cpnt-PNbbl1hOallR'),
    operator: 'NOT_END_WITH'
  }, {
    name: t('cpnt-PFrtOshhGKA1'),
    operator: 'IS_TRUE'
  }, {
    name: t('cpnt-7LFTCvUyqCJR'),
    operator: 'IS_FALSE'
  }
  ],
  // typeOperator: {
  //   INT: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
  //   LONG: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
  //   DOUBLE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
  //   HIVE_DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   DATETIME: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   HIVE_TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   STRING: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'LIKE', 'NOT_LIKE', 'START_WITH', 'NOT_START_WITH', 'END_WITH', 'NOT_END_WITH', 'IN', 'NOT_IN'],
  //   BOOL: ['IS_TRUE', 'IS_FALSE']
  // },

  // typeOperator: {
  //   INT: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'IN', 'NOT_IN'],
  //   LONG: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'IN', 'NOT_IN'],
  //   DOUBLE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'IN', 'NOT_IN'],
  //   HIVE_DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'IN', 'NOT_IN'],
  //   DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'IN', 'NOT_IN'],
  //   DATETIME: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'IN', 'NOT_IN'],
  //   HIVE_TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'IN', 'NOT_IN'],
  //   TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'IN', 'NOT_IN'],
  //   STRING: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'IN', 'NOT_IN']
  //   // BOOL: ['IS_TRUE', 'IS_FALSE']
  // },

  typeOperator: {
    INT: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN'],
    LONG: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'IN', 'NOT_IN'],
    DOUBLE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'IN', 'NOT_IN'],
    HIVE_DATE: ['GTE', 'LTE', 'BETWEEN'],
    DATE: ['GTE', 'LTE', 'BETWEEN'],
    DATETIME: ['GTE', 'LTE', 'BETWEEN'],
    HIVE_TIMESTAMP: ['GTE', 'LTE', 'BETWEEN'],
    TIMESTAMP: ['GTE', 'LTE', 'BETWEEN'],

    STRING: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'IN', 'NOT_IN'],
    BOOL: ['IS_NOT_NULL', 'IS_NULL', 'IS_TRUE', 'IS_FALSE']
  },

  typeFormatter: {
    DATE: 'YYYY-MM-DD',
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    TIMESTAMP: 'YYYY-MM-DD HH:mm:ss',
    HIVE_DATE: 'YYYY-MM-DD',
    HIVE_TIMESTAMP: 'YYYY-MM-DD HH:mm:ss'
  },
  connector: [{
    name: t('cpnt-rAevvR3Vjdin'),
    value: 'AND'
  }, {
    name: t('cpnt-EsO4TcHAI7wD'),
    value: 'OR'
  }],
  validator: {
    STRING: {
      option: {
        required: true,
        maxLen: 500
      },
      message: {
        required: t('cpnt-eiwhz1qV1no2'),
        maxLen: t('cpnt-ytfMow9keXU8')
      }
    },
    INT: {
      option: {
        required: true,
        maxLen: 11,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: t('cpnt-eiwhz1qV1no2'),
        maxLen: t('cpnt-sb6g8SkJhpJm'),
        regex: t('cpnt-L15WIrPsqIbG')
      }
    },
    LONG: {
      option: {
        required: true,
        maxLen: 20,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: t('cpnt-eiwhz1qV1no2'),
        maxLen: t('cpnt-9ZWLTMDAre63'),
        regex: t('cpnt-L15WIrPsqIbG')
      }
    },
    DOUBLE: {
      option: {
        required: true,
        // regex: '^\\d*[.]?\\d*$',
        regex: '^[+-]?\\d+(\\.\\d+)?$',
        maxLen: 20
      },
      message: {
        required: t('cpnt-eiwhz1qV1no2'),
        maxLen: t('cpnt-9ZWLTMDAre63'),
        regex: t('cpnt-x4VSu31cTBRW')
      }
    },
    DATETIME: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-Osu1pGbGmxSb')
      }
    },
    TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-Osu1pGbGmxSb')
      }
    },
    DATE: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-dhgTSN1Ske8h')
      }
    },
    HIVE_TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-Osu1pGbGmxSb')
      }
    },
    HIVE_DATE: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-dhgTSN1Ske8h')
      }
    },
    BOOL: {
      option: {
        required: false
      },
      message: {
        required: t('cpnt-eiwhz1qV1no2')
      }
    }
  }
};
