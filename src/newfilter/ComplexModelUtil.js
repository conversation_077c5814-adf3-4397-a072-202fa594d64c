import _ from 'lodash';
import FilterModel from './FilterModel';
import Log from '../utils/log';

const log = Log.getLogger('FilterModelUtil');

export default class ComplexModelUtil {
  /**
   *  创建过滤model
   */
  static createFilter() {
    return new FilterModel();
  }

  /**
   * 创建过滤列表
   */
  static createFilterGroup(filters) {
    return {
      connector: 'AND',
      filters: filters || []
    };
  }

  static createComplexGroup(filters) {
    return {
      connector: 'OR',
      filters: [{ filter: filters }]
    };
  }

  /**
   * 向过滤组添加过滤组
   * @param {object} filter {connector, filters:[]}
   * @return 返回新创建的过滤组
   */
  static addFilterGroup(filterGroup, filter) {
    if (!filterGroup) {
      log.warn('addFilterGroup', '试图向空过滤组添加过滤组');
      return;
    }
    if (!filterGroup.filter) {
      filterGroup.filter = {};
    }

    if (_.isEmpty(filterGroup.filter.filters) || !_.isArray(filterGroup.filter.filters)) {
      filterGroup.filter.filters = [];
    }
    const _filterGroup = this.createFilterGroup([filter]);
    filterGroup.filter.filters.push(_filterGroup);
    return _filterGroup;
  }

  /**
   * 向过滤组添加过滤组, 并且添加一个Filter
   * @param {object} filter {connector, filters:[]}
   */
  static addFilterGroupWithOneFilter(filterGroup) {
    return this.addFilterGroup(filterGroup, this.createFilter());
  }

  static addComplexGroupWithOneFilter(filterGroup, i) {
    const obj = { filter: this.createFilterGroup([]) };
    if (!filterGroup) {
      log.warn('addFilterGroup', '试图向空过滤组添加过滤组');
      return;
    }

    if (_.isEmpty(filterGroup.filters) || !_.isArray(filterGroup.filters)) {
      filterGroup.filters = [];
    }
    if (i !== undefined) {
      filterGroup.filters[i] = obj;
    } else {
      filterGroup.filters.push(obj);
    }
    // return this.addFilterGroup(filterGroup);
  }

  /**
   * 向过滤组中添加过滤
   * @param {filter} filterGroup {connector, filters:[]}
   * @return 返回FilterModel
   */
  static addFilter(filterGroup) {
    if (!filterGroup) {
      log.warn('addFilter', '试图向空过滤组添加过滤');
      return;
    }

    if (_.isEmpty(filterGroup.filters) || !_.isArray(filterGroup.filters)) {
      filterGroup.filters = [];
    }

    const _filter = this.crateFilter();
    filterGroup.filters.push(_filter);
    return _filter;
  }

  /**
   * 初始化一个有一个过滤组一个过滤器的FilterModel
   */
  static initCreateFilterGroup() {
    return this.createComplexGroup(this.createFilterGroup([]));
  }

  static deleteEmptyFilterList(filterListGroup) {
    if (!filterListGroup || !filterListGroup.filters) {
      log.warn('deleteEmptyFilterList', '无法处理空filterListGroup');
      return;
    }
    filterListGroup.filters = filterListGroup.filters.filter(v => !_.isEmpty(v.filters));
  }

  static isFilterValid(filter) {
    return filter.valid().isValid;
  }

  static isFilterListGroupValid(filterListGroup) {
    let result1 = true;
    if (!_.isEmpty(filterListGroup.filters) && filterListGroup.filters.some(n => n.id === undefined)) {
      result1 = false;
    }
    return filterListGroup.filters
      .flatMap(v => v.filter.filters).flatMap(w => w.filters)
      .map(v => {
        v.validating = true;
        return v;
      })
      .map(v => this.isFilterValid(v))
      .reduce((a, b) => a && b, true) && result1;
  }

  static fromJson(json) {
    if (_.isEmpty(json)) {
      return this.initCreateFilterGroup();
    }
    const _json = _.cloneDeep(json);
    _json.filters.map(filterGroups => {
      filterGroups.filter.filters.forEach((filterGroup, w) => {
        filterGroup.filters.forEach((filter, i) => {
          filterGroup.filters[i] = FilterModel.fromJson(filter);
        });
        filterGroups.filter.filters[w] = filterGroup;
      });
      return filterGroups;
    });
    return _json;
  }

  static toJson(filterListGroup) {
    if (_.isEmpty(filterListGroup)) {
      return null;
    }
    const result = {};
    result.connector = filterListGroup.connector;
    result.filters = filterListGroup.filters.map(filterGroup => {
      return {
        connector: filterGroup.connector,
        filters: filterGroup.filters.map(filter => filter.toJson())
      };
    });
    return result;
  }

  static getValidJson(filterListGroup) {
    if (_.isEmpty(filterListGroup)) {
      return null;
    }
    const result = {};
    result.connector = filterListGroup.connector;
    result.filters = filterListGroup.filters.map(filterGroup => {
      const results = filterGroup.filter.filters.map(item => {
        return {
          connector: item.connector,
          filters: item.filters.filter(v => v.valid().isValid).map(filter => filter.toJson())
        };
      });
      // return filterGroup;
      return { ...filterGroup, filter: { ...filterGroup.filter, filters: results } };
    });
    // if (_.isEmpty(result.filters)) return {};
    return result;
  }

  static getValidFilterListCount(filterGroup) {
    return filterGroup.filters
      .filter(v => v.valid().isValid)
      .map(() => 1)
      .reduce((a, b) => a + b, 0);
  }

  static getValidFilterListGroupCount(filterListGroup) {
    return filterListGroup.filters
      .map(v => this.getValidFilterListCount(v))
      .reduce((a, b) => a + b, 0);
  }
}
