import React, { useState, useEffect } from 'react';
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, Menu, Input } from 'antd';
import FilterConfig from './FilterConfig';
import { t } from '../utils/translation';

const { operatorList, typeOperator } = FilterConfig;
// 操作符map
const OPERATOR_MAP = operatorList.reduce((map, obj) => {
  map[obj.operator] = obj;
  return map;
}, {});

export default function FilterOperator({ value, onChange }) {
  let [searchText, setSearchText] = useState(OPERATOR_MAP[value.operator]?.name || '');
  const [menuVisible, setMenuVisible] = useState(false);

  useEffect(() => {
    if (menuVisible) {
      setSearchText('');
    } else if (OPERATOR_MAP[value.operator]) {
      setSearchText(OPERATOR_MAP[value.operator].name);
    }
  }, [value.operator, menuVisible]);

  useEffect(() => {
    setSearchText(OPERATOR_MAP[value.operator]?.name || '');
  }, [value.operator]);

  const operatorMenu = () => {
    return getTypeOperators()
      .map(v => OPERATOR_MAP[v])
      .filter(v => !searchText || v.name.indexOf(searchText) >= 0)
      .map((p, i) => {
        return (
          <Menu.Item key={i} onClick={() => onSelect(p)}>
            {p.name}
          </Menu.Item>
        );
      });
  };

  const onSelect = oeprator => {
    value.changeOperator(oeprator.operator);
    onChange(value);
    setSearchText(OPERATOR_MAP[value.operator]?.name);
  };

  const getTypeOperators = () => {
    return typeOperator[value.fieldType];
  };

  const menu = () => {
    return (
      <Menu forceSubMenuRender style={{ maxHeight: 400, overflowY: 'auto', maxWidth: 500, overflowX: 'auto' }}>
        {value?.fieldType && operatorMenu()}
      </Menu>
    );
  };

  const onMenuVisible = v => {
    if (v) {
      setSearchText('');
    }
    setMenuVisible(v);
  };

  return (
    <Dropdown getPopupContainer={triggerNode => triggerNode.parentNode} overlay={menu} trigger={['click']} onOpenChange={onMenuVisible}>
      <div className="clickWrapper">
        <Input
          className="ant-dropdown-link"
          placeholder={t('cpnt-a51l12jsqcxa')}
          // suffix={<DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />}
          onChange={e => setSearchText((e.target.value))}
          onFocus={event => event.target.select()}
          value={searchText}
        />
        <DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />
      </div>
    </Dropdown>
  );
}
