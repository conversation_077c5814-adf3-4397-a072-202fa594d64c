import React, {
  useState,
  useContext,
  useEffect,
  useRef,
  Fragment
} from 'react';
import { Input, AutoComplete, DatePicker, Select, InputNumber } from 'antd';
import _ from 'lodash';
import dayjs from 'dayjs';
import FilterContext from './FilterContext';
import useDebounce from '../utils/useDebounce';
import SelectTime from '../selectTime';
import { t } from '../utils/translation';

const { Option } = AutoComplete;
const { RangePicker } = DatePicker;

const FilterValueContext = React.createContext();

/**
 * 输入一个值的情况
 */
function TextInput(props) {
  let { fieldValue, items, onChange } = props;
  const { setMenuVisible } = useContext(FilterValueContext);
  const inputEl = useRef(null);

  if (_.isArray(items)) {
    items.filter(n => !!n.value);
  } else {
    items = [];
  }

  const itemsDs = items.map(v => ({ value: v.value || v, label: v.value || v }));

  return (
    <AutoComplete
      // dataSource={itemsDs}
      style={{
        width: 100
      }}
      options={itemsDs}
      showSearch={items.length > 0}
      onChange={onChange}
      value={fieldValue}
      filterOption={(inputValue, option) => `${option.value}`
        .toUpperCase()
        .indexOf(
          typeof inputValue === 'string' ? inputValue.toUpperCase() : ''
        ) !== -1}
      // allowClear
      // optionLabelProp="label"
      ref={inputEl}
      onDropdownVisibleChange={v => setMenuVisible(v)}
    />
  );
  // return 'OneInput';
}

function DateInput(props) {
  let { fieldType, onChange } = props;

  const showTime = fieldType === 'DATETIME'
    || fieldType === 'TIMESTAMP'
    || fieldType === 'HIVE_TIMESTAMP'
    ? { format: 'HH:mm:ss' }
    : null;
  const format = fieldType === 'DATETIME'
    || fieldType === 'TIMESTAMP'
    || fieldType === 'HIVE_TIMESTAMP'
    ? 'YYYY-MM-DD HH:mm:ss'
    : 'YYYY-MM-DD';
  let unit = fieldType === 'DATETIME'
    || fieldType === 'TIMESTAMP'
    || fieldType === 'HIVE_TIMESTAMP'
    ? 'second'
    : 'day';

  const onValueChange = m => {
    onChange(m.startOf(unit).valueOf());
  };

  return (
    <DatePicker
      allowClear={false}
      placeholder={t('cpnt-dhgTSN1Ske8h')}
      showTime={showTime}
      format={format}
      // allowClear
      value={props.fieldValue && dayjs(props.fieldValue)}
      // getCalendarContainer={triggerNode => triggerNode.parentNode}
      onChange={onValueChange}
    />
  );
}

/**
 * 单一输入的输入框，要根据fieldType决定显示那种输入框，可能是TextInput, 也可能是DateInput
 * @param {FilterModel} value
 * @param {function} onChange
 */
function SingleInput(props) {
  const { fieldType } = props;

  if (
    fieldType === 'DATE'
    || fieldType === 'DATETIME'
    || fieldType === 'TIMESTAMP'
    || fieldType === 'HIVE_DATE'
    || fieldType === 'HIVE_TIMESTAMP'
  ) {
    return DateInput(props);
  }

  return TextInput(props);
}

const longToMoment = fv => {
  return _.isArray(fv) ? fv.map(v => dayjs(v)) : undefined;
};

/**
 * 范围日历输入框
 */
function DateBetweenInput(props) {
  let { fieldValue, fieldType, onChange } = props;
  const [value, setValue] = useState(longToMoment(fieldValue));
  const showTime = !!(
    fieldType === 'DATETIME'
    || fieldType === 'TIMESTAMP'
    || fieldType === 'HIVE_TIMESTAMP'
  );
  const format = fieldType === 'DATETIME'
    || fieldType === 'TIMESTAMP'
    || fieldType === 'HIVE_TIMESTAMP'
    ? 'YYYY-MM-DD HH:mm:ss'
    : 'YYYY-MM-DD';
  let unit = fieldType === 'DATETIME'
    || fieldType === 'TIMESTAMP'
    || fieldType === 'HIVE_TIMESTAMP'
    ? 'second'
    : 'day';
  const onValueChange = m => {
    setValue(m);
    onChange(
      m
      && m[0]
      && m[1] && [m[0].startOf(unit).valueOf(), m[1].startOf(unit).valueOf()]
    );
  };

  // const onValueOk = m => {
  //   onChange(m && m[0] && m[1] && [m[0].valueOf(), m[1].valueOf()]);
  // };

  return (
    <RangePicker
      allowClear={false}
      showTime={showTime}
      format={format}
      placeholder={[t('cpnt-HQkHA9NZFHBk'), t('cpnt-FwShS9cLTEdR')]}
      // onOk={onValueOk}
      onChange={onValueChange}
      value={value}
    />
  );
}

function NumberBetweenInput(props) {
  let { onChange, fieldValue } = props;
  const inputEl = useRef(null);

  if (!_.isArray(fieldValue)) {
    fieldValue = [];
  }
  return (
    <>
      <InputNumber
        style={{ width: 100, marginRight: '5px' }}
        placeholder={t('cpnt-OGjUhEjFHKq4')}
        value={fieldValue[0]}
        ref={inputEl}
        onChange={value => !isNaN(value) && onChange([value, fieldValue[1]])}
      />
      {t('cpnt-d3V4YiM4aztS')}
      <InputNumber
        style={{ width: 100, marginLeft: '5px' }}
        placeholder={t('cpnt-BXbukcBVYWBg')}
        value={fieldValue[1]}
        onChange={value => !isNaN(value) && onChange([fieldValue[0], value])}
      />
    </>
  );
}

function TwoInput(props) {
  const { fieldType, operator, onChange, fieldValue, items } = props;
  if (
    fieldType === 'DATE'
    || fieldType === 'DATETIME'
    || fieldType === 'TIMESTAMP'
    || fieldType === 'HIVE_DATE'
    || fieldType === 'HIVE_TIMESTAMP'
  ) {
    return (
      <DateBetweenInput
        fieldType={fieldType}
        operator={operator}
        fieldValue={fieldValue}
        items={items}
        onChange={onChange}
      />
    );
  }
  return (
    <NumberBetweenInput
      fieldType={fieldType}
      operator={operator}
      fieldValue={fieldValue}
      items={items}
      onChange={onChange}
    />
  );
}

function AdvancedBetween(props) {
  const { onChange, fieldValue } = props;
  return <SelectTime showTime data={fieldValue} onChange={onChange} />;
}

/**
 * 枚举类型的输入框
 * @param {object} props
 */
function EnumInput(props) {
  let { onChange, fieldValue, items } = props;
  const inputEl = useRef(null);
  const { setMenuVisible } = useContext(FilterValueContext);
  if (!_.isArray(fieldValue)) {
    fieldValue = [];
  }

  return (
    // <Input
    //   onChange={onChange}
    //   value={fieldValue}
    //   ref={inputEl}
    //   style={{ minWidth: 150 }}
    // />
    <Select
      mode="tags"
      onChange={onChange}
      allowClear
      maxTagCount={10}
      value={fieldValue}
      ref={inputEl}
      style={{ minWidth: 150 }}
      optionLabelProp="label"
      onDropdownVisibleChange={v => setMenuVisible(v)}
    >
      {items
        .filter(n => !!n.value)
        .map((item, i) => (
          <Option
            value={item.value ? item.value : item}
            label={item.name ? item.name : item}
            key={i}
          >
            {item.name ? item.name : item}
          </Option>
        ))}
    </Select>
  );
}

/**
 * *****************************************
 * 主组件
 * *****************************************
 */
export default function FilterValue({ value, onChange }) {
  const { logProvider } = useContext(FilterContext);
  const [items, setItems] = useState([]);
  const [log] = useState(logProvider.getLogger('FilterValue'));
  const [menuVisible, setMenuVisible] = useState(false);
  const [filterValueContext] = useState({
    setMenuVisible
  });

  const { field, fieldType, operator, isEnum, tableId, schemaId } = value;
  const [fieldValue, setFieldValue] = useState(value.value);
  const debounceFieldValue = useDebounce(fieldValue, 500);

  useEffect(() => {
    const fetchItem = async () => {
      setItems([]);
      // const _items = await dataProvider?.getPropertyEnumList(tableId, schemaId, debounceFieldValue);
      // log.debug('fetchItem', _items);
      // setItems(_items);
    };
    if (menuVisible) {
      fetchItem();
    }
  }, [field, log, isEnum, tableId, schemaId, menuVisible]);

  useEffect(() => {
    setFieldValue(value.value);
  }, [value.value]);

  useEffect(() => {
    log.debug('debounceFieldValue changed call onChange', debounceFieldValue);
    if (debounceFieldValue === null || debounceFieldValue === undefined) return;
    onChange(debounceFieldValue);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceFieldValue]);

  const onChangeFieldValue = v => {
    log.debug('onChangeFieldValue', v, JSON.stringify(value));
    value.value = v;

    // 如果value是个枚举类型的属性，并且枚举是keyvalue形式的，需要向filterModel赋值一个showValue，方便用于回显
    if (value.isEnum && _.isArray(items) && items.length > 0 && items[0].name) {
      const itemValueMap = items.reduce((a, b) => {
        a[b.value] = b.name;
        return a;
      }, {});
      if (_.isArray(v)) {
        value.showValue = v.map(_v => itemValueMap[_v] || _v);
      } else {
        value.showValue = itemValueMap[v] || v;
      }
    }
    setFieldValue(v);
  };

  /**
   * 主方法
   * 返回渲染组件
   */
  const filterValue = () => {
    switch (operator) {
      case 'EQ':
      case 'NE':
      case 'GT':
      case 'GTE':
      case 'LT':
      case 'LTE':
        return (
          <SingleInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'BETWEEN':
        return (
          <TwoInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'ADVANCED_BETWEEN':
        return (
          <AdvancedBetween
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'IN':
      case 'NOT_IN':
      case 'LIKE':
      case 'NOT_LIKE':
      case 'START_WITH':
      case 'NOT_START_WITH':
      case 'END_WITH':
      case 'NOT_END_WITH':
        return (
          // <TextInput
          //   fieldType={fieldType}
          //   operator={operator}
          //   fieldValue={fieldValue}
          //   items={items}
          //   onChange={onChangeFieldValue}
          // />
          <EnumInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'IS_NOT_NULL':
      case 'IS_NULL':
      case 'IS_TRUE':
      case 'IS_FALSE':
        return <span />;
      default:
        return (
          <Input placeholder={t('cpnt-6z54TkYlrYFJ')} disabled={!operator} style={{ width: 100 }} />
        );
    }
  };

  return (
    <FilterValueContext.Provider value={filterValueContext}>
      <div>{filterValue()}</div>
    </FilterValueContext.Provider>
  );
}
