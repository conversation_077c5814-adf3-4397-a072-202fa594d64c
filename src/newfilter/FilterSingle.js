import React, { useState, useContext, useEffect, Fragment } from 'react';
import { PlusCircleOutlined, QuestionCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import _ from 'lodash';
import FilterSingleWrapper from './FilterSingleWrapper';
import FilterField from './FilterField';
import FilterOperator from './FilterOperator';
import FilterValue from './FilterValue';
import FilterContext from './FilterContext';

/**
 * 封装单个过滤组件
 * 当前组件失去焦点时校验数据
 * 当Field、Operator、Value结束编辑时，刺激当前组件获得焦点，以便贸然校验
 */
function FilterSingle({ value, onChange, onAdd, onDelete }) {
  const { fieldName } = value;
  const { logProvider, canAdd, mode, hideAdd, hideInit } = useContext(FilterContext);

  // 校验器
  const [validator, setValidator] = useState({});
  const [log] = useState(logProvider.getLogger('FilterSingle'));

  useEffect(() => {
    if (mode !== 'edit') return;
    // 退出编辑
    setValidator(value.valid());
  }, [log, value.field, value.fieldType, value.value, value.operator, value, mode]);

  log.debug('Before Render', JSON.stringify(value));

  const onDeleteItem = () => {
    onDelete && onDelete(hideInit);
  };

  return (
    <li className={`FilterSingle ${mode}`}>
      <div style={{ display: 'flex' }} hidden={mode !== 'edit' && !value.valid().isValid}>
        <div className={`FilterField ${mode} ${validator?.fieldType && value.validating ? 'has-error' : ''}`}>
          <FilterSingleWrapper value={fieldName} useTakePlaceWidth>
            <FilterField value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div>
        <div className={`FilterOperator ${mode} ${validator?.operator && value.validating ? 'has-error' : ''}`}>
          <FilterSingleWrapper value={value.getOperatorShow()} useTakePlaceWidth>
            <FilterOperator value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div>
        <div className={`FilterValue ${mode} ${validator?.value && value.validating ? 'has-error' : ''}`} hidden={value.isValueCanEdit() === false}>
          <FilterSingleWrapper value={value.getValueShow()}>
            <FilterValue value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div>
        {
          mode === 'edit' && <>
            <div className="Ctroller">
              { value.validating && (validator?.fieldType || validator?.operator || _.head(_.values(validator.value))) && <Tooltip placement="topRight" title={_.head(_.values(validator.value))}>
                <div style={{ marginRight: 5 }}><QuestionCircleOutlined className="Validator" /></div>
              </Tooltip>}
              {!hideAdd && <PlusCircleOutlined className="add" onClick={onAdd} hidden={!canAdd} />}
              <CloseCircleOutlined className="delete" onClick={onDeleteItem} />
            </div>
          </>
        }
      </div>
    </li>
  );
}

export default FilterSingle;
