import React, { useState, useEffect, useContext } from 'react';
import { DownOutlined, SyncOutlined } from '@ant-design/icons';
import { Dropdown, Menu, Input } from 'antd';
import _ from 'lodash';
import FilterContext from './FilterContext';
import useDebounce from '../utils/useDebounce';
import { t } from '../utils/translation';

export default function FilterField({ value, onChange }) {
  const { logProvider, dataProvider } = useContext(FilterContext);

  const [propertyMap, setPropertyMap] = useState({});
  const [searchText, setSearchText] = useState(value.field);
  const [menuVisible, setMenuVisible] = useState(false);
  const [fetching, setFetching] = useState(false);
  const debounceSearchText = useDebounce(searchText, 200);

  const [fetchPropertyList] = useState(() => {
    return dp => {
      return async name => {
        log.debug('fetchPropertyList');
        const plist = await dp.getPropertyList(name, dp.eventId);
        let levelMap = _.groupBy(plist, v => v.level1);
        _.keys(levelMap).forEach(level1 => {
          levelMap[level1] = _.groupBy(levelMap[level1], v => v.level2);
        });
        setFetching(false);
        setPropertyMap(levelMap);
        return levelMap;
      };
    };
  });

  const [log] = useState(logProvider.getLogger('FilterField'));

  useEffect(() => {
    if (menuVisible) {
      setSearchText('');
    } else {
      setSearchText(value.fieldName || '');
    }
  }, [value.fieldName, menuVisible]);

  useEffect(() => {
    if (!menuVisible) return;
    setFetching(true);
    if (debounceSearchText === value.fieldName) return;
    fetchPropertyList(dataProvider)(debounceSearchText);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceSearchText, dataProvider, menuVisible, fetchPropertyList]);

  const propertyMenu = properties => {
    return properties
      .filter(
        v => !searchText
          || v.field.indexOf(searchText) >= 0
          || v.fieldName.indexOf(searchText) >= 0
      )
      .map((p, i) => {
        return (
          <Menu.Item key={`${p.level1}_${p.level2}_${p.fieldName}_${i}`} onClick={() => onSelectProperty(p)}>
            {p.fieldName}
          </Menu.Item>
        );
      });
  };

  const level2Menu = (children, level2) => {
    if (!level2) {
      if (_.isArray(children)) {
        return propertyMenu(children);
      }
    }

    return (
      <Menu.ItemGroup title={level2} key={level2}>
        {propertyMenu(children)}
      </Menu.ItemGroup>
    );
  };

  const onSelectProperty = property => {
    value.changeProperty(property);
    onChange(value);
    setMenuVisible(false);
  };

  const level1Menu = (children, level1) => {
    if (!level1) {
      if (_.isArray(children)) {
        return propertyMenu(children);
      } else if (_.isObject(children) && !Object.entries(children)[0][0]) {
        return propertyMenu(Object.entries(children)[0][1]);
      }
    }

    return (
      <Menu.ItemGroup title={level1} key={level1}>
        {_.map(children, level2Menu)}
      </Menu.ItemGroup>
    );
  };

  const menu = () => {
    return (
      <Menu style={{ maxHeight: 400, overflowY: 'auto', maxWidth: 500, overflowX: 'auto' }}>
        {(fetching && _.isEmpty(propertyMap)) ? <Menu.Item><SyncOutlined spin />loading...</Menu.Item> : _.map(propertyMap, level1Menu)}
      </Menu>
    );
  };

  return (
    <Dropdown arrow getPopupContainer={triggerNode => triggerNode.parentNode} overlay={menu} trigger={['click']} onOpenChange={setMenuVisible}>
      <div className="clickWrapper">
        <Input
          className="ant-dropdown-link"
          placeholder={t('cpnt-ybBAfr5w5mDg')}
          // suffix={}
          onChange={e => setSearchText((e.target.value))}
          onFocus={event => event.target.select()}
          value={searchText}
        />
        <DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />
      </div>
    </Dropdown>
  );
}
