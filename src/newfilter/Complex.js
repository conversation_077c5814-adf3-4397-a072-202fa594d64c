// https://stackoverflow.com/questions/37949981/call-child-method-from-parent
import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { CloseCircleFilled, PlusOutlined } from '@ant-design/icons';
import { Button, Select } from 'antd';
import FilterListGroup from './FilterListGroup';
import FilterContext from './FilterContext';
import ComplexModelUtil from './ComplexModelUtil';
import ComplexGroup from './ComplexGroup';
import FilterConfig from './FilterConfig';
import Log from '../utils/log';
import './filter.scss';
import { t } from '../utils/translation';

const { Option } = Select;

const log = Log.getLogger('Filter');
/**
 * 过滤器组件
 * mode有两种形式，edit|detail，edit模式下可编辑，默认是edit
 * @param {object} 过滤值 value
 * @param {object} 数据提供器 {getPropertyList: (name) => data}
 * @param {function} 响应改变 onChange
 */
const Filter = forwardRef((props, ref) => {
  // The component instance will be extended
  // with whatever you return from the callback passed
  // as the second argument
  useImperativeHandle(ref, () => ({
    isValid() {
      setContext({ ...context, validating: true });
      return ComplexModelUtil.isFilterListGroupValid(value);
    }
  }));

  const { dataProvider, onChange, mode, selectList } = props;
  const { maxFilterCount } = FilterConfig;
  const [value, setValue] = useState(ComplexModelUtil.initCreateFilterGroup());
  // 用于保存父子组件的value状态，比对这个值和props.value可以判断需不需要刷新
  const [propsValue, setPropsValue] = useState({});
  const [segmentList, setSegmentList] = useState([]);

  const [context, setContext] = useState({
    dataProvider,
    logProvider: Log,
    mode: mode || 'edit',
    validating: false
  });

  useEffect(() => {
    const list = [...selectList];
    if (props.value.filters) {
      props.value.filters.forEach(n => {
        if (n.id && list.findIndex(k => k.id === n.id) === -1) {
          const info = { ...n };
          delete info.filter;
          list.push(info);
        }
      });
    }
    if (list.length !== segmentList.length) {
      setSegmentList(list);
    }
  }, [selectList, props.value, segmentList]);

  // 父组件改变，设置当前值
  useEffect(() => {
    log.debug('props.value changed', JSON.stringify(props.value));
    // 仅当不相等是才刷新文档
    if (props.value !== propsValue) {
      setValue(ComplexModelUtil.fromJson(props.value));
    }
  }, [props.value, propsValue]);

  // 过滤组改变，修改过滤条数
  // 父组件改变，设置当前值
  useEffect(() => {
    setContext({
      dataProvider,
      logProvider: Log,
      mode: mode || 'edit'
    });
  }, [value, maxFilterCount, dataProvider, mode]);

  /**
   * 添加过滤组
   */
  const addFilterGroup = i => {
    ComplexModelUtil.addFilterGroupWithOneFilter(value.filters[i]);
    const _value = ComplexModelUtil.getValidJson(value);
    setPropsValue(_value);
    onChange(_value);
    setValue({ ...value });
  };

  const addComplexGroup = () => {
    ComplexModelUtil.addComplexGroupWithOneFilter(value);
    const _value = ComplexModelUtil.getValidJson(value);
    setPropsValue(_value);
    onChange(_value);
    setValue({ ...value });
  };

  const deleteGroup = i => {
    value.filters.splice(i, 1);
    const _value = ComplexModelUtil.getValidJson(value);

    setPropsValue(_value);
    onChange(_value);
    setValue({ ...value });
  };

  /**
   * 当过滤组修改时回调
   * @param {object} v 过滤组
   */
  const onValueChange = (v, i) => {
    log.debug('onValueChanged', JSON.stringify(v));
    value.filters[i].filter = v;
    const _value = ComplexModelUtil.getValidJson(value);
    setPropsValue(_value);
    onChange(_value);
    setValue({ ...value });
  };

  const onChangeConnector = filter => {
    return v => {
      filter.connector = v;
      onChange(value);
    };
  };

  log.debug('Before Render', JSON.stringify(value), context.canAdd);

  const onChangedd = (id, i) => {
    const info = selectList.find(n => n.id === id);
    ComplexModelUtil.addComplexGroupWithOneFilter(value, i);
    value.filters[i].id = info.id;
    value.filters[i].name = info.name;
    value.filters[i].lastCalcTime = info.lastCalcTime;
    value.filters[i].customerCount = info.customerCount;
    const _value = ComplexModelUtil.getValidJson(value);
    setPropsValue(_value);
    onChange(_value);
    setValue({ ...value });
  };

  return (
    // </FilterContext.Provider>
    <div className="wolf-static-component_filter_FilterGroupPanel">
      <ComplexGroup
        connector={value.connector}
        onChangeConnector={onChangeConnector(value)}
        filterCount={value.filters.length}
        inner="inner"
        mode={context.mode}
      >
        {value.filters.map((v, i) => {
          let filterCount = 0;
          if (v.filter && v.filter.filters) {
            v.filter.filters.forEach(w => {
              if (w.filters && w.filters.length > 0) {
                filterCount += w.filters.length;
              }
            });
          }
          return (
            <div style={{ marginBottom: 20 }} key={i}>
              <FilterContext.Provider value={{ ...context, canAdd: filterCount < maxFilterCount, filterCount }}>
                <div>
                  <Select
                    showSearch
                    optionFilterProp="children"
                    className={context.validating && !v.id ? 'has-error' : ''}
                    disabled={context.mode === 'detail'}
                    value={v.id}
                    key={v.id}
                    onChange={id => onChangedd(id, i)}
                    style={{ width: 280 }}
                    placeholder={t('cpnt-QT6Mwe7KWyOh')}
                  >
                    {segmentList.map(n => <Option key={`${n.id}`} value={n.id}>{n.name}</Option>)}
                  </Select>
                  {context.mode !== 'detail' && <CloseCircleFilled
                    onClick={() => deleteGroup(i)}
                    style={{ color: '#ccc', fontSize: 20, marginLeft: 20, cursor: 'pointer' }}
                  />}
                </div>

                {v.id && <div style={{ marginLeft: 20 }}>
                  <FilterListGroup value={v.filter} onChange={val => onValueChange(val, i)} />
                  <div style={{ marginTop: 10 }} className="FilterAdder">
                    <Button
                      type="dashed"
                      icon={<PlusOutlined />}
                      onClick={() => addFilterGroup(i)}
                      disabled={filterCount >= maxFilterCount}
                      hidden={context.mode === 'detail'}
                    >
                      {t('cpnt-s5FxIWuNrIPK')}
                    </Button>
                    <span style={{ marginLeft: 10 }} hidden={context.mode === 'detail'}>
                      [{filterCount}/{maxFilterCount}] {t('cpnt-PtSZHIiZV2LJ', { maxFilterCount })}
                    </span>
                  </div>
                </div>}
              </FilterContext.Provider>
            </div>
          );
        })}
      </ComplexGroup>
      <div>
        <Button
          type="dashed"
          icon={<PlusOutlined />}
          onClick={addComplexGroup}
          disabled={value.filters.length >= 5}
          hidden={context.mode === 'detail'}
        >
          {t('cpnt-cUVwbwqpeTZv')} [{value.filters.length}/5]
        </Button>
      </div>
    </div>
  );
});

export default Filter;
