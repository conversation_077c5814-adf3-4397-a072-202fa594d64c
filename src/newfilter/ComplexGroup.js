import React from 'react';
import { img1, img2 } from '../base64/index';

function FilterGroup({ connector, onChangeConnector, filterCount, children, inner, mode }) {
  const onChangeImg = () => {
    if (mode === 'detail') return;
    if (connector === 'AND') {
      onChangeConnector('OR');
    } else {
      onChangeConnector('AND');
    }
  };

  return (
    <div className="FilterGroupPanel">
      <div className="ConnectorPanel" hidden={filterCount <= 1}>
        <div className="TopLine" />
        <div className="VLine" />
        <div className="BottomLine" />
        <div className="Connector">
          {/* <FilterConnector value={connector} onChange={onChangeConnector} /> */}
          <span onClick={onChangeImg}><img src={connector === 'OR' ? img1 : img2} style={{ width: 30, marginLeft: 5.5, cursor: 'pointer' }} alt="" /></span>
        </div>
      </div>
      <ul className={`FilterList ${inner}`}>{children}</ul>
    </div>
  );
}

export default FilterGroup;
