import React, { useState, useEffect } from 'react';
import { SyncOutlined } from '@ant-design/icons';
import { Menu } from 'antd';
import _ from 'lodash';
import Log from '../utils/log';

const log = Log.getLogger('FilterFieldMenu');

export default function FilterFieldMenu({ searchText, dataProvider, onChange, value }) {
  const [propertyMap, setPropertyMap] = useState([]);
  let [fetching, setFetching] = useState(true);

  useEffect(() => {
    const featchPropertyList = async () => {
      setFetching(true);
      const plist = await dataProvider.getPropertyList();
      let levelMap = _.groupBy(plist, (v) => v.level1);
      _.keys(levelMap).forEach((level1) => {
        levelMap[level1] = _.groupBy(levelMap[level1], (v) => v.level2);
      });
      setFetching(false);
      setPropertyMap(levelMap);
    };
    featchPropertyList();
  }, [searchText, dataProvider]);

  const propertyMenu = (properties) => {
    return properties
      .filter((v) => !searchText || v?.field?.indexOf(searchText) >= 0 || v?.fieldName?.indexOf(searchText) >= 0)
      .map((p, i) => {
        return (
          <Menu.Item key={i} onClick={() => onChange(p)}>
            {p?.field}[{p?.fieldName}]
          </Menu.Item>
        );
      });
  };

  const level2Menu = (children, level2) => {
    if (!level2) {
      if (_.isArray(children)) {
        return propertyMenu(children);
      }
    }

    return (
      <Menu.ItemGroup title={level2} key={level2}>
        {propertyMenu(children)}
      </Menu.ItemGroup>
    );
  };

  const level1Menu = (children, level1) => {
    if (!level1) {
      if (_.isArray(children)) {
        return propertyMenu(children);
      }
    }

    return (
      <Menu.ItemGroup title={level1} key={level1}>
        {_.map(children, level2Menu)}
      </Menu.ItemGroup>
    );
  };

  log.debug('before render', value);
  return (
    <div style={{ height: 400, overflowY: 'scroll' }}>
      <Menu forceSubMenuRender>
        {fetching ? (
          <Menu.Item>
            <SyncOutlined spin />
            loading
          </Menu.Item>
        ) : (
          _.map(propertyMap, level1Menu)
        )}
      </Menu>
    </div>
  );
}
