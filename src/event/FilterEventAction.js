// 事件的操作类型输入域
import React from 'react';
import { Select } from 'antd';
import _ from 'lodash';
import FILTER_CONFIG from './FilterConfig';
import { t } from '../utils/translation';

const { Option } = Select;

const optionValues = _.keys(FILTER_CONFIG.EVENT_ACTION);
const optionNames = _.values(FILTER_CONFIG.EVENT_ACTION);

const FilterEventAction = props => {
  const { value, onChange } = props;
  const handleChange = v => {
    value.changeProperty({ ...value, action: v });
    onChange(value);
  };

  return (
    <Select value={value.action} style={{ width: '100%' }} onChange={handleChange} placeholder={t('cpnt-qu7fCgRbWz6M')}>
      {
        _.map(optionValues, (item, index) => {
          return <Option key={`${item}`} value={item}>{optionNames[index]}</Option>;
        })
      }
    </Select>
  );
};

export default FilterEventAction;
