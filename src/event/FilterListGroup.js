import React, { useContext, useRef, forwardRef, useImperativeHandle } from 'react';
import _ from 'lodash';
import FilterGroup from './FilterGroup';
import FilterSingle from './FilterSingle';
import FilterContext from './FilterContext';
import FilterModelUtil from './FilterModelUtil';

function FilterListGroup({ value, onChange }, ref) {
  const { logProvider, mode, isActionCollection } = useContext(FilterContext);
  const log = logProvider.getLogger('FilterListGroup');

  useImperativeHandle(ref, () => ({
    isValid() {
      let relult = _.without(refArr.current, null, undefined, false);
      if (_.isEmpty(relult)) return true;
      return _.map(relult, item => item.isValid()).reduce((a, b) => a && b, true);
    }
  }));

  const onChangeFilter = () => {
    return () => {
      // filterList[index] = filter;
      onChange(value);
    };
  };

  const onAddFilter = (filterList, index) => {
    return () => {
      filterList.filters = [
        ...filterList.filters.slice(0, index + 1),
        FilterModelUtil.createFilter(isActionCollection),
        ...filterList.filters.slice(index + 1)
      ];
      onChange(value);
    };
  };

  const refArr = useRef([]);

  const onDeleteFilter = (filterList, index) => {
    return () => {
      filterList.filters.splice(index, 1);
      FilterModelUtil.deleteEmptyFilterList(value);
      onChange(value);
    };
  };

  const getFilterSingle = (filterList, filter, index) => {
    return (
      <FilterSingle
        key={`${filter.key}`}
        value={filter}
        ref={el => refArr.current[index] = el}
        onChange={onChangeFilter(filterList, index)}
        onAdd={onAddFilter(filterList, index)}
        onDelete={onDeleteFilter(filterList, index)}
      />
    );
  };

  /**
   * 获得FilterGroup组件
   * @param {array} filterList
   * @param {integer} index
   */
  const getFilterGroup = (filterList, index) => {
    if (!filterList || !filterList.filters) return '';
    const filterCount = mode === 'detail' ? FilterModelUtil.getValidFilterListCount(filterList) : filterList.filters.length;
    if (filterCount === 0) return '';
    return (
      <FilterGroup
        key={index}
        connector={filterList.connector}
        onChangeConnector={onChangeConnector(filterList)}
        filterCount={filterCount}
        inner="inner"
      >
        {filterList.filters.map((v, i) => getFilterSingle(filterList, v, i))}
      </FilterGroup>
    );
  };

  const onChangeConnector = filter => {
    return v => {
      filter.connector = v;
      onChange(value);
    };
  };

  // 没有值就退出
  if (!value || !value.filters) {
    return '';
  }

  log.debug('connector', value.connector);
  log.debug('filters', value.filters);

  // const filterListCount = mode === 'detail' ? FilterModelUtil.getValidFilterListGroupCount(value) : value.filters.length;
  const filterListCount = value.filters.length;
  if (filterListCount === 0) return '';
  return (
    <div className="FilterGroupListPannel1">
      <FilterGroup
        connector={value.connector}
        onChangeConnector={onChangeConnector(value)}
        filterCount={filterListCount}
        inner=""
      >
        {value.filters.map(getFilterGroup)}
      </FilterGroup>
    </div>
  );
}

export default forwardRef(FilterListGroup);
