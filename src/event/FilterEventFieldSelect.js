/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useContext } from 'react';
import { Line } from '@ant-design/charts';
import { Spin, Select, Table, Popover } from 'antd';
import _ from 'lodash';
import dayjs from 'dayjs';
import FilterContext from './FilterContext';
import useDebounce from '../utils/useDebounce';
import { t } from '../utils/translation';

const { Option } = Select;

const columns = [
  {
    title: t('cpnt-AR7T7RqJ1EqY'),
    dataIndex: 'propertySchema'
  },
  {
    title: t('cpnt-bByX9ETiMI5G'),
    dataIndex: 'displayName'
  },
  {
    title: t('cpnt-Q85vnmdiku4d'),
    dataIndex: 'dataType'
  }
];

const nameAlias = {
  date: {
    alias: t('cpnt-ori6NhWq0sfN')
  },
  count: {
    alias: t('cpnt-u0Om2N6Mf2Xt')
  }
};

const Item = (props) => {
  const { value, onChange } = props;
  const { dataProvider } = useContext(FilterContext);
  const [fetching, setFetching] = useState(true);
  const [searchValue, setSearchValue] = useState(value?.eventInfo?.displayName || '');
  const [dropDownOpen, setDropDownOpen] = useState(false);
  const [eventList, setEventList] = useState([]);
  const [lineList, setLineList] = useState([]);

  const debounceSearchText = useDebounce(searchValue, 300);

  useEffect(() => {
    setSearchValue(value?.eventInfo?.displayName || '');
  }, [value?.eventInfo?.displayName]);

  const init = async (debounceSearchText1) => {
    // if (searchValue) {
    setFetching(true);
    const list = await dataProvider.getEventList(debounceSearchText1);
    setEventList(list.content);

    if (dataProvider.getEventCountLogsByProjectId) {
      const lineRes = await dataProvider.getEventCountLogsByProjectId();
      setLineList(lineRes);
    }

    setFetching(false);
    // }
  };

  useEffect(() => {
    init(debounceSearchText);
  }, []);

  useEffect(() => {
    dropDownOpen && init(debounceSearchText);
  }, [debounceSearchText, dropDownOpen]);

  const filterOption = (inputValue, option) => {
    if (option.props.children.key.indexOf(inputValue) >= 0) return true;
  };

  const onEventFilterChange = (v) => {
    const currentEvent = _.find(eventList, (item) => item.id === v);
    value.changeProperty({
      ...value,
      eventInfo: !_.isEmpty(currentEvent)
        ? {
            id: currentEvent.id,
            eventType: currentEvent.eventType,
            displayName: currentEvent.name,
            eventNameValue: currentEvent.eventNameValue,
            filter: currentEvent.filter,
            specialPropertyMappingList: currentEvent.specialPropertyMappingList
          }
        : {},
      eventAggregateProperty: {},
      eventFilterProperty: null
    });
    onChange(value);
  };
  const renderEventInfo = (item) => {
    const findValue = _.find(lineList, (i) => i.eventId === item.id);
    if (
      _.some(findValue.eventCountLogList, (o) => {
        return !o;
      })
    ) {
      return;
    }
    const data = findValue.eventCountLogList.map((e) => {
      return {
        ...e,
        statTime: dayjs(e.statTime).format('MM-DD')
      };
    });

    const config = {
      data,
      padding: 'auto',
      xField: 'statTime',
      yField: 'count'
    };

    return (
      <div className="eventInfo">
        <div className="eventInfoTitle">{item.eventNameValue}</div>
        <div className="eventInfoContent">
          <div className="createInformation">
            {item.createUserName} {t('cpnt-3mm2zG3vyIhQ')}{' '}
            {dayjs(item.createTime).format('YYYY-MM-DD')}
          </div>
          <div>
            {t('cpnt-mFwxaTueE7tP')}{item.eventType === 'CUSTOM' ? t('cpnt-nNsbEWqYBV4c') : t('cpnt-QowbVlFYnrFw')}
          </div>
          <div className="eventTable">
            <Table
              size="small"
              rowKey="key"
              columns={columns}
              dataSource={_.map(item?.specialPropertyMappingList, (e, index) => {
                return { ...e, key: index };
              })}
              pagination={{
                defaultPageSize: 5,
                pageSizeOptions: ['5']
              }}
            />
          </div>
          <div className="line">
            <div className="lineTitle">{t('cpnt-fVo1sBrlSvbW')}</div>
            <div className="lineChart">
              <Line {...config} meta={nameAlias} />
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div>
      <Select
        showSearch
        style={{ width: '100%' }}
        placeholder={t('cpnt-pjD5rsv5CilX')}
        notFoundContent={fetching ? <Spin size="small" /> : null}
        onSearch={_.debounce(setSearchValue, 500)}
        onChange={onEventFilterChange}
        value={value?.eventInfo?.id}
        allowClear
        dropdownMatchSelectWidth={false}
        onDropdownVisibleChange={setDropDownOpen}
        optionLabelProp="label"
        filterOption={filterOption}
      >
        {eventList?.map((event) => (
          <Option key={`${event?.id}`} value={event?.id} label={event?.name}>
            {/* {event.name} */}
            <Popover
              placement="right"
              key={event?.name}
              autoAdjustOverflow
              overlayClassName="eventInfoPopover"
              content={() => renderEventInfo(event)}
              trigger="hover"
              overlayStyle={{ width: '512px' }}
            >
              <div style={{ width: '100%' }}>{event?.name}</div>
            </Popover>
          </Option>
        ))}
      </Select>
    </div>
  );
};

export default Item;
