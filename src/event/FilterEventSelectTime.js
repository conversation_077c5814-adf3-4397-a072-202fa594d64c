// 事件的操作类型输入域
import React from 'react';
import dayjs from 'dayjs';
import SelectTime from '../selectTime';
import 'dayjs/locale/zh-cn';

dayjs.locale('zh-cn');

const FilterEventSelectTime = props => {
  const { value, onChange, isActionCollection } = props;
  const { dateRange } = value;

  const handleChange = v => {
    value.changeProperty({ ...value, dateRange: v });
    onChange(value);
  };

  return (
    <SelectTime
      data={dateRange}
      showTime
      style={{ width: 360 }}
      onChange={handleChange}
      isActionCollection={isActionCollection}
    />
  );
};

export default FilterEventSelectTime;
