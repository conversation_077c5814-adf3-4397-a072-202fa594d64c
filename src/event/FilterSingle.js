/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useContext, useEffect, useRef, forwardRef, useImperativeHandle, useMemo } from 'react';
import { PlusCircleOutlined, QuestionCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
// import { Filter } from 'wolf-static-cpnt';
import Filter from '../filter/Filter';
import FilterSingleWrapper from './FilterSingleWrapper';
import FilterEventFieldSelect from './FilterEventFieldSelect';
import FilterOperator from './FilterOperator';
import FilterValue from './FilterValue';

import FilterEventFunction from './FilterEventFunction';
import FilterContext from './FilterContext';
import FilterEventAction from './FilterEventAction';
import FilterEventSelectTime from './FilterEventSelectTime';
import FilterEventProperty from './FilterEventProperty';
import { t } from '../utils/translation';

import FILTER_CONFIG from './FilterConfig';

/**
 * 封装单个过滤组件
 * 当前组件失去焦点时校验数据
 * 当Field、Operator、Value结束编辑时，刺激当前组件获得焦点，以便贸然校验
 */
function FilterSingle({ value, onChange, onDelete }, ref) {
  useImperativeHandle(ref, () => ({
    isValid() {
      if (_.isEmpty(filterRef.current && filterRef.current.getValue())) return true;
      return filterRef.current && filterRef.current.isValid && filterRef.current.isValid(true);
    }
  }));
  const { eventAggregateProperty = {}, action, eventInfo = {}, dateRange = [], eventFilterProperty = {} } = value;
  const { id } = eventInfo;
  const { propertyType, fun, operator } = eventAggregateProperty;
  const { mode, dataProvider, isActionCollection } = useContext(FilterContext);

  const _dataProvider = useMemo(() => {
    const pickPd = _.pick(dataProvider, 'getPropertyList');
    pickPd.eventId = id || 0;
    return pickPd;
  }, [id]);

  // 校验器
  const [validator, setValidator] = useState({});

  useEffect(() => {
    if (mode !== 'edit') return;
    // 退出编辑
    setValidator(value.valid());
  }, [action, id, eventAggregateProperty?.value, propertyType, JSON.stringify(dateRange), fun, operator, mode]);

  const filterRef = useRef(null);

  const onChangeFilter = data => {
    value.changePropertyValue(data);
    onChange(value);
  };

  const onAddFilter = () => {
    const filters = filterRef.current.addFilterGroup();
    value.changePropertyValue(filters);
    onChange(value);
  };

  let funValue = '';

  if (fun) {
    funValue = (_.find(FILTER_CONFIG.CONDITIONFUN[eventAggregateProperty?.property?.fieldType || 'INT'], item => {
      return item.value === fun;
    }) || {}).name;
  }

  const timeStr1 = getString(dateRange[0], true);
  const timeStr2 = getString(dateRange[1], true);

  return (
    <li className={`FilterSingle ${mode}`}>
      <div style={{ display: 'flex' }} hidden={mode !== 'edit' && !value.valid().isValid}>
        <div className={`FilterEventAction ${mode} ${validator?.action && value.validating ? 'has-error' : ''}`}>
          <FilterSingleWrapper value={FILTER_CONFIG.EVENT_ACTION[action]} useTakePlaceWidth>
            <FilterEventAction value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div>
        <div className={`FilterField ${mode} ${validator?.id && value.validating ? 'has-error' : ''}`}>
          <FilterSingleWrapper value={eventInfo.displayName} useTakePlaceWidth>
            <FilterEventFieldSelect value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div>
        <div className={`FilterField ${mode} ${validator?.eventAggregateProperty && value.validating ? 'has-error' : ''}`}>
          <FilterSingleWrapper value={eventAggregateProperty.propertyType === 'TIMES' ? t('cpnt-AP8ztX2itxu1') : eventAggregateProperty.property?.fieldName} useTakePlaceWidth>
            <FilterEventProperty value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div>
        {
          eventAggregateProperty.propertyType === 'EVENT_PROPERTY' && <div className={`FilterField ${mode} ${validator?.fun && value.validating ? 'has-error' : ''}`}>
            <FilterSingleWrapper value={funValue} useTakePlaceWidth>
              <FilterEventFunction value={value} onChange={onChange} />
            </FilterSingleWrapper>
          </div>
        }
        <div className={`FilterOperator ${mode} ${validator?.operator && value.validating ? 'has-error' : ''}`}>
          <FilterSingleWrapper value={value.getOperatorShow()} useTakePlaceWidth>
            <FilterOperator value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div>
        <div className={`FilterValue ${mode} ${validator?.value && value.validating ? 'has-error' : ''}`} hidden={value.isValueCanEdit() === false}>
          <FilterSingleWrapper value={value.getValueShow()}>
            <FilterValue value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div>
        <div className={`FilterEventSelectTime ${mode} ${validator?.dateRange && value.validating ? 'has-error' : ''}`}>
          <FilterSingleWrapper value={`${timeStr1}~${timeStr2}`}>
            <FilterEventSelectTime value={value} onChange={onChange} isActionCollection={isActionCollection} />
          </FilterSingleWrapper>
        </div>
        {
          mode === 'edit' && <>
            <div className="Ctroller">
              { value.validating && (validator?.action || validator?.id || validator?.eventAggregateProperty || validator?.fun || validator?.operator || validator?.value || validator?.dateRange) && <Tooltip placement="topRight" title={_.head(_.values(validator.message))}>
                <div style={{ marginRight: 5 }}><QuestionCircleOutlined className="Validator" /></div>
              </Tooltip>}
              <span className="handleAdd" onClick={onAddFilter} hidden={filterRef.current && filterRef.current.getFilterCount() >= 10}><PlusCircleOutlined className="add" />{t('cpnt-QrY09JXA5WNw')}</span>
              <CloseCircleOutlined className="delete" onClick={onDelete} />
            </div>
          </>
        }
      </div>
      <Filter className="innerFilter" dataProvider={_dataProvider} value={eventFilterProperty} onChange={onChangeFilter} mode={mode} ref={filterRef} hideAdd hideInit />
    </li>
  );
}

const timeTerm = [
  // {
  //   value: 'MINUTE',
  //   label: '分钟',
  //   unit: 'minutes'
  // },
  // {
  //   value: 'HOUR',
  //   label: '小时',
  //   unit: 'hours'
  // },
  {
    value: 'DAY',
    label: t('cpnt-Bbyv5pXAGD8Z'),
    unit: 'days'
  },
  {
    value: 'WEEK',
    label: t('cpnt-xFpgeLOyIrNe'),
    unit: 'weeks'
  },
  {
    value: 'MONTH',
    label: t('cpnt-9f6MOUbMJXfC'),
    unit: 'months'
  }
];

const isPassObj = {
  true: '之前',
  false: '之后'
};

const getString = (obj, showTime) => {
  let str = '';
  if (typeof obj === 'object') {
    if (obj.type === 'ABSOLUTE') {
      const format = showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';
      str = `${dayjs(obj.timestamp).format(format)}`;
    } else if (obj.type === 'RELATIVE') {
      str = `${obj.times}${timeTerm.find(n => n.value === obj.timeTerm)?.label}${isPassObj[JSON.stringify(obj.isPast)]}`;
    } else if (obj.type === 'NOW') {
      str = t('cpnt-hW0UaBuqALyv');
    }
  }

  return str;
};

export default forwardRef(FilterSingle);
