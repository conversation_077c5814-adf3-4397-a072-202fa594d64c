import i18next from 'i18next';

/**
 * cpnt语言文件转CSV
 * @param startWith 前缀
 * @returns
 */
function cpntConvertToCSV(startWith: string) {
  const { cn: cnLang, en: enLang } = cpntGetNewLang();

  const keys = Object.keys(cnLang).filter((key) => (startWith ? key.startsWith(startWith) : true));

  const jsonData = keys.map((key) => ({
    key,
    cn: cnLang[key as keyof typeof cnLang] || '',
    en: enLang[key as keyof typeof enLang] || ''
  }));

  const str = jsonData.reduce((acc, { key, cn, en }) => {
    const escape = (text: string) => `"${(text || '').replace(/"/g, '""')}"`; // 确保text不为undefined
    return `${acc}${escape(key)},${escape(cn)},${escape(en)}\n`;
  }, 'key,cn,en\n');

  const uri = `data:text/csv;charset=utf-8,\ufeff${encodeURIComponent(str)}`;

  const link = document.createElement('a');
  link.href = uri;
  link.download = 'cpnt中英文对照表.csv';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * cpnt 每次下载的时候 获取当前系统新的语言包
 */
function cpntGetNewLang() {
  const cn = i18next.getResourceBundle('zh_CN', 'translation') || {};
  const en = i18next.getResourceBundle('en_US', 'translation') || {};
  return { cn, en };
}

Object.assign(window, { cpntConvertToCSV, cpntGetNewLang });

export { cpntConvertToCSV, cpntGetNewLang };
