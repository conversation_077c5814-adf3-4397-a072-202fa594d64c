import React, { useEffect, useState, useRef, useContext } from 'react';
import { Tabs, Calendar, Button, InputNumber, Select, Input } from 'antd';
import dayjs from 'dayjs';
import locale from 'antd/es/date-picker/locale/zh_CN';
import { DataContext } from './context';

import './index.scss';
import 'dayjs/locale/zh-cn';

const { TabPane } = Tabs;
const timeTerm = [
  // {
  //   value: 'MINUTE',
  //   label: '分钟',
  //   unit: 'minutes'
  // },
  // {
  //   value: 'HOUR',
  //   label: '小时',
  //   unit: 'hours'
  // },
  {
    value: 'DAY',
    label: '天',
    unit: 'days'
  },
  {
    value: 'WEEK',
    label: '周',
    unit: 'weeks'
  },
  {
    value: 'MONTH',
    label: '月',
    unit: 'months'
  }
];

const hourArr = [];
const minuteArr = [];
const secondArr = [];
const scorllUnit = 32;

for (let i = 0; i < 60; i++) {
  let str = i.toString();
  if (str.length === 1) str = `0${str}`;
  if (i < 24) {
    hourArr.push(str);
  }
  minuteArr.push(str);
  secondArr.push(str);
}

const selectStyle = {
  // color: '#ff6800',
  // backgroundColor: '#e6f7ff'
  backgroundColor: '#fff1f0'
};

const { Option } = Select;
const initInfo = { type: 'ABSOLUTE', timeTerm: timeTerm[0].value, isPast: true };

export default ({ save, data }) => {
  const [date, setDate] = useState(dayjs());
  const [hour, setHour] = useState('');
  const [minut, setMinut] = useState('');
  const [second, setSecond] = useState('');
  const [info, setInfo] = useState({ ...initInfo });
  const { state, dispatch } = useContext(DataContext);
  const hourRef = useRef(null);
  const minutRef = useRef(null);
  const secondRef = useRef(null);

  useEffect(() => {
    const timestamp = data?.timestamp || dayjs().valueOf();
    setDate(dayjs(timestamp));
    const hourValue = dayjs(timestamp).format('HH');
    setHour(hourValue);
    hourRef.current.scrollTop = parseInt(hourValue) * scorllUnit;
    const minutValue = dayjs(timestamp).format('mm');
    setMinut(minutValue);
    minutRef.current.scrollTop = parseInt(minutValue) * scorllUnit;
    const secondValue = dayjs(timestamp).format('ss');
    setSecond(secondValue);
    secondRef.current.scrollTop = parseInt(secondValue) * scorllUnit;
    // info.timestamp = dayjs(`${date.format('YYYY-MM-DD')} ${hour}:${minut}:${second}`);
    if (data) {
      setInfo({ ...data });
    } else {
      setInfo({ ...initInfo, timestamp: dayjs(`${date.format('YYYY-MM-DD')} ${hourValue}:${minutValue}:${secondValue}`) });
      dispatch({ info: { ...info, timestamp: dayjs(`${date.format('YYYY-MM-DD')} ${hourValue}:${minutValue}:${secondValue}`) } });
    }
  }, [data]);

  function onDateChange(value) {
    info.timestamp = dayjs(`${date.format('YYYY-MM-DD')} ${hour}:${minut}:${second}`);
    setDate(value);
    dispatch({ info: { ...info, timestamp: dayjs(`${value.format('YYYY-MM-DD')} ${hour}:${minut}:${second}`) } });
  }

  const onSave = () => {
    info.timestamp = dayjs(`${date.format('YYYY-MM-DD')} ${hour}:${minut}:${second}`);
    save(info);
    dispatch({ info: { ...info, timestamp: dayjs(`${date.format('YYYY-MM-DD')} ${hour}:${minut}:${second}`) } });
  };

  const clickHour = n => {
    setHour(n);
    hourRef.current.scrollTop = parseInt(n) * scorllUnit;
    dispatch({ info: { ...info, timestamp: dayjs(`${date.format('YYYY-MM-DD')} ${n}:${minut}:${second}`) } });
  };

  const clickMinut = n => {
    setMinut(n);
    minutRef.current.scrollTop = parseInt(n) * scorllUnit;
    dispatch({ info: { ...info, timestamp: dayjs(`${date.format('YYYY-MM-DD')} ${hour}:${n}:${second}`) } });
  };

  const clickSecond = n => {
    setSecond(n);
    secondRef.current.scrollTop = parseInt(n) * scorllUnit;
    dispatch({ info: { ...info, timestamp: dayjs(`${date.format('YYYY-MM-DD')} ${hour}:${minut}:${n}`) } });
  };
  return (
    <div className="contentStyle">
      <div className="absoluteDateTime">
        <Calendar
          locale={locale}
          style={{ width: 280 }}
          value={date}
          fullscreen={false}
          onChange={onDateChange}
        />
        <div style={{ backgroundColor: '#ff6800', width: 1, margin: '0 5px' }} />
        <div className="timeStyle">
          <div ref={hourRef} className="eachTime">
            {hourArr.map(n => <div className="oneTime" onClick={() => clickHour(n)} style={n === hour ? selectStyle : {}} key={n}>{n}</div>)}
          </div>
          <div style={{ backgroundColor: '#f5f5f5', width: 1 }} />
          <div ref={minutRef} className="eachTime">
            {minuteArr.map(n => <div className="oneTime" onClick={() => clickMinut(n)} style={n === minut ? selectStyle : {}} key={n}>{n}</div>)}
          </div>
          <div style={{ backgroundColor: '#f5f5f5', width: 1 }} />
          <div ref={secondRef} className="eachTime">
            {secondArr.map(n => <div className="oneTime" onClick={() => clickSecond(n)} style={n === second ? selectStyle : {}} key={n}>{n}</div>)}
          </div>
        </div>
      </div>
      {/* <div className="save">
        <Button type="primary" onClick={onSave} size="small">保存</Button>
      </div> */}
    </div>
  );
};
