.selectTimeV2 {
  .selectTimeV2-content {
    width: 360px;
    height: 32px;
    line-height: 32px;
    display: -webkit-inline-box;

    // background: rgb(134, 147, 211);
    .contentLeft {
      display: flex;
      border: 1px solid #d9d9d9;

      .time {
        width: 162px;
        display: flex;
        justify-content: center;
      }

      .symbol {
        width: 35px;
        display: flex;
        justify-content: space-around;
      }
    }

    .shortcut {
      display: flex;

      .icons {
        width: 50px;
        height: 32px;
        border: 1px solid #d9d9d9;
        border-right: none;
        padding-left: 8px;
        cursor: pointer;
        border-right: none;

        .icon {
          margin-right: 4px;
          font-size: 16px;
        }
      }

      .customTime {
        padding: 0 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 32px;
        border-top: 1px solid #d9d9d9;
        border-bottom: 1px solid #d9d9d9;
        border-left: none;

        span {
          display: inline-block;
          height: 22px;
          line-height: 20px;
          border-radius: 6px;
          border: 1px solid #ffbb8c;
          color: #FF6800;
          padding: 0 8px;
        }
      }
    }

    .contentRight {
      display: flex;

      .ant-radio-button-wrapper {
        padding: 0 4px;
      }
    }

    .ant-popover {
      .ant-popover-inner-content {
        .ant-tabs {
          .inPast {
            .startTime {
              // margin-bottom: 10px;
            }

            // display: flex;
            // justify-content: space-between;
          }

          .endTime {
            margin-top: 10px;
          }

          .site-calendar-demo-card {
            width: 300px;
            border: 1px solid #f0f0f0;
            border-radius: 2px;

            .ant-picker-input {
              padding-left: 15px;
            }
          }
        }
      }

      .shortcutOptions {
        .shortcutOptions-item {
          cursor: pointer;
          margin-right: 10px;
          margin-bottom: 10px;
          display: inline-block;
          width: 88px;
          height: 40px;
          border: 1px solid #d9d9d9;
          line-height: 40px;
          text-align: center;
          align-items: center;

          &:nth-child(2n) {
            margin-right: 0;
          }

          &:hover {
            background: #ff6800;
            color: #fff;
          }
        }
      }
    }
  }
}