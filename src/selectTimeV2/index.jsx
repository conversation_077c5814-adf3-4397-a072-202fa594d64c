/*
 * @Author: Wxw
 * @Date: 2022-07-26 14:57:35
 * @LastEditTime: 2022-08-01 11:58:10
 * @LastEditors: Wxw
 * @Description: SelectTimeV2时间组件
 * @FilePath: \datatist-wolf-static-cpnt\src\selectTimeV2\index.jsx
 */
import React, { useEffect, useReducer, useState } from 'react';
import { Popover, Tabs, DatePicker, Select, InputNumber } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import 'dayjs/locale/zh-cn';
import locale from 'antd/es/date-picker/locale/zh_CN';
import dayjs from 'dayjs';
import _ from 'lodash';
import { DataContext } from './context';
import './index.scss';
import Tab2 from './tab2';

const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { Option } = Select;
const reducer = (state, action) => ({ ...state, ...action });
const icon = <svg viewBox="64 64 896 896" focusable="false" data-icon="calendar" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z" /></svg>;
const shortcutOptions = {
  今天: [dayjs().startOf('day'), dayjs().endOf('day')],
  昨日: [dayjs().subtract(1, 'days').startOf('day'), dayjs().subtract(1, 'days').endOf('day')],
  本周: [dayjs().startOf('week'), dayjs().endOf('week')],
  上周: [dayjs().subtract(1, 'weeks').startOf('week'), dayjs().subtract(1, 'weeks').endOf('week')],
  本月: [dayjs().startOf('month'), dayjs().endOf('month')],
  上月: [dayjs().subtract(1, 'months').startOf('month'), dayjs().subtract(1, 'months').endOf('month')],
  今年: [dayjs().startOf('year'), dayjs().endOf('year')],
  去年: [dayjs().subtract(1, 'years').startOf('year'), dayjs().subtract(1, 'years').endOf('year')],
  过去的1小时: [dayjs().subtract(1, 'hours').startOf('hour'), dayjs().subtract(1, 'hours').endOf('hour')],
  过去的24小时: [dayjs().subtract(1, 'hours').subtract(24, 'hours').startOf('hour'), dayjs().subtract(1, 'hours').subtract(24, 'hours').endOf('hour')],
  过去的7天: [dayjs().subtract(1, 'days').subtract(7, 'days'), dayjs().subtract(1, 'days')],
  过去的14天: [dayjs().subtract(14, 'days'), dayjs().subtract(1, 'days')],
  过去的30天: [dayjs().subtract(30, 'days'), dayjs().subtract(1, 'days')],
  过去的60天: [dayjs().subtract(60, 'days'), dayjs().subtract(1, 'days')],
  过去的90天: [dayjs().subtract(90, 'days'), dayjs().subtract(1, 'days')],
  过去的180天: [dayjs().subtract(180, 'days'), dayjs().subtract(1, 'days')]
};

const conversion = {
  day: '天',
  week: '周',
  month: '月',
  inPast: '过去',
  future: '未来',
  specificTime: '具体时间',
  today: '今天',
  yesterday: '昨天'
};

export default function SelectTimeV2({ value, onChange }) {
  const [desc, setDesc] = useState([dayjs().subtract(8, 'days'), dayjs().subtract(1, 'days')]); // 用来显示的时间段 默认过去的7天
  const [info, setInfo] = useState([]);
  const [flag, setFlag] = useState(false);
  const [isShortcut, setIsShortcut] = useState(null);
  const [state, dispatch] = useReducer(reducer, {
    loading: false,
    tabKey: 'RELATIVE',
    visibleConversion: false,
    visibleIcon: false,
    value: [dayjs().startOf('day'), dayjs().endOf('day')],
    radio: '过去7天',
    inPastValue: {
      startSpecificTime: 'inPast',
      startType: 'day',
      startValue: 7,
      endValue: 1,
      endType: 'day',
      endSpecificTime: 'today'
    },
    info: {}// 用来保存具体时间
  });

  // 切换key的时候 默认inPast选择过去7天
  const changeTabs = key => {
    let _info = _.cloneDeep(info);
    if (key === 'ABSOLUTE') {
      _info = [{
        type: key,
        timestamp: dayjs(desc[0]).valueOf()
      }, {
        type: key,
        timestamp: dayjs(desc[1]).valueOf()
      }];
    } else if (key === 'RELATIVE') {
      _info = [{
        type: key,
        times: state.inPastValue.startValue,
        timeTerm: state.inPastValue.startType,
        startSpecificTime: state.inPastValue.startSpecificTime,
        timestamp: state.inPastValue.startSpecificTime === 'specificTime'
          ? dayjs(state.info.timestamp).valueOf()
          : null
      }, {
        type: key,
        times: state.inPastValue.endValue,
        timeTerm: state.inPastValue.endType,
        endSpecificTime: state.inPastValue.endSpecificTime
      }];
    }
    setInfo(_info);
    dispatch({ tabKey: key });
    onChange && onChange(_info);
    isShortcut && setIsShortcut(null);
  };

  useEffect(() => {
    // 把value[0].timestamp 和 value[1].timestamp 时间戳转换成moment
    if (!_.isEmpty(value)) {
      const start = dayjs(value[0]?.timestamp || dayjs().subtract(8, 'days'));
      const end = dayjs(value[1]?.timestamp || dayjs());
      setDesc([start, end]);
    }
    dispatch({
      tabKey: value[0]?.type || 'RELATIVE',
      inPastValue:
      {
        ...state.inPastValue,
        startSpecificTime: value[0]?.startSpecificTime || 'inPast',
        startType: value[0]?.timeTerm || 'day',
        startValue: value[0]?.times || 7,
        endSpecificTime: value[1]?.endSpecificTime || 'today',
        endType: value[1]?.timeTerm || 'day',
        endValue: value[1]?.times || 1
      }
    });
    // setInfo(value);
  }, [value]);

  // 触发固定时间的change
  const changeRangePicker = (date, dateString) => {
    const { tabKey } = state;
    let infos = [
      {
        type: tabKey,
        timeTerm: 'day',
        isPast: true,
        timestamp: dateString[0] ? dayjs(dateString[0]).valueOf() : 0
      },
      {
        type: tabKey,
        timeTerm: 'day',
        isPast: true,
        timestamp: dateString[1] ? dayjs(dateString[1]).valueOf() : 0
      }
    ];
    setInfo(infos);
    setDesc(date);
    onChange && onChange([...infos]);
    isShortcut && setIsShortcut(null);
  };

  // 触发inPast的change
  const inPast = async (e, type, startOrEnd) => {
    let _info = [{
      type: state.tabKey,
      times: state.inPastValue.startValue,
      timeTerm: state.inPastValue.startType,
      startSpecificTime: state.inPastValue.startSpecificTime,
      timestamp: state.inPastValue.startSpecificTime === 'specificTime'
        ? dayjs(state.info.timestamp).valueOf()
        : null
    }, {
      type: state.tabKey,
      times: state.inPastValue.endValue,
      timeTerm: state.inPastValue.endType,
      endSpecificTime: state.inPastValue.endSpecificTime
    }];

    if (startOrEnd === 'start') {
      if (type === 'select') {
        dispatch({ inPastValue: { ...state.inPastValue, startType: e } });
        _info[0].timeTerm = e;
      } else if (type === 'input') {
        dispatch({ inPastValue: { ...state.inPastValue, startValue: e, times: e } });
        _info[0].times = e;
      } else if (type === 'specificTime') {
        dispatch({ inPastValue: { ...state.inPastValue, startSpecificTime: e, times: e === 'epecificTime' ? dayjs(state.info.timestamp).valueOf() : null } });
        _info[0].timestamp = e === 'epecificTime' ? dayjs(state.info.timestamp).valueOf() : null;
        _info[0].startSpecificTime = e;
      }
    } else if (startOrEnd = 'end') {
      if (type === 'select') {
        dispatch({ inPastValue: { ...state.inPastValue, endType: e } });
        _info[1].timeTerm = e;
      } else if (type === 'input') {
        dispatch({ inPastValue: { ...state.inPastValue, endValue: e, times: e } });
        _info[1].times = e;
      } else if (type === 'specificTime') {
        dispatch({ inPastValue: { ...state.inPastValue, endSpecificTime: e } });
        _info[1].endSpecificTime = e;
        // _info[1].timestamp = e === 'epecificTime' ? dayjs(state.info.timestamp).valueOf() : null;
      }
    }
    // debugger;
    setInfo(_info);
    onChange && onChange(_info);
    isShortcut && setIsShortcut(null);
    // await updataInPastValue();
  };

  useEffect(() => {
    if (flag) {
      let _info = _.cloneDeep(info);
      _info = [{
        type: state.tabKey,
        times: state.inPastValue.startValue,
        timeTerm: state.inPastValue.startType,
        startSpecificTime: state.inPastValue.startSpecificTime,
        timestamp: state.inPastValue.startSpecificTime === 'specificTime'
          ? dayjs(state.info.timestamp).valueOf()
          : null
      }, {
        type: state.tabKey,
        times: state.inPastValue.endValue,
        timeTerm: state.inPastValue.endType,
        endSpecificTime: state.inPastValue.endSpecificTime
      }];
      setInfo(_info);
      onChange && onChange(_info);
      isShortcut && setIsShortcut(null);
    }
    setFlag(true);
  }, [state.info]);

  const save = e => {
    dispatch({ visibleConversion: false });
  };

  const renderSelectTimeV2 = () => {
    return (
      <div>
        <Tabs activeKey={state.tabKey} onChange={changeTabs}>
          <TabPane tab="固定时间" key="ABSOLUTE">
            <RangePicker
              value={desc}
              locale={locale}
              // ranges={{
              //   今天: [dayjs(), dayjs()],
              //   昨日: [dayjs().subtract(1, 'days'), dayjs().subtract(1, 'days')],
              //   本周: [dayjs().startOf('week'), dayjs().endOf('week')],
              //   本月: [dayjs().startOf('month'), dayjs().endOf('month')],
              //   过去7天: [dayjs().subtract(7, 'days'), dayjs()],
              //   过去30天: [dayjs().subtract(30, 'days'), dayjs()],
              //   过去60天: [dayjs().subtract(60, 'days'), dayjs()]
              // }}
              allowClear={false}
              showTime
              format="YYYY/MM/DD HH:mm:ss"
              onChange={changeRangePicker}
            />
          </TabPane>
          <TabPane tab="相对时间" key="RELATIVE">
            <div className="inPast">
              <div className="startTime">
                <span>开始时间 ：</span>
                <Select
                  value={state.inPastValue.startSpecificTime}
                  style={{ width: '25%', marginRight: '13px' }}
                  onChange={e => inPast(e, 'specificTime', 'start')}
                >
                  <Option value="specificTime">具体时间</Option>
                  <Option value="inPast">过去</Option>
                  <Option value="future">未来</Option>
                </Select>
                {state.inPastValue.startSpecificTime === 'specificTime'
                  && <span>{dayjs(state.info.timestamp).format('YYYY/MM/DD HH:mm:ss')}</span>}
                {state.inPastValue.startSpecificTime !== 'specificTime'
                  && <InputNumber
                    value={state.inPastValue.startValue}
                    style={{ width: '25%', marginRight: '13px' }}
                    allowClear
                    placeholder="输入数值"
                    min={0}
                    onChange={e => inPast(e, 'input', 'start')}
                  />}
                {state.inPastValue.startSpecificTime !== 'specificTime'
                  && <Select
                    value={state.inPastValue.startType}
                    style={{ width: '25%' }}
                    onChange={e => inPast(e, 'select', 'start')}
                  >
                    <Option value="day">天</Option>
                    <Option value="week">周</Option>
                    <Option value="month">月</Option>
                  </Select>}
              </div>
            </div>
            {state.inPastValue.startSpecificTime === 'specificTime' && <Tab2 save={save} value={value} />}

            <div className="endTime">
              <span>结束时间 ：</span>
              <Select
                value={state.inPastValue.endSpecificTime}
                style={{ width: '25%', marginRight: '13px' }}
                onChange={e => inPast(e, 'specificTime', 'end')}
              >

                <Option value="today">今天</Option>
                <Option value="yesterday">昨天</Option>
                <Option value="inPast">过去</Option>
                <Option value="future">未来</Option>
              </Select>
              {state.inPastValue.endSpecificTime !== 'today' && state.inPastValue.endSpecificTime !== 'yesterday' ? <InputNumber
                value={state.inPastValue.endValue}
                style={{ width: '25%', marginRight: '13px' }}
                allowClear
                placeholder="输入数值"
                min={0}
                onChange={e => inPast(e, 'input')}
              /> : null}
              {state.inPastValue.endSpecificTime !== 'today'
                && state.inPastValue.endSpecificTime !== 'yesterday'
                && <Select
                  value={state.inPastValue.endType}
                  style={{ width: '25%' }}
                  onChange={e => inPast(e, 'select')}
                >
                  <Option value="day">天</Option>
                  <Option value="week">周</Option>
                  <Option value="month">月</Option>
                </Select>}
            </div>
            {/* <Calendar fullscreen={false} onChange={onPanelChange} /> */}
            {/* <RangePicker
              value={state.value}
              // defaultValue={[dayjs().subtract(7, 'days'), dayjs()]}
              allowClear={false}
              format="YYYY/MM/DD HH:mm:ss"
              onChange={changeInpast}
              disabled={[false, true]}
            /> */}
          </TabPane>
        </Tabs>
      </div>
    );
  };

  const handleVisibleChange = () => {
    dispatch({ visibleConversion: !state.visibleConversion });
  };

  const handleVisibleIconChange = () => {
    dispatch({ visibleIcon: !state.visibleIcon });
  };

  const shortcut = () => {
    return (
      <div className="shortcutOptions">
        {Object.keys(shortcutOptions).map(item => {
          return (
            <span key={item} className="shortcutOptions-item" onClick={() => clickShortcut(item)}>{item}</span>
          );
        })}
      </div>
    );
  };

  const clickShortcut = async name => {
    const range = shortcutOptions[name];
    const _info = [{
      type: 'ABSOLUTE',
      timestamp: dayjs(range[0]).valueOf()
    }, {
      type: 'ABSOLUTE',
      timestamp: dayjs(range[1]).valueOf()
    }];
    setDesc(range);
    setInfo(_info);

    dispatch({ visibleIcon: false, tabKey: 'ABSOLUTE' });
    onChange && onChange(_info);
    setIsShortcut(name);
  };

  // 渲染时间范围
  const renderTimes = () => {
    if (state.tabKey === 'RELATIVE') {
      const { startSpecificTime, startValue, startType, endSpecificTime, endValue, endType } = state.inPastValue;
      let startTime; let endTime;
      if (startSpecificTime === 'specificTime') {
        startTime = dayjs(state.info.timestamp).format('YYYY/MM/DD HH:mm:ss');
      } else if (startSpecificTime === 'future') { // 未来
        startTime = dayjs().add(startValue, startType).valueOf();
      } else if (startSpecificTime === 'inPast') { // 过去
        startTime = dayjs().subtract(startValue + 1, startType).valueOf();
      }

      if (endSpecificTime === 'today') {
        endTime = dayjs().endOf('day').valueOf();
      } else if (endSpecificTime === 'yesterday') {
        endTime = dayjs().subtract(1, 'days').endOf('day').valueOf();
      } else if (endSpecificTime === 'inPast') {
        endTime = dayjs().subtract(endValue + 1, endType).endOf('day').valueOf();
      } else if (endSpecificTime === 'future') {
        endTime = dayjs().add(endValue, endType).endOf('day').valueOf();
      }
      return (
        <div className="customTime">
          <span>
            {dayjs(startTime).format('YYYY/MM/DD HH:mm:ss')} ~
            {dayjs(endTime).format('YYYY/MM/DD HH:mm:ss')}
          </span>
        </div>
      );
    } else if (!_.isEmpty(isShortcut)) {
      return (
        <div className="customTime"><span>{isShortcut}</span></div>
      );
    }
  };

  return (
    <div className="selectTimeV2">
      <DataContext.Provider value={{ state, dispatch }}>
        <div className="selectTimeV2-content">
          <Popover
            content={shortcut}
            getPopupContainer={() => document.getElementsByClassName('selectTimeV2-content')[0]}
            trigger="click"
            open={state.visibleIcon}
            className="shortcut"
            overlayStyle={{ width: '227px' }}
            onOpenChange={handleVisibleIconChange}
          >
            <div>
              <div className="icons">
                <span className="icon">{icon}</span>
                <DownOutlined height="32px" />
              </div>
              {renderTimes()}
            </div>
          </Popover>
          <Popover
            content={renderSelectTimeV2()}
            getPopupContainer={() => document.getElementsByClassName('selectTimeV2-content')[0]}
            trigger="click"
            placement="bottom"
            className="selectTimeV2-popover"
            open={state.visibleConversion}
            onOpenChange={handleVisibleChange}
          >

            {state.tabKey !== 'RELATIVE'
              ? <div className="contentLeft">
                <span className="time">
                  <span>{dayjs(desc[0]).format('YYYY/MM/DD HH:mm:ss')}</span>
                </span>
                <span className="symbol"> ~ </span>
                <span className="time">{dayjs(desc[1]).format('YYYY/MM/DD HH:mm:ss')}</span>
              </div>
              : <div className="contentLeft">
                <span className="time">
                  {state.inPastValue.startSpecificTime === 'specificTime'
                    ? dayjs(state.info.timestamp).format('YYYY/MM/DD HH:mm:ss')
                    : `${conversion[state.inPastValue.startSpecificTime]} ${state.inPastValue.startValue} ${conversion[state.inPastValue.startType]} `}
                </span>
                <span className="symbol"> ~ </span>
                <span className="time">{state.inPastValue.endSpecificTime === 'today' || state.inPastValue.endSpecificTime === 'yesterday'
                  ? `${conversion[state.inPastValue.endSpecificTime]} `
                  : `${conversion[state.inPastValue.endSpecificTime]} ${state.inPastValue.endValue} ${conversion[state.inPastValue.endType]}`}</span>
              </div>}
          </Popover>
        </div>
      </DataContext.Provider>
    </div>
  );
}
