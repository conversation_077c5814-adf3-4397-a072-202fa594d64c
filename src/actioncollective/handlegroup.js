// https://stackoverflow.com/questions/37949981/call-child-method-from-parent
import React from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import './filter.scss';
import { t } from '../utils/translation';

const HandelGroup = props => {
  const { onClick, mode, value } = props;

  if (mode === 'detail') return null;

  return (
    <div className="action_collective_button_group" hidden={mode === 'detail'}>
      <Button type="dashed" disabled={value.eventGroup} icon={<PlusOutlined />} onClick={() => onClick('eventGroup')}>{t('cpnt-LpNuvGVTV407')}</Button>
      <Button type="dashed" disabled={value.userProperty} icon={<PlusOutlined />} onClick={() => onClick('userProperty')}>{t('cpnt-a1G94chB6wvN')}</Button>
      <Button type="dashed" disabled={value.userLabel} icon={<PlusOutlined />} onClick={() => onClick('userLabel')}>{t('cpnt-GxBcFaIonxON')}</Button>
      <Button type="dashed" disabled={value.segment} icon={<PlusOutlined />} onClick={() => onClick('segment')}>{t('cpnt-k6p4z7j2bqbt')}</Button>
    </div>
  );
};

export default HandelGroup;
