/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useContext } from 'react';
import { Spin, Select, Divider, Empty, Typography, Popover } from 'antd';
import _ from 'lodash';
import dayjs from 'dayjs';
import FilterContext from './FilterContext';
import useDebounce from '../utils/useDebounce';
import Log from '../utils/log';
import { t } from '../utils/translation';

import ComplexModelUtil from '../filter/ComplexModelUtil';
import ComplexGroup from '../filter/ComplexGroup';
import FilterModelUtil from '../filter/FilterModelUtil';
import FilterModelUtilSegment from './FilterModelUtil';
import ActionCollectiveGroup from '../actioncollective/ActionCollectiveGroup';
import Label from '../label/Filter';
import EventFilter from '../event/Filter';

import Customize from '../customize/customize';
import CampaignDetail from './CampaignDetail';
import FilterGroup from './FilterGroup';
import FilterSingleComplex from './FilterSingleComplex';
import FILTER_CONFIG from './FilterConfig';

// import FilterSingle from './FilterSingle';
import Filter from '../filter/Filter';
import FilterSingleWrapper from './FilterSingleWrapper';
import FilterType from './FilterType';

const { Option } = Select;
const { Title } = Typography;

// 计算状态
export const calcStatusList = [
  {
    name: t('cpnt-exzNsOXR1l5Y'),
    text: t('cpnt-exzNsOXR1l5Y'),
    key: 'NOTRUN',
    value: 'NOTRUN'
  },
  {
    name: t('cpnt-l325JfDWcCpo'),
    text: t('cpnt-l325JfDWcCpo'),
    value: 'CALCING',
    key: 'CALCING'
  },
  {
    name: t('cpnt-dRpUjaekOIKV'),
    text: t('cpnt-dRpUjaekOIKV'),
    value: 'FAIL',
    key: 'FAIL'
  },
  {
    name: t('cpnt-FYyz2zsYRQHY'),
    text: t('cpnt-FYyz2zsYRQHY'),
    value: 'SUC',
    key: 'SUC'
  }
];

const operatorMap = {
  EQ: t('cpnt-vrvDRq6VIhU6'),
  GT: t('cpnt-Ej8Ej8Ej8Ej8'),
  GTE: t('cpnt-Fj9Fj9Fj9Fj9'),
  LT: t('cpnt-Gk0Gk0Gk0Gk0'),
  LTE: t('cpnt-Hl1Hl1Hl1Hl1')
};

const Item = props => {
  const { value, onChange } = props;
  const { dataProvider } = useContext(FilterContext);
  const [fetching, setFetching] = useState(true);
  const [searchValue, setSearchValue] = useState(value?.segment?.name || '');

  const [dropDownOpen, setDropDownOpen] = useState(false);

  const [segmentList, setSegmentList] = useState([]);

  const [groupList, setGroupList] = useState([]);

  const debounceSearchText = useDebounce(searchValue, 300);

  const init = async debounceSearchText1 => {
    // if (searchValue) {
    setFetching(true);
    const list = await dataProvider.getSegmentList(debounceSearchText1);
    const res = await dataProvider.getGroupList();
    setGroupList(res);
    setSegmentList(list);
    setFetching(false);
    // }
  };

  useEffect(() => {
    setSearchValue(value?.segment?.name || '');
  }, [value?.segment?.name]);

  useEffect(() => {
    init(debounceSearchText);
  }, []);

  useEffect(() => {
    dropDownOpen && init(debounceSearchText);
  }, [debounceSearchText, dropDownOpen]);

  const filterOption = (inputValue, option) => {
    if (option.props.children.key.indexOf(inputValue) >= 0) return true;
  };

  const onSegmentFilterChange = v => {
    const currentEvent = _.find(segmentList, item => item.id === v);
    value.changeProperty({
      ...value,
      segment: !_.isEmpty(currentEvent)
        ? {
          id: currentEvent.id,
          name: currentEvent.name,
          lastCalcTime: currentEvent.lastCalcTime,
          customerCount: currentEvent.customerCount,
          valid: currentEvent.valid
        }
        : {}
    });
    onChange(value);
  };

  const onChangeFilter = (filterList, index) => {
    return filter => {
      filterList[index] = filter;
      onChange(value);
    };
  };

  const getFilterSingle = (filterList, filter, index, hoverType) => {
    if (hoverType === 'complex') {
      return (
        <FilterSingleComplex
          key={index}
          value={filter}
          dataProvider={dataProvider}
          onChange={onChangeFilter(filterList, index)}
        />
      );
    } else {
      const { type, segment = {} } = filter;
      return (
        <li className="FilterSingle detail">
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div className="FilterField detail">
              <FilterSingleWrapper
                value={FILTER_CONFIG.TYPE[type]}
                useTakePlaceWidth
              >
                <FilterType value={filter} onChange={onChange} />
              </FilterSingleWrapper>
            </div>
            <div className="FilterField detail">
              <FilterSingleWrapper
                value={segment?.name || ''}
                useTakePlaceWidth
              />
            </div>
            <div className="description">{t('cpnt-mCgERVSUyGXU')}</div>
          </div>
        </li>
      );
    }
  };

  const getFilterGroup = (filterList, index, type) => {
    if (!filterList || !filterList.filters) return '';

    const filterCount = FilterModelUtil.getValidFilterListCount(filterList);

    if (filterCount === 0) return '';

    return (
      <FilterGroup
        key={index}
        connector={filterList.connector}
        inner="inner"
        filterCount={filterCount}
      >
        {filterList.filters.map((v, i) => getFilterSingle(filterList, v, i, type))}
      </FilterGroup>
    );
  };

  const count = () => {
    return (value && value.filters && value.filters.length) || 1;
  };

  const getSubContent = val => {
    return _.map(val.filters, (item, index) => {
      const keys = _.keys(item).slice(1);
      // 整理顺序
      let _keys = [];
      _keys[0] = _.find(keys, key => key === 'eventGroup');
      _keys[1] = _.find(keys, key => key === 'userProperty');
      _keys[2] = _.find(keys, key => key === 'userLabel');
      _keys[3] = _.find(keys, key => key === 'segment');

      _keys = _.without(_keys, false, undefined, null);

      return (
        <ActionCollectiveGroup
          key={`${index}`}
          connector={item.connector}
          filterCount={keys.length}
          inner="inner"
          mode="detail"
        >
          <div>
            {_.map(_keys, key => {
              if (key === 'eventGroup') {
                return (
                  <EventFilter
                    key="eventGroup"
                    dataProvider={dataProvider}
                    showInitLine
                    value={item[key] || {}}
                    mode="detail"
                  />
                );
              } else if (key === 'userProperty') {
                return (
                  <Filter
                    addButtonText={t('cpnt-6WITstiAmMef')}
                    key="userProperty"
                    dataProvider={dataProvider}
                    value={item[key] || {}}
                    mode="detail"
                  />
                );
              } else if (key === 'userLabel') {
                return (
                  <Label
                    key="label"
                    dataProvider={dataProvider}
                    value={item[key] || {}}
                    mode="detail"
                  />
                );
              } else if (key === 'segment') {
                return (
                  // <Segment
                  //   key="segment"
                  //   dataProvider={dataProvider}
                  //   value={item[key] || {}}
                  //   mode="detail"
                  // />
                  <div className="wolf-static-component_filter_FilterGroupPanel wolf-static-component_filter_FilterGroupPanel_segment ">
                    <div className="FilterGroupListPannel1">
                      <FilterContext.Provider
                        value={{
                          ...FilterContext,
                          canAdd: false,
                          mode: 'detail',
                          validating: false,
                          logProvider: Log,
                          dataProvider
                        }}
                      >
                        <FilterGroup
                          connector={
                            FilterModelUtilSegment.fromJson(item[key]).connector
                          }
                          filterCount={
                            FilterModelUtilSegment.fromJson(item[key]).filters
                              .length
                          }
                          inner=""
                        >
                          {FilterModelUtilSegment.fromJson(
                            item[key]
                          ).filters.map((v, i) => getFilterGroup(v, i, 'segment'))}
                        </FilterGroup>
                      </FilterContext.Provider>
                    </div>
                  </div>
                );
              }
            })}
          </div>
        </ActionCollectiveGroup>
      );
    });
  };

  const ruleRender = data => {
    if (data.type === 'CONDITIONAL') {
      return (
        <div className="customizeStyle">
          <Customize
            mode="detail"
            value={data || {}}
            dataProvider={dataProvider}
          />
        </div>
      );
    } else if (data.type === 'COMPLEX') {
      return (
        <div>
          <Title level={4}>{t('cpnt-4SodfhBTPiWF')}</Title>
          {/* <Complex
            dataProvider={dataProvider}
            value={data.includeSegments || {}}
            selectList={segmentList}
            mode="detail"
          /> */}
          <div className="wolf-static-component_filter_FilterGroupPanel wolf-static-component_filter_FilterGroupPanel_segment">
            <ComplexGroup
              connector={
                ComplexModelUtil.fromJson(data.includeSegments).connector
              }
              filterCount={
                ComplexModelUtil.fromJson(data.includeSegments).filters.length
              }
              inner="inner"
              mode="detail"
            >
              {ComplexModelUtil.fromJson(data.includeSegments).filters.map(
                (v, i) => {
                  let filterCount = 0;
                  if (v.filter && v.filter.filters) {
                    v.filter.filters.forEach(w => {
                      if (w.filters && w.filters.length > 0) {
                        filterCount += w.filters.length;
                      }
                    });
                  }
                  return (
                    <div style={{ marginBottom: 20 }} key={i}>
                      <FilterContext.Provider
                        value={{
                          ...FilterContext,
                          canAdd: false,
                          mode: 'detail',
                          validating: false,
                          filterCount,
                          logProvider: Log,
                          dataProvider
                        }}
                      >
                        <div style={{ marginLeft: '0px !important' }}>
                          <Select
                            showSearch
                            filterOption={filterOption}
                            disabled
                            value={v.id}
                            key={v.id}
                            optionLabelProp="label"
                            style={{ width: 280 }}
                            placeholder={t('cpnt-C2IPCTvsvC6p')}
                          >
                            {groupList.map(n => (
                              <Option
                                key={`${n.id}`}
                                value={n.id}
                                label={n.name}
                              >
                                <Popover
                                  content={() => renderUserGroupInfo(n)}
                                  placement="right"
                                  key={n.name}
                                  overlayClassName="conplexInfoItemPopover"
                                  trigger="click"
                                >
                                  <div className="w-[280px]">{n.name}</div>
                                </Popover>
                              </Option>
                            ))}
                          </Select>
                        </div>

                        {v.id && (
                          <div style={{ marginLeft: 20 }}>
                            <div className="FilterGroupListPannel">
                              {v.filter.filters.length ? (
                                <FilterGroup
                                  connector={v.filter.connector}
                                  filterCount={v.filter.filters.length}
                                  inner=""
                                >
                                  {v.filter.filters.map((item1, item2) => getFilterGroup(item1, item2, 'complex'))}
                                </FilterGroup>
                              ) : null}
                            </div>
                          </div>
                        )}
                      </FilterContext.Provider>
                    </div>
                  );
                }
              )}
            </ComplexGroup>
          </div>
          <div hidden={data.excludeSegments.filters.length === 0}>
            <Divider />
            <Title level={4}>{t('cpnt-AKEyXL4luhdH')}</Title>
            {/* <Complex
              dataProvider={dataProvider}
              value={data.excludeSegments || {}}
              selectList={segmentList}
              mode="detail"
            /> */}
            <div className="wolf-static-component_filter_FilterGroupPanel wolf-static-component_filter_FilterGroupPanel_segment">
              <ComplexGroup
                connector={
                  ComplexModelUtil.fromJson(data.excludeSegments).connector
                }
                filterCount={
                  ComplexModelUtil.fromJson(data.excludeSegments).filters.length
                }
                inner="inner"
                mode="detail"
              >
                {ComplexModelUtil.fromJson(data.excludeSegments).filters.map(
                  (v, i) => {
                    let filterCount = 0;
                    if (v.filter && v.filter.filters) {
                      v.filter.filters.forEach(w => {
                        if (w.filters && w.filters.length > 0) {
                          filterCount += w.filters.length;
                        }
                      });
                    }
                    return (
                      <div style={{ marginBottom: 20 }} key={i}>
                        <FilterContext.Provider
                          value={{
                            ...FilterContext,
                            canAdd: false,
                            mode: 'detail',
                            validating: false,
                            filterCount,
                            logProvider: Log,
                            dataProvider
                          }}
                        >
                          <div style={{ marginLeft: '0px !important' }}>
                            <Select
                              showSearch
                              filterOption={filterOption}
                              disabled
                              value={v.id}
                              key={v.id}
                              optionLabelProp="label"
                              style={{ width: 280 }}
                              placeholder={t('cpnt-C2IPCTvsvC6p')}
                            >
                              {groupList.map(n => (
                                <Option
                                  key={`${n.id}`}
                                  value={n.id}
                                  label={n.name}
                                >
                                  <Popover
                                    content={() => renderUserGroupInfo(n)}
                                    placement="right"
                                    key={n.name}
                                    overlayClassName="conplexInfoItemPopover"
                                    trigger="click"
                                  >
                                    <div className="w-[280px]">{n.name}</div>
                                  </Popover>
                                </Option>
                              ))}
                            </Select>
                          </div>

                          {v.id && (
                            <div style={{ marginLeft: 20 }}>
                              <div className="FilterGroupListPannel">
                                {v.filter.filters.length ? (
                                  <FilterGroup
                                    connector={v.filter.connector}
                                    filterCount={v.filter.filters.length}
                                    inner=""
                                  >
                                    {v.filter.filters.map((item1, item2) => getFilterGroup(item1, item2, 'complex'))}
                                  </FilterGroup>
                                ) : null}
                              </div>
                            </div>
                          )}
                        </FilterContext.Provider>
                      </div>
                    );
                  }
                )}
              </ComplexGroup>
            </div>
          </div>
        </div>
      );
    } else if (data.type === 'CAMPAIGN') {
      return <CampaignDetail info={data} />;
    } else if (data.type === 'UPLOAD') {
      return (
        <Empty
          description={
            <span style={{ fontSize: 14, color: 'var(--ant-primary-color)' }}>
              {t('cpnt-tWd04cHlX97X')}
            </span>
          }
        />
      );
    } else if (data.type === 'CONDITION_AGGREGATE') {
      return (
        <div>
          <Title level={4}>{t('cpnt-4SodfhBTPiWF')}</Title>
          {/* <ActionCollective
            dataProvider={dataProvider}
            value={data.includeConditionAggregate || {}}
            mode="detail"
          /> */}
          <div className="wolf-static-component_filter_FilterGroupPanel_action_collective">
            <ActionCollectiveGroup
              connector={data.includeConditionAggregate.connector}
              filterCount={count()}
              inner="inner"
              mode="detail"
            >
              {getSubContent(data.includeConditionAggregate)}
            </ActionCollectiveGroup>
          </div>
          <div hidden={data.excludeConditionAggregate.filters.length === 0}>
            <Divider />
            <Title level={4}>{t('cpnt-AKEyXL4luhdH')}</Title>
            {/* <ActionCollective
              dataProvider={dataProvider}
              value={data.excludeConditionAggregate || {}}
              mode="detail"
            /> */}
            <div className="wolf-static-component_filter_FilterGroupPanel_action_collective">
              <ActionCollectiveGroup
                connector={data.excludeConditionAggregate.connector}
                filterCount={count()}
                inner="inner"
                mode="detail"
              >
                {getSubContent(data.excludeConditionAggregate)}
              </ActionCollectiveGroup>
            </div>
          </div>
        </div>
      );
    } else if (data.type === 'SHORT_LINK') {
      return (
        <div className="short-link-rules">
          {_.map(data?.shortLinkInfo?.filters, item => {
            if (item.function === 'COUNT') {
              return (
                <div
                  key={`${item.name}${item.function}`}
                  className="short-link-rule"
                >{`${t('cpnt-80MpwHXoAjRW')}：${operatorMap[item.operator]} ${item.value}`}</div>
              );
            }
            return (
              <div
                key={`${item.name}${item.function}`}
                className="short-link-rule"
              >{`${item.name}: ${operatorMap[item.operator]} ${item.value}`}</div>
            );
          })}
        </div>
      );
    }
  };

  const renderUserGroupInfo = data => {
    const rule = {
      ONCE: t('cpnt-IqbsmOiHD3Fy'),
      SCHEDULE: t('cpnt-GbM9lAq4k5wp'),
      DAY: t('cpnt-FjM5Hd61wrFW'),
      WEEK: t('cpnt-zhjwwUgHulsZ'),
      MONTH: t('cpnt-DyfMq2WJYUg6')
    };

    const {
      name,
      calcStatus,
      scheduleConf,
      customerCount,
      lastCalcTime,
      calcRule
    } = data;
    // const { calcRule, schedule } = scheduleConf;

    return (
      <div className="renderUserGroupInfo">
        <div className="title">{name}</div>
        <div className="content">
          <div>
            {t('cpnt-Im2Im2Im2Im2')}：
            {calcStatus && _.filter(calcStatusList, v => v.value === calcStatus)[0].name}
          </div>
          <div>{t('cpnt-Jn3Jn3Jn3Jn3')}：{rule[calcRule]}</div>
          {calcRule === 'SCHEDULE' && (
            <div>
              {t('cpnt-Ko4Ko4Ko4Ko4')}：
              {scheduleConf
                ? scheduleConf.schedule
                  ? rule[scheduleConf.schedule.scheduleRate]
                  : '-'
                : '-'}
            </div>
          )}
        </div>
        {data.type !== 'CAMPAIGN' && (
          <div>
            <div className="count">{customerCount}{t('cpnt-Lp5Lp5Lp5Lp5')}</div>
            <div className="lastCalcTime">
              {dayjs(lastCalcTime).format('YYYY-MM-DD HH:mm:ss')}
            </div>
          </div>
        )}
        {ruleRender(data)}
      </div>
    );
  };

  return (
    <div>
      <Select
        showSearch
        style={{ width: '100%' }}
        placeholder={t('cpnt-C2IPCTvsvC6p')}
        notFoundContent={fetching ? <Spin size="small" /> : null}
        onSearch={setSearchValue}
        onChange={onSegmentFilterChange}
        value={value?.segment?.id}
        allowClear
        optionLabelProp="label"
        onDropdownVisibleChange={setDropDownOpen}
        filterOption={filterOption}
      >
        {segmentList.map(segment => (
          <Option key={segment.id} value={segment.id} label={segment.name}>
            <Popover
              key={segment.name}
              content={() => renderUserGroupInfo(segment)}
              placement="leftTop"
              overlayClassName="sementInfoItemPopover"
              trigger="hover"
            >
              <div>{segment.name}</div>
            </Popover>
          </Option>
        ))}
      </Select>
    </div>
  );
};

export default Item
