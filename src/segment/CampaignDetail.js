/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import React from 'react';
import dayjs from 'dayjs';
import { Button, InputNumber, Checkbox, Tag } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import intersection from '../img/img2.png';
import unionset from '../img/img1.png';
// import { DataContext } from '../context';
import './segment.scss';
import { t } from '../utils/translation';

const CampaignDetail = props => {
  const { info = {} } = props;

  return (
    <div className="selectCampaignDetailContent">
      <div className="totalPeople">{info.customerCount} {t('cpnt-E3bUw3m8IpKg')}</div>
      <div className="dateTime">
        {t('cpnt-kKTRubK0Vl3y')}{' '}
        {info.lastCalcTime
          && dayjs(info.lastCalcTime).format('YYYY-MM-DD HH:mm:ss')}
      </div>
      <div className="desc">{t('cpnt-hr1Q0bVLKSHm')}</div>
      <div className="selectCampaignDetail">
        <div className="left">
          <img
            className="imgStyle"
            alt=""
            src={info.connector === 'AND' ? intersection : unionset}
          />
          <div className="connectLine" />
        </div>
        <div className="right">
          <div className="timeCondition">
            <Button
              hidden={info?.timeCondition?.show === true}
              disabled
              className="conditionButton"
              type="dashed"
            >
              {t('cpnt-WZ1YrBaKMfbs')}
            </Button>
            <div
              hidden={info?.timeCondition?.show === false}
              className="conditionAll"
            >
              <div style={{ marginRight: 40 }}>
                {t('cpnt-i40vOVk963ka')}{' '}
                <span style={{ color: 'red' }}>
                  {' '}
                  {info?.timeCondition?.recentlyDays}{' '}
                </span>{' '}
                {t('cpnt-ZZaarID5x5Yt')}
              </div>
              <Checkbox disabled checked={info?.timeCondition?.onlyCreatedByMe}>
                {t('cpnt-xxYce5rVoXOZ')}
              </Checkbox>
            </div>
          </div>
          <div className="compaignList">
            <div name="campaignCalcLogNodes">
              <Button
                className="plusButton"
                icon={<PlusOutlined style={{ marginRight: 2 }} />}
                hidden={
                  info.campaignCalcLogNodes
                  && info.campaignCalcLogNodes.length > 0
                }
                disabled
                type="dashed"
              >
                {t('cpnt-3s65yWwhrUYG')}
              </Button>
              <div className="detailCompaignStyle">
                <div style={{ width: 70 }}>{t('cpnt-9Vx2kRuPkI0v')}</div>
                <div>
                  {info.campaignCalcLogNodes?.map(n => {
                    return (
                      <div style={{ marginBottom: 5 }} key={n?.campaign?.id}>
                        <Tag>
                          [{n?.campaign?.id}]{n.campaign?.name}
                        </Tag>
                        {n?.calcLogAndNodes?.length > 0 && <span>{t('cpnt-kb8zE5DTkI8E')} </span>}
                        {n?.calcLogAndNodes?.length > 0
                          && n?.calcLogAndNodes?.map(w => (
                            <Tag key={w?.calcLogId}>[{w?.calcLogId}]</Tag>
                          ))}
                        {n?.calcLogAndNodes?.length === 1
                          && n?.calcLogAndNodes[0]?.flowNodeIds?.length > 0 && (
                            <span>{t('cpnt-okG7NE6jth7R')} </span>
                          )}
                        {n?.calcLogAndNodes?.length === 1
                          && n?.calcLogAndNodes[0]?.flowNodeIds?.length > 0
                          && n.calcLogAndNodes[0]?.flowNodeIds?.map(h => (
                            <Tag key={h}>[{h}]</Tag>
                          ))}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="limitS">
        <Checkbox disabled checked={info.limits} />
        <span className="limitTitle">{t('cpnt-fCtmVa14GdIx')}</span>
        <InputNumber
          disabled
          style={{ width: 160 }}
          min={0}
          max={2000000}
          value={info.limits}
        />
        <span style={{ marginLeft: 8 }}>{t('cpnt-qa2TprSihQWo')}</span>
      </div>
    </div>
  );
};
export default CampaignDetail;
