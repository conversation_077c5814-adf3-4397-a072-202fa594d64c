/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useContext, useEffect, Fragment } from 'react';
import { CloseCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import _ from 'lodash';
import FilterSingleWrapper from './FilterSingleWrapper';
import FilterSegment from './FilterSegment';
import FilterType from './FilterType';
import FilterContext from './FilterContext';
import FILTER_CONFIG from './FilterConfig';
import { t } from '../utils/translation';

/**
 * 封装单个过滤组件
 * 当前组件失去焦点时校验数据
 * 当Field、Operator、Value结束编辑时，刺激当前组件获得焦点，以便贸然校验
 */
function FilterSingle({ value, onChange, onDelete }) {
  const { type, segment = {} } = value;

  const { mode } = useContext(FilterContext);

  // 校验器
  const [validator, setValidator] = useState({});

  useEffect(() => {
    if (mode !== 'edit') return;
    // 退出编辑
    setValidator(value.valid());
  }, [value.type, segment.id]);

  return (
    <li className={`FilterSingle ${mode}`}>
      <div style={{ display: 'flex', alignItems: 'center' }} hidden={mode !== 'edit' && !value.valid().isValid}>
        <div className={`FilterField ${mode} ${validator?.type && value.validating ? 'has-error' : ''}`}>
          <FilterSingleWrapper value={FILTER_CONFIG.TYPE[type]} useTakePlaceWidth>
            <FilterType value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div>
        <div className={`FilterField ${mode} ${validator?.id && value.validating ? 'has-error' : ''}`}>
          <FilterSingleWrapper value={segment?.name || ''} useTakePlaceWidth>
            <FilterSegment value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div>
        {
          mode === 'edit' && <>
            <div className="Ctroller">
              { value.validating && (validator?.id || validator?.type) && <Tooltip placement="topRight" title={_.head(_.values(validator.message))}>
                <div style={{ marginRight: 5 }}><QuestionCircleOutlined className="Validator" /></div>
              </Tooltip>}
              <CloseCircleOutlined className="delete" onClick={onDelete} />
            </div>
          </>
        }
        <div className="description">{t('cpnt-J2xk1kp4qIU9')}</div>
      </div>
    </li>
  );
}

export default FilterSingle;
