// 事件的操作类型输入域
import React from 'react';
import { Select } from 'antd';
import _ from 'lodash';
import FILTER_CONFIG from './FilterConfig';

const { Option } = Select;

const optionValues = _.keys(FILTER_CONFIG.TYPE);
const optionNames = _.values(FILTER_CONFIG.TYPE);

const FilterType = props => {
  const { value, onChange } = props;
  const handleChange = v => {
    value.changeProperty({ ...value, type: v });
    onChange(value);
  };

  return (
    <Select value={value.type} style={{ width: '100%' }} onChange={handleChange}>
      {
        _.map(optionValues, (item, index) => {
          return <Option key={`${item}`} value={item}>{optionNames[index]}</Option>;
        })
      }
    </Select>
  );
};

export default FilterType;
