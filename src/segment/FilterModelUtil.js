import _ from 'lodash';
import FilterModel from './FilterModel';
import Log from '../utils/log';

const log = Log.getLogger('FilterModelUtil');

export default class FilterModelUtil {
  /**
   *  创建过滤model
   */
  static createFilter() {
    return new FilterModel('INCLUDE', {});
  }

  /**
   * 创建过滤列表
   */
  static createFilterGroup(filters) {
    return {
      connector: 'AND',
      filters: filters || []
    };
  }

  /**
   * 向过滤组添加过滤组
   * @param {object} filter {connector, filters:[]}
   * @return 返回新创建的过滤组
   */
  static addFilterGroup(filterGroup, filter) {
    if (!filterGroup) {
      log.warn('addFilterGroup', '试图向空过滤组添加过滤组');
      return;
    }

    if (_.isEmpty(filterGroup.filters) || !_.isArray(filterGroup.filters)) {
      filterGroup.filters = [];
    }
    const _filterGroup = this.createFilterGroup([filter]);
    filterGroup.filters.push(_filterGroup);
    return _filterGroup;
  }

  /**
   * 向过滤组添加过滤组, 并且添加一个Filter
   * @param {object} filter {connector, filters:[]}
   */
  static addFilterGroupWithOneFilter(filterGroup) {
    return this.addFilterGroup(filterGroup, this.createFilter());
  }

  /**
   * 向过滤组中添加过滤
   * @param {filter} filterGroup {connector, filters:[]}
   * @return 返回FilterModel
   */
  static addFilter(filterGroup) {
    if (!filterGroup) {
      log.warn('addFilter', '试图向空过滤组添加过滤');
      return;
    }

    if (_.isEmpty(filterGroup.filters) || !_.isArray(filterGroup.filters)) {
      filterGroup.filters = [];
    }

    const _filter = this.crateFilter();
    filterGroup.filters.push(_filter);
    return _filter;
  }

  /**
   * 初始化一个有一个过滤组一个过滤器的FilterModel
   */
  static initCreateFilterGroup(showInitLine) {
    if (showInitLine) {
      return this.createFilterGroup([this.createFilterGroup([this.createFilter()])]);
    }
    return this.createFilterGroup([]);
  }

  static deleteEmptyFilterList(filterListGroup) {
    if (!filterListGroup || !filterListGroup.filters) {
      log.warn('deleteEmptyFilterList', '无法处理空filterListGroup');
      return;
    }
    filterListGroup.filters = filterListGroup.filters.filter(v => !_.isEmpty(v.filters));
  }

  static isFilterValid(filter) {
    return filter.valid().isValid;
  }

  static isFilterListGroupValid(filterListGroup, flag) {
    if (!flag && _.isEmpty(filterListGroup.filters)) {
      return false;
    }
    return filterListGroup.filters
      .flatMap(v => v.filters)
      .map(v => {
        v.validating = true;
        return v;
      })
      .map(v => this.isFilterValid(v))
      .reduce((a, b) => a && b, true);
  }

  static fromJson(json) {
    if (_.isEmpty(json)) {
      return this.initCreateFilterGroup();
    }
    const _json = _.cloneDeep(json);
    _json.filters.map(filterGroup => {
      filterGroup.filters.forEach((filter, i) => {
        filterGroup.filters[i] = FilterModel.fromJson(filter);
      });
      return filterGroup;
    });
    return _json;
  }

  static toJson(filterListGroup) {
    if (_.isEmpty(filterListGroup)) {
      return null;
    }
    const result = {};
    result.connector = filterListGroup.connector;
    result.filters = filterListGroup.filters.map(filterGroup => {
      return {
        connector: filterGroup.connector,
        filters: filterGroup.filters.map(filter => filter.toJson())
      };
    });
    return result;
  }

  static getValidJson(filterListGroup) {
    if (_.isEmpty(filterListGroup)) {
      return null;
    }
    const result = {};
    result.connector = filterListGroup.connector;
    result.filters = filterListGroup.filters.map(filterGroup => {
      return {
        connector: filterGroup.connector,
        filters: filterGroup.filters.filter(v => v.valid().isValid).map(filter => filter.toJson())
      };
    }).filter(v => !_.isEmpty(v.filters));
    if (_.isEmpty(result.filters)) return {};
    return result;
  }

  static getValidFilterListCount(filterGroup) {
    return filterGroup.filters
      .filter(v => v.valid().isValid)
      .map(() => 1)
      .reduce((a, b) => a + b, 0);
  }

  static getValidFilterListGroupCount(filterListGroup) {
    return filterListGroup.filters
      .map(v => this.getValidFilterListCount(v))
      .reduce((a, b) => a + b, 0);
  }
}
