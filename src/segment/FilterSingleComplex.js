import React from 'react';
import FilterSingleWrapperComplex from './FilterSingleWrapperComplex';
import FilterField from './FilterField';
import FilterOperator from './FilterOperator';
import FilterValue from './FilterValue';

/**
 * 封装单个过滤组件
 * 当前组件失去焦点时校验数据
 * 当Field、Operator、Value结束编辑时，刺激当前组件获得焦点，以便贸然校验
 */
function FilterSingleComplex({ value, dataProvider }) {
  const { fieldName } = value;

  return (
    <li className="FilterSingle detail">
      <div style={{ display: 'flex' }}>
        <div className="FilterField detail">
          <FilterSingleWrapperComplex value={fieldName}>
            <FilterField value={value} dataProvider={dataProvider} />
          </FilterSingleWrapperComplex>
        </div>
        <div className="FilterOperator detail">
          <FilterSingleWrapperComplex value={value.getOperatorShow()}>
            <FilterOperator value={value} />
          </FilterSingleWrapperComplex>
        </div>
        <div className="FilterValue detail">
          <FilterSingleWrapperComplex value={value.getValueShow()}>
            <FilterValue value={value} dataProvider={dataProvider} />
          </FilterSingleWrapperComplex>
        </div>
      </div>
    </li>
  );
}

export default FilterSingleComplex;
