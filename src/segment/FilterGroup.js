import React, { useContext } from 'react';
import FilterConnector from './FilterConnector';
import FilterContext from './FilterContext';

function FilterGroup({ connector, onChangeConnector, filterCount, children, inner }) {
  const { logProvider } = useContext(FilterContext);
  const log = logProvider.getLogger('FilterGroup');

  log.debug('connector', connector);

  return (
    <div className="FilterGroupPanel1">
      <div className="ConnectorPanel1" hidden={filterCount <= 1}>
        <div className="TopLine1" />
        <div className="VLine1" />
        <div className="BottomLine1" />
        <div className="Connector1">
          <FilterConnector value={connector} onChange={onChangeConnector} />
        </div>
      </div>
      <ul className={`FilterList1 ${inner}`}>{children}</ul>
    </div>
  );
}

export default FilterGroup;
