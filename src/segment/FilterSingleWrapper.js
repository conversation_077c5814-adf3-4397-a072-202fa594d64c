import React, { useContext, useRef, useEffect, useState } from 'react';
import FilterContext from './FilterContext';
import FilterSingleWrapperContext from './FilterSingleWrapperContext';

export default function FilterSingleWrapper({ value, children, useTakePlaceWidth }) {
  const { mode } = useContext(FilterContext);
  const takePlace = useRef(null);
  const [style, setStyle] = useState({});

  useEffect(() => {
    if (takePlace.current && mode === 'edit' && useTakePlaceWidth) {
      setStyle({ width: Math.max(takePlace.current.clientWidth, 55) + 45 });
    } else {
      setStyle({});
    }
  }, [takePlace, value, mode, useTakePlaceWidth]);

  if (!value) {
    value = '';
  }

  return (
    <FilterSingleWrapperContext.Provider value={{}}>
      <div className="FilterSingleWrapper" style={{ ...style }}>
        { mode === 'detail' && <div className="valueShow">
          {value}
          </div>}
        { mode === 'edit' && <div className="takePlace" ref={takePlace}>{value}</div>}
        { mode === 'edit' && children}
      </div>
    </FilterSingleWrapperContext.Provider>
  );
}
