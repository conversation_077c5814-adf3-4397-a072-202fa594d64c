import _ from 'lodash';
import dayjs from 'dayjs';
import FilterConfig from './FilterConfig';
import Log from '../utils/log';
import { t } from '../utils/translation';

const { operatorList, validator, DATE_TYPE_MAP, relativeTimeObj } = FilterConfig;
// 操作符map
const OPERATOR_MAP = operatorList.reduce((map, obj) => {
  map[obj.operator] = obj;
  return map;
}, {});

const log = Log.getLogger('FilterModel');

class FilterModel {
  static counter = 0;

  constructor(action, eventInfo, eventAggregateProperty, dateRange, eventFilterProperty, key) {
    this.action = action;
    this.eventInfo = eventInfo;
    this.eventAggregateProperty = eventAggregateProperty;
    this.dateRange = dateRange;
    this.eventFilterProperty = eventFilterProperty;
    this.key = key;
    log.debug('constructor', JSON.stringify(this));
  }

  static fromJson({ action, eventInfo, eventAggregateProperty, dateRange, eventFilterProperty }) {
    log.debug('fromJson', JSON.stringify({ action, eventInfo, eventAggregateProperty, dateRange, eventFilterProperty }));
    return new FilterModel(action, eventInfo, eventAggregateProperty, dateRange, eventFilterProperty, Math.random());
  }

  toJson() {
    const { action, eventInfo, eventAggregateProperty, dateRange, eventFilterProperty } = this;
    return { action, eventInfo, eventAggregateProperty, dateRange, eventFilterProperty };
  }

  setDirty(isDirty) {
    this.isDirty = isDirty;
  }

  /**
   * 校验filterModel，返回{
   *  isValid: (boolean) 是否有效
   *  fieldType: 表示fieldType有错误，次值为错误返回值
   *  operator: 同上
   *  value: {
   *    maxLen: 错误信息
   *    required: 错误信息
   *  }
   * }
   */
  valid() {
    const { eventAggregateProperty = {} } = this;

    const { propertyType, fun, operator, value, property = {} } = eventAggregateProperty;

    const { fieldType } = property || {};

    const result = { isValid: true, message: [] };

    // if (!action) {
    //   result.message.push('请选择事件行为');
    //   result.action = true;
    //   result.isValid = false;
    // }

    // if (!id) {
    //   result.message.push('请选择事件');
    //   result.id = true;
    //   result.isValid = false;
    // }

    // if (!propertyType || (propertyType === 'EVENT_PROPERTY' && !field)) {
    //   result.message.push('请选择事件属性');
    //   result.eventAggregateProperty = true;
    //   result.isValid = false;
    // }

    if (!value) {
      result.message.push(t('cpnt-iP8mVQw4YFBO'));
      result.value = true;
      result.isValid = false;
    }

    if (_.isString(value)) {
      const pre = /^\s+/g;
      const suffix = /\s+$/g;
      if (pre.test(value) || suffix.test(value)) {
        result.value = true;
        result.message.push(t('cpnt-1CygfUuAMFap'));
        result.isValid = false;
      }
    }

    if (!propertyType || (propertyType === 'EVENT_PROPERTY' && !fun)) {
      result.message.push(t('cpnt-pf6XKrQogDOx'));
      result.eventAggregateProperty = true;
      result.isValid = false;
    }

    if (propertyType === 'EVENT_PROPERTY' && !fun) {
      result.message.push('请选择计数函数');
      result.fun = true;
      result.isValid = false;
    }

    let typeValidator = validator[fieldType];

    if (propertyType === 'TIMES') {
      typeValidator = validator.INT;
    }

    // if (id && !typeValidator) {
    //   if (fieldType) {
    //     log.info('没有找到对应的校验器', `fieldType=${fieldType}`);
    //   }
    //   result.message.push('选择的过滤属性类型不存在');
    //   result.id = true;
    //   result.isValid = false;
    // }

    // if (!operator) {
    //   result.message.push('请输入操作符');
    //   result.operator = true;
    //   result.isValid = false;
    // }

    if (!['ALL', 'IS_NULL', 'IS_NOT_NULL', 'IS_TRUE', 'IS_FALSE', ''].includes(operator) && typeValidator) {
      if (_.isNil(value)) {
        // result.message.push('请输入属性值');
        // result.value = true;
        // result.isValid = false;
      } else if (_.isArray(value)) {
        // 数组类型返回验证器, 如果有错误的，返回一个
        const valueValidators = value.map(v => this._typeValueValid(typeValidator, v))
          .filter(v => v !== null);
        if (valueValidators.length > 0) {
          result.value = true;
          result.message.push(Object.values(valueValidators[0])[0]);
          result.isValid = false;
        }
      } else {
        const data = this._typeValueValid(typeValidator, value);
        if (data) {
          result.value = true;
          result.message.push(Object.values(data)[0]);
          result.isValid = false;
        }
      }
    }

    // if (_.isEmpty(dateRange)) {
    //   result.message.push('请选择时间范围');
    //   result.dateRange = true;
    //   result.isValid = false;
    // }

    return result;
  }

  /**
   * 类型验证器，可以验证STRING, LONG , DOUBLE等数据类型
   * @param {object} typeValidator {required, minLen, maxLen, regex, min, max}
   * @param {object} value 被验证的值
   */
  _typeValueValid(typeValidator, value) {
    const { required, minLen, maxLen, regex, min, max } = typeValidator.option;
    const errMessage = typeValidator.message;

    const message = {};

    if (required && (value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0))) {
      message.required = errMessage.required;
    }

    if (minLen !== null || minLen !== undefined) {
      if (value !== null && value !== undefined && value !== '' && value.toString().length < minLen) {
        message.minLen = errMessage.minLen;
      }
    }

    if (maxLen !== null || maxLen !== undefined) {
      if (value !== null && value !== undefined && value !== '' && value.toString().length > maxLen) {
        message.maxLen = errMessage.maxLen;
      }
    }

    if (regex && !new RegExp(regex).test(value)) {
      message.regex = errMessage.regex;
    }

    if (min !== null && min !== undefined && Number.parseInt(value) < min) {
      message.min = errMessage.min;
    }

    if (max !== null && max !== undefined && Number.parseInt(value) > max) {
      message.max = errMessage.max;
    }

    log.debug('_typeValueValid', typeValidator, value, message);

    if (_.isEmpty(message)) {
      return null;
    }

    return message;
  }

  changeProperty(property) {
    const { action, eventInfo, eventAggregateProperty, dateRange, eventFilterProperty } = property;
    this.action = action;
    this.eventInfo = eventInfo;
    this.eventAggregateProperty = eventAggregateProperty;
    this.dateRange = dateRange;
    this.eventFilterProperty = eventFilterProperty;
  }

  changeOperator(operator) {
    this.eventAggregateProperty.operator = operator;
    this.eventAggregateProperty.value = null;
  }

  changeValue(value) {
    this.eventAggregateProperty.value = value;
  }

  changePropertyValue(eventFilterProperty) {
    this.eventFilterProperty = eventFilterProperty;
  }

  changeEventAggregateProperty(eventAggregateProperty) {
    this.eventAggregateProperty = eventAggregateProperty;
  }

  clearProperty() {
    this.action = 'DONE';
    this.eventInfo = {};
    this.eventAggregateProperty = {};
    this.dateRange = [];
    this.eventFilterProperty = null;
  }

  getOperatorShow() {
    return OPERATOR_MAP[this.eventAggregateProperty?.operator]?.name;
  }

  getDateTypeShow() {
    return DATE_TYPE_MAP[this.dateType];
  }

  getTimeShow() {
    if (this.dateType === 'ABSOLUTE') {
      return dayjs(this.times).format('YYYY-MM-DD');
    } else if (this.dateType === 'RELATIVE') {
      return relativeTimeObj[this.times] || `${this.times} 天前`;
    }
    return null;
  }

  getValueShow() {
    const { eventAggregateProperty = {} } = this;
    const { value, operator } = eventAggregateProperty;

    if (!value) {
      return '';
    }

    switch (operator) {
      case 'EQ':
      case 'NE':
      case 'GT':
      case 'GTE':
      case 'LT':
      case 'LTE':
      case 'LIKE':
      case 'NOT_LIKE':
      case 'START_WITH':
      case 'NOT_START_WITH':
      case 'END_WITH':
      case 'NOT_END_WITH':
        return value;
      case 'IN':
      case 'NOT_IN':
        return `[${_.join((value), ',')}]`;
      case 'IS_NOT_NULL':
      case 'IS_NULL':
        return '';
      case 'BETWEEN':
        return `[${value[0]}-${value[1]}]`;
      default:
        return '';
    }
  }

  isValueCanEdit() {
    switch (this.operator) {
      case 'IS_NOT_NULL':
      case 'IS_NULL':
      case 'IS_TRUE':
      case 'IS_FALSE':
        return false;
      default:
        return true;
    }
  }
}

export default FilterModel;
