// /* eslint-disable react-hooks/exhaustive-deps */
// import React, { useState, useRef, useEffect, useCallback, forwardRef, useContext } from 'react';
// import { DownOutlined } from '@ant-design/icons';
// import { Menu, Dropdown, Input, List, Spin, Select } from 'antd';
// import dayjs from 'dayjs';
// import _ from 'lodash';
// import FilterContext from './FilterContext';
// import useDebounce from '../utils/useDebounce';

// const { Option } = Select;

// const Item = (props, ref) => {
//   const { value, onChange } = props;
//   const { dataProvider } = useContext(FilterContext);
//   const [fetching, setFetching] = useState(true);
//   const [searchValue, setSearchValue] = useState(value?.eventAggregateProperty?.propertyType === 'TIMES' ? 'TIMES' : value?.eventAggregateProperty?.property?.fieldName);
//   const [dropDownOpen, setDropDownOpen] = useState(false);
//   const debounceSearchText = useDebounce(searchValue, 300);

//   const [eventPropertyList, setEventPropertyList] = useState([]);

//   useEffect(() => {
//     setSearchValue(value?.eventAggregateProperty?.propertyType === 'TIMES' ? 'TIMES' : value?.eventAggregateProperty?.property?.fieldName);
//   }, [value]);

//   const init = async (debounceSearchText1, eventId) => {
//     setFetching(true);
//     let list = [];
//     if (!_.isNil(value?.eventInfo?.id)) {
//       list = await dataProvider.getEventPropertyList(debounceSearchText1, eventId);
//     }
//     if (!_.find(list, item => item.field === 'TIMES')) {
//       list.unshift({ field: 'TIMES', fieldName: '次数' });
//     }

//     setEventPropertyList(list);
//     setFetching(false);
//   };

//   useEffect(() => {
//     init(debounceSearchText, value?.eventInfo?.id);
//   }, []);

//   useEffect(() => {
//     dropDownOpen && init(debounceSearchText, value?.eventInfo?.id);
//   }, [debounceSearchText, value?.eventInfo?.id, dropDownOpen]);

//   const filterOption = (inputValue, option) => {
//     if (option.props.children.indexOf(inputValue) >= 0) return true;
//   };

//   const onEventFilterChange = v => {
//     debugger
//     let currentEvent = _.find(eventPropertyList, item => item.field === v);

//     if (currentEvent) {
//       value.changeProperty({
//         ...value,
//         eventAggregateProperty: {
//           propertyType: currentEvent.field === 'TIMES' ? 'TIMES' : 'EVENT_PROPERTY',
//           property: currentEvent.field === 'TIMES' ? {} : currentEvent,
//           fun: currentEvent.field === 'TIMES' ? 'COUNT' : '',
//           operator: '',
//           value: ''
//         }
//       });
//     } else {
//       value.changeProperty({
//         ...value,
//         eventAggregateProperty: {
//           propertyType: '',
//           property: {},
//           fun: '',
//           operator: '',
//           value: ''
//         }
//       });
//     }

//     onChange(value);
//   };

//   return (
//     <div>
//       <Select
//         showSearch
//         style={{ width: '100%' }}
//         placeholder="请选择事件属性"
//         notFoundContent={fetching ? <Spin size="small" /> : null}
//         onSearch={setSearchValue}
//         onChange={onEventFilterChange}
//         value={value?.eventAggregateProperty?.propertyType === 'TIMES' ? 'TIMES' : `${value?.eventAggregateProperty?.property?.level1} ${value?.eventAggregateProperty?.property?.level2} ${value?.eventAggregateProperty?.property?.field}`}
//         allowClear
//         onDropdownVisibleChange={setDropDownOpen}
//         filterOption={filterOption}
//       >
//         {
//           eventPropertyList.map(property => <Option key={`${property.field}`} value={property.field === 'TIMES' ? 'TIMES' : `${property?.level1} ${property?.level2} ${property?.field}`}>
//             {property.fieldName}
//           </Option>)
//         }
//       </Select>
//     </div>
//   );
// };

// export default forwardRef(Item);

import React, { useState, useEffect, useContext } from 'react';
import { DownOutlined, SyncOutlined } from '@ant-design/icons';
import { Dropdown, Menu, Input } from 'antd';
import _ from 'lodash';
import FilterContext from './FilterContext';
import useDebounce from '../utils/useDebounce';
import { t } from '../utils/translation';

export default function FilterField({ value, onChange, eventAggregateProperty }) {
  const { logProvider, dataProvider } = useContext(FilterContext);

  const [propertyMap, setPropertyMap] = useState({});
  const [searchText, setSearchText] = useState(value.field);
  const [menuVisible, setMenuVisible] = useState(false);
  const [fetching, setFetching] = useState(false);
  const debounceSearchText = useDebounce(searchText, 200);

  const [fetchPropertyList] = useState(() => {
    return dp => {
      return async name => {
        log.debug('fetchPropertyList');
        let plist = await dp.getEventPropertyList(name, value?.eventInfo?.id);
        // if (!_.find(plist, item => item.field === 'TIMES')) {
        //   plist.unshift({
        //     field: 'TIMES',
        //     fieldName: '次数',
        //     isEnum: false,
        //     level1: '次数',
        //     level2: ''
        //   });
        // }
        let levelMap = _.groupBy(plist, v => v.level1);
        _.keys(levelMap).forEach(level1 => {
          levelMap[level1] = _.groupBy(levelMap[level1], v => v.level2);
        });
        setFetching(false);
        setPropertyMap(levelMap);
        return levelMap;
      };
    };
  });

  const [log] = useState(logProvider.getLogger('FilterField'));

  useEffect(() => {
    if (menuVisible) {
      setSearchText('');
    } else {
      setSearchText(value?.eventAggregateProperty?.propertyType === 'TIMES' ? '次数' : value?.eventAggregateProperty?.property?.fieldName || '');
    }
  }, [value?.eventAggregateProperty?.propertyType, value?.eventAggregateProperty?.property?.fieldName, menuVisible]);

  useEffect(() => {
    if (!menuVisible) return;
    setFetching(true);
    if (debounceSearchText === value?.eventAggregateProperty?.property?.fieldName) return;
    fetchPropertyList(dataProvider)(debounceSearchText);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceSearchText, dataProvider, menuVisible, fetchPropertyList]);

  const propertyMenu = properties => {
    return properties
      .filter(
        v => !searchText
          || v.field.indexOf(searchText) >= 0
          || v.fieldName.indexOf(searchText) >= 0
      )
      .map((p, i) => {
        return (
          <Menu.Item key={`${p.level1}_${p.level2}_${p.fieldName}_${i}`} onClick={() => onSelectProperty(p)}>
            {p.fieldName}
          </Menu.Item>
        );
      });
  };

  const level2Menu = (children, level2) => {
    if (!level2) {
      if (_.isArray(children)) {
        return propertyMenu(children);
      }
    }

    return (
      <Menu.ItemGroup title={level2} key={level2}>
        {propertyMenu(children)}
      </Menu.ItemGroup>
    );
  };

  const onSelectProperty = property => {
    value.changeEventAggregateProperty({
      propertyType: property.field === 'TIMES' ? 'TIMES' : 'EVENT_PROPERTY',
      property: property.field === 'TIMES' ? {} : property,
      fun: property.field === 'TIMES' ? 'COUNT' : '',
      operator: '',
      value: eventAggregateProperty.value
    });
    onChange(value);
    setMenuVisible(false);
  };

  const level1Menu = (children, level1) => {
    if (!level1) {
      if (_.isArray(children)) {
        return propertyMenu(children);
      } else if (_.isObject(children) && !Object.entries(children)[0][0]) {
        return propertyMenu(Object.entries(children)[0][1]);
      }
    }

    return (
      <Menu.ItemGroup title={level1} key={level1}>
        {_.map(children, level2Menu)}
      </Menu.ItemGroup>
    );
  };

  const menu = () => {
    return (
      <Menu style={{ maxHeight: 400, overflowY: 'auto', maxWidth: 500, overflowX: 'auto' }}>
        {(fetching && _.isEmpty(propertyMap)) ? <Menu.Item><SyncOutlined spin />loading...</Menu.Item> : _.map(propertyMap, level1Menu)}
      </Menu>
    );
  };

  return (
    <Dropdown arrow getPopupContainer={triggerNode => triggerNode.parentNode} overlay={menu} trigger={['click']} onOpenChange={setMenuVisible}>
      <div className="clickWrapper">
        <Input
          className="ant-dropdown-link"
          placeholder={t('cpnt-xk5O2EBD5y7M')}
          // suffix={}
          onChange={e => setSearchText((e.target.value))}
          onFocus={event => event.target.select()}
          value={searchText}
        />
        <DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />
      </div>
    </Dropdown>
  );
}
