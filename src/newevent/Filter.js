// https://stackoverflow.com/questions/37949981/call-child-method-from-parent
import React, { useState, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import _ from 'lodash';
import FilterListGroup from './FilterListGroup';
import FilterContext from './FilterContext';
import FilterModelUtil from './FilterModelUtil';
import FilterConfig from './FilterConfig';
import Log from '../utils/log';
import './event.scss';
import { t } from '../utils/translation';

const log = Log.getLogger('Filter');
/**
 * 过滤器组件
 * mode有两种形式，edit|detail，edit模式下可编辑，默认是edit
 * @param {object} 过滤值 value
 * @param {object} 数据提供器 {getPropertyList: (name) => data}
 * @param {function} 响应改变 onChange
 */
const Event = forwardRef((props, ref) => {
  // The component instance will be extended
  // with whatever you return from the callback passed
  // as the second argument
  useImperativeHandle(ref, () => ({
    isValid(flag) {
      setContext({ ...context, validating: true });
      let subFilterValid = filterGroupRef.current.isValid(true);
      return FilterModelUtil.isFilterListGroupValid(value, flag) && subFilterValid;
    },
    getFilterCount() {
      return value.filters
        .map(v => v.filters?.length)
        .reduce((a, b) => a + b, 0);
    }
  }));

  const filterGroupRef = useRef(null);

  const { dataProvider, onChange, mode, showInitLine = true } = props;
  const { maxFilterCount } = FilterConfig;
  const [value, setValue] = useState(props.value && props.value.filters && props.value.filters.length > 0 ? FilterModelUtil.fromJson(props.value) : FilterModelUtil.initCreateFilterGroup(showInitLine));
  // 用于保存父子组件的value状态，比对这个值和props.value可以判断需不需要刷新
  const [propsValue, setPropsValue] = useState({});

  const [context, setContext] = useState({
    dataProvider,
    logProvider: Log,
    canAdd: true,
    mode: mode || 'edit',
    filterCount: 0,
    validating: false
  });

  // 父组件改变，设置当前值
  useEffect(() => {
    log.debug('props.value changed', JSON.stringify(props.value));
    // 仅当不相等是才刷新文档
    if (!_.isEqual(props.value, propsValue)) {
      setValue(FilterModelUtil.fromJson(props.value));
    }
  }, [props.value, propsValue]);

  // 过滤组改变，修改过滤条数
  // 父组件改变，设置当前值
  useEffect(() => {
    const filterCount = value.filters
      .map(v => v.filters?.length)
      .reduce((a, b) => a + b, 0);
    setContext({
      dataProvider,
      logProvider: Log,
      canAdd: filterCount < maxFilterCount,
      mode: mode || 'edit',
      filterCount
    });
  }, [value, maxFilterCount, dataProvider, mode]);

  /**
   * 添加过滤组
   */
  const addFilterGroup = () => {
    FilterModelUtil.addFilterGroupWithOneFilter(value);
    onValueChange(value);
  };

  /**
   * 当过滤组修改时回调
   * @param {object} v 过滤组
   */
  const onValueChange = v => {
    log.debug('onValueChanged', JSON.stringify(v));
    // if (FilterModelUtil.isFilterListGroupValid(v)) {
    //   log.debug('Filter is valid, call Father\'s onChange');
    //   onChange(FilterModelUtil.toJson(v));
    // }
    const _v = FilterModelUtil.getValidJson(v);
    setPropsValue(_v);
    onChange(_v, v);
    setValue({ ...v });
  };

  log.debug('Before Render', JSON.stringify(value), context.canAdd);

  return (
    <FilterContext.Provider value={context}>
      <div className="wolf-static-component_filter_FilterGroupPanel_event">
        <FilterListGroup ref={filterGroupRef} value={value} onChange={onValueChange} />
      </div>
      <div className="FilterAdder">
        <Button
          type="dashed"
          icon={<PlusOutlined />}
          onClick={addFilterGroup}
          disabled={!context.canAdd}
          hidden={context.mode === 'detail'}
        >
          {t('cpnt-mKB7uE1Zpkme')}
        </Button>
        <span style={{ marginLeft: 10 }} hidden={context.mode === 'detail'}>
          [{context.filterCount}/{maxFilterCount}] {t('cpnt-2pIPTkdcDUy5', { maxFilterCount })}
        </span>
      </div>
    </FilterContext.Provider>
  );
});

export default Event;
