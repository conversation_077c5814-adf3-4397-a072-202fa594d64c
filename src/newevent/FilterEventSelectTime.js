// 事件的操作类型输入域
import React from 'react';
// import { SelectTime } from 'wolf-static-cpnt';
import dayjs from 'dayjs';
import SelectTime from '../selectTime';
import 'dayjs/locale/zh-cn';

dayjs.locale('zh-cn');

const FilterEventSelectTime = props => {
  const { value, onChange } = props;
  const { dateRange } = value;

  const handleChange = v => {
    value.changeProperty({ ...value, dateRange: v });
    onChange(value);
  };

  return (
    <SelectTime
      data={dateRange}
      showTime
      style={{ width: 360 }}
      onChange={handleChange}
    />
  );
};

export default FilterEventSelectTime;
