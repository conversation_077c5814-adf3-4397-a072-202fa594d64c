// 事件的操作类型输入域
import React, { useState, useEffect } from 'react';
import { Select } from 'antd';
import _ from 'lodash';
import FILTER_CONFIG from './FilterConfig';
import { t } from '../utils/translation';

const { Option } = Select;

const FilterEventFunction = props => {
  const { value, onChange } = props;
  const { eventAggregateProperty = {} } = value;

  const [functions, setFunctions] = useState([]);

  useEffect(() => {
    if (eventAggregateProperty.propertyType) {
      let funs = [];
      if (eventAggregateProperty?.propertyType === 'TIMES') {
        funs = FILTER_CONFIG.CONDITIONFUN.TIMES;
      } else {
        funs = FILTER_CONFIG.CONDITIONFUN[eventAggregateProperty?.property?.fieldType];
      }

      setFunctions(funs);
    }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventAggregateProperty.propertyType, eventAggregateProperty.property?.field]);

  const handleChange = v => {
    value.changeProperty({ ...value, eventAggregateProperty: { ...eventAggregateProperty, fun: v } });
    onChange(value);
  };

  return (
    <Select value={eventAggregateProperty.fun} style={{ width: '100%' }} onChange={handleChange} placeholder={t('cpnt-RROziHsAzp7C')}>
      {
        _.map(functions, item => {
          return <Option key={`${item.value}`} value={item.value}>{item.name}</Option>;
        })
      }
    </Select>
  );
};

export default FilterEventFunction;
