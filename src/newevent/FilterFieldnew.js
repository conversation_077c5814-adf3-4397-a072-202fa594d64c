/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useRef, useEffect, useCallback, useContext } from 'react';
import { DownOutlined } from '@ant-design/icons';
import { Menu, Dropdown, Input, List, Spin } from 'antd';
import _ from 'lodash';
import FilterContext from './FilterContext';

const Item = props => {
  const { value } = props;
  const { dataProvider } = useContext(FilterContext);
  const [visible, setVisible] = useState(false);
  const [searchValue, setSearchValue] = useState(value.label);
  const [name, setName] = useState('');
  const [selectedKeys, setSelectedKeys] = useState(value.id ? [value.id] : []);
  const [objData] = useState({});
  const [, setSelectedTag] = useState(value.label || '请选择');
  const [loading] = useState(false);
  const searchRef = useRef(null);

  const [listData, setListData] = useState([]);

  useEffect(() => {
    setSearchValue(value.label);
    setSelectedKeys(value.id ? [value.id] : []);
    setSelectedTag(value.label || '请选择');
  }, [value]);

  useEffect(() => {
    const init = async () => {
      // if (name) {
      const list = await dataProvider.getEventList({ name });
      setListData(list);
      // }
    };
    if (visible) {
      init();
    }
  }, [name, visible]);

  // const getAsyncData = async key => {
  //   setLoading(true);
  //   const result = await dataProvider.findAllCategory({ categoryId: parseInt(key.split('.')[1]) });
  //   const data = [];
  //   const obj = {};
  //   result.categoryList && result.categoryList.forEach(n => {
  //     data.push({ title: n.name, key: `key.${n.id}` });
  //   });
  //   result.userLabels && result.userLabels.forEach(n => {
  //     data.push({ title: n.name, key: `${n.id}`, isLeaf: true });
  //     obj[`${n.id}`] = n;
  //   });
  //   setObjData({ ...objData, ...obj });
  //   setLoading(false);
  //   return data;
  // };

  // const onSelect = (data, info) => {
  //   if (info.node.isLeaf) {
  //     setSelectedKeys(data);
  //     handleVisibleChange(false, data);
  //     const label = objData[data[0]].name;
  //     const fieldType = objData[data[0]].dataType;
  //     value.changeProperty({ id: data[0], label, fieldType: fieldType || 'STRING' });
  //     onChange(value);
  //   }
  // };

  // const onMouseEnter = async event => {
  //   if (event.node.isLeaf) {
  //     const _tagInfp = objData[event.node.key];
  //     if (_tagInfp) {
  //       const _items = await dataProvider.getTagValuesById(_tagInfp.id);
  //       const param = { ..._tagInfp, userLabelValues: _items };
  //       setTagInfo(param);
  //     }
  //   }
  // };

  const handleVisibleChange = (flag, data) => {
    if (!flag) {
      const lastData = data && data[0] || selectedKeys[0] || '';
      const _value = lastData && objData[lastData].name;
      setSearchValue(_value);
      setName('');
      _value && setSelectedTag(_value);
      searchRef.current.blur();
    }
    setVisible(flag);
  };

  const debouncedSave = useCallback(_.debounce(nextValue => setName(nextValue), 1000), []);

  const onSearch = function (e) {
    const _value = e.target.value;
    setSearchValue(_value);
    debouncedSave(_value);
  };

  const menu = (
    <Menu>
      <div style={{ display: 'flex', padding: '10px 0px' }}>
        <div style={{ minWidth: 200, borderRight: '1px solid #E1E1E1', maxHeight: 280, overflow: 'auto' }}>
          {/* {!loading ? _.map(listData, item => {
            return <Menu.Item className="ant-menu-item ant-menu-item-only-child ant-menu-item-selected" key={item.id}>
              {item.name}
            </Menu.Item>;
          }) : <Spin />} */}
          {!loading ? <List
            size="small"
            bordered
            dataSource={listData}
            renderItem={item => <List.Item>{item.name}</List.Item>}
          /> : <Spin />}
        </div>
        {/* <div style={{ width: 205, maxHeight: 280, overflowY: 'auto', padding: '3px 10px' }}>
          <div style={{ fontFamily: 'PingFangSC-Regular', fontSize: 14, color: '#000000' }}>{tagInfo.name}</div>
          <div style={{ opacity: 0.5, fontFamily: 'PingFangSC-Regular', fontSize: 14, color: '#000000' }}>{tagInfo.remark}</div>
          <div style={{ marginTop: 25, fontFamily: 'PingFangSC-Regular', fontSize: 14, color: 'rgba(0,0,0,0.65)' }}>标签值</div>
          <div>
            {tagInfo.userLabelValues && tagInfo.userLabelValues.filter((w, i) => i <= 9).map(n => <Tag key={n.id}>{n.value.length <= 10 ? n.value : `${n.value.substr(0, 10)}...`}</Tag>)}
            {tagInfo.userLabelValues && tagInfo.userLabelValues.length > 10 && '...'}
          </div>
          <div style={{ marginTop: 28, fontFamily: 'PingFangSC-Regular', fontSize: 14, color: 'rgba(0,0,0,0.65)' }}>创建人：{tagInfo.updateUserName}</div>
          <div style={{ marginTop: 20, fontFamily: 'PingFangSC-Regular', fontSize: 12, color: 'rgba(0,0,0,0.65)' }}>创建时间：{dayjs(tagInfo.createTime).format('YYYY-MM-DD HH:mm:ss')}</div>
        </div> */}
      </div>
    </Menu>
  );

  return (
    <div>
      <Dropdown
        trigger={['click']}
        onOpenChange={handleVisibleChange}
        getPopupContainer={triggerNode => triggerNode.parentNode}
        open={visible}
        overlay={menu}
      >
        <Input
          className="ant-dropdown-link"
          ref={searchRef}
          placeholder="事件"
          suffix={<DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />}
          onChange={onSearch}
          onFocus={() => { setSearchValue(''); setName(''); }}
          value={searchValue}
        />
      </Dropdown>
    </div>
  );
};

export default Item
