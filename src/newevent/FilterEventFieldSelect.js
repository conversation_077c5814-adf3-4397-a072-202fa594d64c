/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useContext } from 'react';
import { Spin, Select } from 'antd';
import _ from 'lodash';
import FilterContext from './FilterContext';
import useDebounce from '../utils/useDebounce';
import { t } from '../utils/translation';

const { Option } = Select;

const Item = props => {
  const { value, onChange } = props;
  const { dataProvider } = useContext(FilterContext);
  const [fetching, setFetching] = useState(true);
  const [searchValue, setSearchValue] = useState(value?.eventInfo?.displayName || '');
  const [dropDownOpen, setDropDownOpen] = useState(false);
  const [eventList, setEventList] = useState([]);

  const debounceSearchText = useDebounce(searchValue, 300);

  useEffect(() => {
    setSearchValue(value?.eventInfo?.displayName || '');
  }, [value?.eventInfo?.displayName]);

  const init = async debounceSearchText1 => {
    // if (searchValue) {
    setFetching(true);
    const list = await dataProvider.getEventList(debounceSearchText1);
    setEventList(list.content);
    setFetching(false);
    // }
  };

  useEffect(() => {
    init(debounceSearchText);
  }, []);

  useEffect(() => {
    dropDownOpen && init(debounceSearchText);
  }, [debounceSearchText, dropDownOpen]);

  const filterOption = (inputValue, option) => {
    if (option.props.children.indexOf(inputValue) >= 0) return true;
  };

  const onEventFilterChange = v => {
    const currentEvent = _.find(eventList, item => item.id === v);
    value.changeProperty({
      ...value,
      eventInfo: !_.isEmpty(currentEvent) ? {
        id: currentEvent.id,
        eventType: currentEvent.eventType,
        displayName: currentEvent.name,
        eventNameValue: currentEvent.eventNameValue,
        filter: currentEvent.filter,
        specialPropertyMappingList: currentEvent.specialPropertyMappingList
      } : {},
      eventAggregateProperty: {},
      eventFilterProperty: null
    });
    onChange(value);
  };

  return (
    <div>
      <Select
        showSearch
        style={{ width: '100%' }}
        placeholder={t('cpnt-JWAWXbY9eWVa')}
        notFoundContent={fetching ? <Spin size="small" /> : null}
        onSearch={_.debounce(setSearchValue, 500)}
        onChange={onEventFilterChange}
        value={value?.eventInfo?.id}
        allowClear
        onDropdownVisibleChange={setDropDownOpen}
        filterOption={filterOption}
      >
        {
          eventList.map(event => <Option key={`${event.id}`} value={event.id}>
            {event.name}
          </Option>)
        }
      </Select>
    </div>
  );
};

export default Item
