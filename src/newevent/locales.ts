export default {
  zh_CN: {
    'cpnt-kxF3zvOs5CRr': '等于',
    'cpnt-jOhgLs5cMvpE': '不等于',
    'cpnt-HqitC4RtNzL2': '包含',
    'cpnt-yW9cyaY5pWl0': '不包含',
    'cpnt-MUh1hCF6kxt7': '有值',
    'cpnt-H81Avy0woRgT': '空值',
    'cpnt-LOQyQLXMNPnI': '且',
    'cpnt-DX80n28lVGHl': '或',
    'cpnt-WuNVRvsXMkks': '请输入日期',
    'cpnt-TTE9dRIUzlvR': '请输入日期时间',
    'cpnt-UyVvA3PHHTf5': '请输入',
    'cpnt-7FOXX2fsJbMr': '固定时间',
    'cpnt-99uC197y6SxK': '相对时间',
    'cpnt-Cn7iOgyPkY6x': '今天',
    'cpnt-28pWneMFrtoR': '昨天',
    'cpnt-JP7h114oa6nv': '前天',
    'cpnt-bRaDymyauQUE': '做过',
    'cpnt-5j0MCRL4qE2b': '未做过',
    'cpnt-Pc6SC9McQnR6': '计数',
    'cpnt-xiBUMzsRYekF': '求合',
    'cpnt-Id8PwR7TWGHD': '平均值',
    'cpnt-4xt799uoPhIf': '最大值',
    'cpnt-L7Ct08lJk6Gl': '最小值',
    'cpnt-iP8mVQw4YFBO': '请选择自定义名称',
    'cpnt-1CygfUuAMFap': '自定义名称前后不能有空格',
    'cpnt-pf6XKrQogDOx': '请选择事件属性',
    'cpnt-mKB7uE1Zpkme': '添加指标',
    'cpnt-2pIPTkdcDUy5': '最多添加{{maxFilterCount}}条指标, 指标过滤条件最多5个',
    'cpnt-xk5O2EBD5y7M': '请选择',
    'cpnt-rva2Ysmx7Tkq': '自定义名称',
    'cpnt-ap2n3s4YJilo': '最小值',
    'cpnt-ZDcQWGpjO5H7': '至',
    'cpnt-raQ4DVxeatXT': '最大值',
    'cpnt-LpZ7W30tOq5t': '值',
    'cpnt-JWAWXbY9eWVa': '请选择事件',
    'cpnt-zjUfqnY4fjE1': '行为',
    'cpnt-RROziHsAzp7C': '统计条件',
    'cpnt-IGBn4vkP0YVV': '操作符'
  },
  en_US: {
    'cpnt-kxF3zvOs5CRr': 'Equal',
    'cpnt-jOhgLs5cMvpE': 'Not equal',
    'cpnt-HqitC4RtNzL2': 'Contains',
    'cpnt-yW9cyaY5pWl0': 'Does not contain',
    'cpnt-MUh1hCF6kxt7': 'Has value',
    'cpnt-H81Avy0woRgT': 'Empty',
    'cpnt-LOQyQLXMNPnI': 'AND',
    'cpnt-DX80n28lVGHl': 'OR',
    'cpnt-WuNVRvsXMkks': 'Please enter date',
    'cpnt-TTE9dRIUzlvR': 'Please enter date time',
    'cpnt-UyVvA3PHHTf5': 'Please enter',
    'cpnt-7FOXX2fsJbMr': 'Fixed time',
    'cpnt-99uC197y6SxK': 'Relative time',
    'cpnt-Cn7iOgyPkY6x': 'Today',
    'cpnt-28pWneMFrtoR': 'Yesterday',
    'cpnt-JP7h114oa6nv': 'Day before yesterday',
    'cpnt-bRaDymyauQUE': 'Done',
    'cpnt-5j0MCRL4qE2b': 'Not done',
    'cpnt-Pc6SC9McQnR6': 'Count',
    'cpnt-xiBUMzsRYekF': 'Sum',
    'cpnt-Id8PwR7TWGHD': 'Average',
    'cpnt-4xt799uoPhIf': 'Maximum',
    'cpnt-L7Ct08lJk6Gl': 'Minimum',
    'cpnt-iP8mVQw4YFBO': 'Please select custom name',
    'cpnt-1CygfUuAMFap': 'Custom name cannot have spaces at the beginning or end',
    'cpnt-pf6XKrQogDOx': 'Please select event property',
    'cpnt-mKB7uE1Zpkme': 'Add Metric',
    'cpnt-2pIPTkdcDUy5': 'Add up to {{maxFilterCount}} metrics, up to 5 filter conditions per metric',
    'cpnt-xk5O2EBD5y7M': 'Please select',
    'cpnt-rva2Ysmx7Tkq': 'Custom name',
    'cpnt-ap2n3s4YJilo': 'Minimum value',
    'cpnt-ZDcQWGpjO5H7': 'to',
    'cpnt-raQ4DVxeatXT': 'Maximum value',
    'cpnt-LpZ7W30tOq5t': 'Value',
    'cpnt-JWAWXbY9eWVa': 'Please select event',
    'cpnt-zjUfqnY4fjE1': 'Behavior',
    'cpnt-RROziHsAzp7C': 'Statistical condition',
    'cpnt-IGBn4vkP0YVV': 'Operator'
  }
};
