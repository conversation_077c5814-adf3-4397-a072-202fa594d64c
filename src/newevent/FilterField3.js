/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useRef, useEffect, useCallback, useContext } from 'react';
import { DownOutlined } from '@ant-design/icons';
import { Menu, Dropdown, Input, Tree, Tag, Spin } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import FilterContext from './FilterContext';

const { DirectoryTree } = Tree;

const findObj = (data, key) => {
  let info = {};
  data.forEach(n => {
    if (n.key === key) {
      info = n;
    }
    n.children && n.children.forEach(w => {
      if (w.key === key) {
        info = w;
      }
      w.children && w.children.forEach(h => {
        if (h.key === key) {
          info = h;
        }
      });
    });
  });
  return info;
};

const getLevelList = (_list, _categoryList) => {
  const _treeData = [];
  let _expandedKeys = [];
  const categoryObj = {};
  _categoryList.forEach(n => {
    categoryObj[`key.${n.id}`] = n;
  });

  _list.forEach(n => {
    const info = categoryObj[`key.${n.categoryId}`];
    if (!info) {
      _treeData.push({ title: n.name, key: `${n.id}`, isLeaf: true });
    } else {
      const codeArr = `${info.path}${info.id}`.split(',').filter(x => x !== '0').map(h => `key.${h}`);
      _expandedKeys = _expandedKeys.concat(codeArr);
      if (codeArr[0] && !_treeData.find(item => item.key === codeArr[0])) {
        _treeData.push({ title: categoryObj[codeArr[0]].name, key: codeArr[0] });
      }
      if (codeArr[1]) {
        const category1Info = findObj(_treeData, codeArr[0]);
        if (!category1Info.children) {
          category1Info.children = [{ title: categoryObj[codeArr[1]].name, key: codeArr[1] }];
        } else if (category1Info.children.findIndex(w => w.key === codeArr[1]) === -1) {
          category1Info.children.push({ title: categoryObj[codeArr[1]].name, key: codeArr[1] });
        }
      }
      if (codeArr[2]) {
        const category2Info = findObj(_treeData, codeArr[1]);
        if (!category2Info.children) {
          category2Info.children = [{ title: categoryObj[codeArr[2]].name, key: codeArr[2] }];
        } else if (category2Info.children.findIndex(w => w.key === codeArr[2]) === -1) {
          category2Info.children.push({ title: categoryObj[codeArr[2]].name, key: codeArr[2] });
        }
      }
      const categoryInfo = findObj(_treeData, `key.${n.categoryId}`);
      if (!categoryInfo.children) {
        categoryInfo.children = [{ title: n.name, key: `${n.id}`, isLeaf: true }];
      } else {
        categoryInfo.children.push({ title: n.name, key: `${n.id}`, isLeaf: true });
      }
    }
  });
  _treeData.sort((a, b) => (a.isLeaf ? 1 : 0) - (b.isLeaf ? 1 : 0));
  return { _treeData, _expandedKeys };
};

const getObjData = _list => {
  const data = {};
  _list.forEach(n => {
    data[n.id] = n;
  });
  return data;
};

const updateTreeData = (list, key, children) => {
  return list.map(node => {
    if (node.key === key) {
      return { ...node, children };
    } else if (node.children) {
      return { ...node, children: updateTreeData(node.children, key, children) };
    }
    return node;
  });
};

const Item = props => {
  const { value, onChange } = props;
  const { dataProvider } = useContext(FilterContext);
  const [treeData, setTreeData] = useState([]);
  const [visible, setVisible] = useState(false);
  const [tagInfo, setTagInfo] = useState({});
  const [searchValue, setSearchValue] = useState(value.label);
  const [name, setName] = useState('');
  const [selectedKeys, setSelectedKeys] = useState(value.id ? [value.id] : []);
  const [objData, setObjData] = useState({});
  const [selectedTag, setSelectedTag] = useState(value.label || '请选择');
  const [loadedKeys, setLoadedKeys] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [flag, setFlag] = useState(true);
  const [loading, setLoading] = useState(false);
  const searchRef = useRef(null);

  useEffect(() => {
    setSearchValue(value.label);
    setSelectedKeys(value.id ? [value.id] : []);
    setSelectedTag(value.label || '请选择');
  }, [value]);

  useEffect(() => {
    const init = async () => {
      if (name) {
        const _list = await dataProvider.getTagList({ name });
        const _categoryList = await dataProvider.findCategoryByProjectId();
        const result = getLevelList(_list, _categoryList);
        setLoadedKeys([]);
        setTreeData(result._treeData);
        setExpandedKeys(result._expandedKeys);
        const data = getObjData(_list);
        setObjData({ ...objData, ...data });
        setFlag(true);
      } else if (flag) {
        setFlag(false);
        const data = await getAsyncData('key.0');
        setLoadedKeys([]);
        setExpandedKeys([]);
        setTreeData(data);
      }
    };
    if (visible) {
      init();
    }
  }, [name, visible]);

  const getAsyncData = async key => {
    setLoading(true);
    const result = await dataProvider.findAllCategory({ categoryId: parseInt(key.split('.')[1]) });
    const data = [];
    const obj = {};
    result.categoryList && result.categoryList.forEach(n => {
      data.push({ title: n.name, key: `key.${n.id}` });
    });
    result.userLabels && result.userLabels.forEach(n => {
      data.push({ title: n.name, key: `${n.id}`, isLeaf: true });
      obj[`${n.id}`] = n;
    });
    setObjData({ ...objData, ...obj });
    setLoading(false);
    return data;
  };

  const onLoadData = async ({ key, children }) => {
    if (children) {
      return;
    }
    const data = await getAsyncData(key);
    setTreeData(origin => updateTreeData(origin, key, data));
  };

  const onSelect = (data, info) => {
    if (info.node.isLeaf) {
      setSelectedKeys(data);
      handleVisibleChange(false, data);
      const label = objData[data[0]].name;
      const fieldType = objData[data[0]].dataType;
      value.changeProperty({ id: data[0], label, fieldType: fieldType || 'STRING' });
      onChange(value);
    }
  };

  const onMouseEnter = async event => {
    if (event.node.isLeaf) {
      const _tagInfp = objData[event.node.key];
      if (_tagInfp) {
        const _items = await dataProvider.getTagValuesById(_tagInfp.id);
        const param = { ..._tagInfp, userLabelValues: _items };
        setTagInfo(param);
      }
    }
  };

  // const renderTreeNodes = data => data.map(item => {
  //   if (item.children) {
  //     return (
  //       <TreeNode title={item.title} key={item.key} dataRef={item}>
  //         {renderTreeNodes(item.children)}
  //       </TreeNode>
  //     );
  //   }
  //   // eslint-disable-next-line react/jsx-props-no-spreading
  //   return <TreeNode key={item.key} {...item} dataRef={item} />;
  // });
  const handleVisibleChange = (_flag, data) => {
    if (!_flag) {
      const lastData = data && data[0] || selectedKeys[0] || '';
      const _value = lastData && objData[lastData].name;
      setSearchValue(_value);
      setName('');
      _value && setSelectedTag(_value);
      searchRef.current.blur();
    }
    setVisible(_flag);
  };

  const debouncedSave = useCallback(_.debounce(nextValue => setName(nextValue), 1000), []);

  const onSearch = function (e) {
    const _value = e.target.value;
    setSearchValue(_value);
    debouncedSave(_value);
  };

  const menu = (
    <Menu>
      <div style={{ display: 'flex', padding: '10px 0px' }}>
        <div style={{ minWidth: 200, borderRight: '1px solid #E1E1E1', maxHeight: 280, overflow: 'auto' }}>
          {!loading ? <DirectoryTree
            showIcon
            onSelect={onSelect}
            onMouseEnter={onMouseEnter}
            selectedKeys={selectedKeys}
            loadedKeys={loadedKeys}
            onLoad={data => setLoadedKeys(data)}
            loadData={onLoadData}
            expandedKeys={expandedKeys}
            onExpand={setExpandedKeys}
            treeData={treeData}
          >
            {/* {renderTreeNodes(treeData)} */}
          </DirectoryTree> : <Spin />}
        </div>
        <div style={{ width: 205, maxHeight: 280, overflowY: 'auto', padding: '3px 10px' }}>
          <div style={{ fontFamily: 'PingFangSC-Regular', fontSize: 14, color: '#000000' }}>{tagInfo.name}</div>
          <div style={{ opacity: 0.5, fontFamily: 'PingFangSC-Regular', fontSize: 14, color: '#000000' }}>{tagInfo.remark}</div>
          <div style={{ marginTop: 25, fontFamily: 'PingFangSC-Regular', fontSize: 14, color: 'rgba(0,0,0,0.65)' }}>标签值</div>
          <div>
            {tagInfo.userLabelValues && tagInfo.userLabelValues.filter((w, i) => i <= 9).map(n => <Tag key={n.id}>{n.value.length <= 10 ? n.value : `${n.value.substr(0, 10)}...`}</Tag>)}
            {tagInfo.userLabelValues && tagInfo.userLabelValues.length > 10 && '...'}
          </div>
          <div style={{ marginTop: 28, fontFamily: 'PingFangSC-Regular', fontSize: 14, color: 'rgba(0,0,0,0.65)' }}>创建人：{tagInfo.updateUserName}</div>
          <div style={{ marginTop: 20, fontFamily: 'PingFangSC-Regular', fontSize: 12, color: 'rgba(0,0,0,0.65)' }}>创建时间：{dayjs(tagInfo.createTime).format('YYYY-MM-DD HH:mm:ss')}</div>
        </div>
      </div>
    </Menu>
  );

  return (
    <div>
      <Dropdown
        trigger={['click']}
        onOpenChange={handleVisibleChange}
        getPopupContainer={triggerNode => triggerNode.parentNode}
        open={visible}
        overlay={menu}
      >
        {/* <Search
          ref={searchRef}
          onChange={onSearch}
          onFocus={() => { setSearchValue(''); setName(''); }}
          value={searchValue}
          placeholder={selectedTag}
        /> */}
        <Input
          className="ant-dropdown-link"
          ref={searchRef}
          placeholder={selectedTag}
          suffix={<DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />}
          onChange={onSearch}
          onFocus={() => { setSearchValue(''); setName(''); }}
          value={searchValue}
        />
        {/* <TreeNode visible={false} /> */}
      </Dropdown>
    </div>
  );
};

export default Item
