/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import React from 'react';
import dayjs from 'dayjs';
import { Button, InputNumber, Checkbox, Tag } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import intersection from '../img/img2.png';
import unionset from '../img/img1.png';
// import { DataContext } from '../context';
import { t } from '../utils/translation';
import './filter.scss';

const CampaignDetail = props => {
  const { info = {} } = props;

  return (
    <div className="selectConplexDetailContent">
      <div className="totalPeople">{info.customerCount} {t('cpnt-i09yvc01OXK7')}</div>
      <div className="dateTime">
        {t('cpnt-TCltc64PQmV6')}{' '}
        {info.lastCalcTime
          && dayjs(info.lastCalcTime).format('YYYY-MM-DD HH:mm:ss')}
      </div>
      <div className="desc">{t('cpnt-YOmPEKUxI93a')}</div>
      <div className="selectCampaignDetail">
        <div className="left">
          <img
            className="imgStyle"
            alt=""
            src={info.connector === 'AND' ? intersection : unionset}
          />
          <div className="connectLine" />
        </div>
        <div className="right">
          <div className="timeCondition">
            <Button
              hidden={info?.timeCondition?.show === true}
              disabled
              className="conditionButton"
              type="dashed"
            >
              {t('cpnt-Fv161dkHDvoq')}
            </Button>
            <div
              hidden={info?.timeCondition?.show === false}
              className="conditionAll"
            >
              <div style={{ marginRight: 40 }}>
                {t('cpnt-2BZyL017DmuE')}{' '}
                <span style={{ color: 'red' }}>
                  {' '}
                  {info?.timeCondition?.recentlyDays}{' '}
                </span>{' '}
                {t('cpnt-3PTYho29KkTs')}
              </div>
              <Checkbox disabled checked={info?.timeCondition?.onlyCreatedByMe}>
                {t('cpnt-3QWRkzMmYK8z')}
              </Checkbox>
            </div>
          </div>
          <div className="compaignList">
            <div name="campaignCalcLogNodes">
              <Button
                className="plusButton"
                icon={<PlusOutlined style={{ marginRight: 2 }} />}
                hidden={
                  info.campaignCalcLogNodes
                  && info.campaignCalcLogNodes.length > 0
                }
                disabled
                type="dashed"
              >
                {t('cpnt-kovtQaAyscUV')}
              </Button>
              <div className="detailCompaignStyle">
                <div style={{ width: 70 }}>{t('cpnt-nQUlqvlaSXQ0')}</div>
                <div>
                  {info.campaignCalcLogNodes?.map(n => {
                    return (
                      <div style={{ marginBottom: 5 }} key={n?.campaign?.id}>
                        <Tag>
                          [{n?.campaign?.id}]{n.campaign?.name}
                        </Tag>
                        {n?.calcLogAndNodes?.length > 0 && <span>{t('cpnt-IR8mRMGgp3IS')} </span>}
                        {n?.calcLogAndNodes?.length > 0
                          && n?.calcLogAndNodes?.map(w => (
                            <Tag key={w?.calcLogId}>[{w?.calcLogId}]</Tag>
                          ))}
                        {n?.calcLogAndNodes?.length === 1
                          && n?.calcLogAndNodes[0]?.flowNodeIds?.length > 0 && (
                            <span>{t('cpnt-jwe5K57vqm9v')} </span>
                          )}
                        {n?.calcLogAndNodes?.length === 1
                          && n?.calcLogAndNodes[0]?.flowNodeIds?.length > 0
                          && n.calcLogAndNodes[0]?.flowNodeIds?.map(h => (
                            <Tag key={h}>[{h}]</Tag>
                          ))}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="limitS">
        <Checkbox disabled checked={info.limits} />
        <span className="limitTitle">{t('cpnt-8PzIse5VsJ10')}</span>
        <InputNumber
          disabled
          style={{ width: 160 }}
          min={0}
          max={2000000}
          value={info.limits}
        />
        <span style={{ marginLeft: 8 }}>{t('cpnt-VafdWyJrkRB2')}</span>
      </div>
    </div>
  );
};
export default CampaignDetail;
