// https://stackoverflow.com/questions/37949981/call-child-method-from-parent
import React, {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle
} from 'react';
import { CloseCircleFilled, PlusOutlined } from '@ant-design/icons';
import { But<PERSON>, Select, Typography, Popover, Divider, Empty } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import FilterContext from './FilterContext';
import ComplexModelUtil from './ComplexModelUtil';
import ComplexGroup from './ComplexGroup';
import FilterConfig from './FilterConfig';
import Filter from './Filter';
import Label from '../label/Filter';
import EventFilter from '../event/Filter';

// import ActionCollective from '../actioncollective/actioncollective';
import ActionCollectiveGroup from '../actioncollective/ActionCollectiveGroup';
import Customize from '../customize/customize';
import CampaignDetail from './CampaignDetail';

import Log from '../utils/log';
import './filter.scss';
import Segment from '../segment/Filter';
import FilterListGroup from './FilterListGroup';
import { t } from '../utils/translation';

const { Option } = Select;
const { Title } = Typography;

// 计算状态
export const calcStatusList = [
  {
    name: t('cpnt-VgCVMMUOhlce'),
    text: t('cpnt-VgCVMMUOhlce'),
    key: 'NOTRUN',
    value: 'NOTRUN'
  },
  {
    name: t('cpnt-f07ZcxPFCzvW'),
    text: t('cpnt-f07ZcxPFCzvW'),
    value: 'CALCING',
    key: 'CALCING'
  },
  {
    name: t('cpnt-TkAygQxGUg8r'),
    text: t('cpnt-TkAygQxGUg8r'),
    value: 'FAIL',
    key: 'FAIL'
  },
  {
    name: t('cpnt-REJQAG6qqTFR'),
    text: t('cpnt-REJQAG6qqTFR'),
    value: 'SUC',
    key: 'SUC'
  }
];

const operatorMap = {
  EQ: t('cpnt-ly6EZYFYvgXy'),
  GT: t('cpnt-ReQFFpklII9g'),
  GTE: t('cpnt-rRwbzVT5NHKf'),
  LT: t('cpnt-LqbyiUIKVl1R'),
  LTE: t('cpnt-oeNNNpcfgKMh')
};

const log = Log.getLogger('Filter');

/**
 * 过滤器组件
 * mode有两种形式，edit|detail，edit模式下可编辑，默认是edit
 * @param {object} props.value 过滤值
 * @param {object} props.dataProvider 数据提供器 {getPropertyList: (name) => data}
 * @param {function} props.onChange 响应改变
 * @param {'edit'|'detail'} props.mode 模式
 * @param {React.Ref<any>} ref 传递的 ref
 * @returns {JSX.Element}
 * @type {React.ForwardRefExoticComponent<any & React.RefAttributes<any>>}
 */
const Complex = forwardRef((props, ref) => {
  // The component instance will be extended
  // with whatever you return from the callback passed
  // as the second argument
  useImperativeHandle(ref, () => ({
    isValid() {
      setContext({ ...context, validating: true });
      return ComplexModelUtil.isFilterListGroupValid(value);
    }
  }));

  const { dataProvider, onChange, mode, selectList, isUserGroup } = props;
  const { maxFilterCount } = FilterConfig;
  const [value, setValue] = useState(ComplexModelUtil.initCreateFilterGroup());
  // 用于保存父子组件的value状态，比对这个值和props.value可以判断需不需要刷新
  const [propsValue, setPropsValue] = useState({});
  const [segmentList, setSegmentList] = useState([]);

  const [context, setContext] = useState({
    dataProvider,
    logProvider: Log,
    mode: mode || 'edit',
    validating: false,
    isUserGroup
  });

  useEffect(() => {
    const list = [...selectList];
    if (props.value.filters) {
      props.value.filters.forEach(n => {
        if (n.id && list.findIndex(k => k.id === n.id) === -1) {
          const info = { ...n };
          delete info.filter;
          list.push(info);
        }
      });
    }
    if (list.length !== segmentList.length) {
      setSegmentList(list);
    }
  }, [selectList, props.value, segmentList]);

  // 父组件改变，设置当前值
  useEffect(() => {
    log.debug('props.value changed', JSON.stringify(props.value));
    // 仅当不相等是才刷新文档
    if (props.value !== propsValue) {
      setValue(ComplexModelUtil.fromJson(props.value));
    }
  }, [props.value, propsValue]);

  // 过滤组改变，修改过滤条数
  // 父组件改变，设置当前值
  useEffect(() => {
    setContext({
      dataProvider,
      logProvider: Log,
      mode: mode || 'edit',
      isUserGroup
    });
  }, [value, maxFilterCount, dataProvider, mode, isUserGroup]);

  /**
   * 添加过滤组
   */
  const addFilterGroup = i => {
    ComplexModelUtil.addFilterGroupWithOneFilter(value.filters[i]);
    const _value = ComplexModelUtil.getValidJson(value);
    setPropsValue(_value);
    onChange(_value);
    setValue({ ...value });
  };

  const addComplexGroup = () => {
    ComplexModelUtil.addComplexGroupWithOneFilter(value);
    const _value = ComplexModelUtil.getValidJson(value);
    setPropsValue(_value);
    onChange(_value);
    setValue({ ...value });
  };

  const deleteGroup = i => {
    value.filters.splice(i, 1);
    const _value = ComplexModelUtil.getValidJson(value);

    setPropsValue(_value);
    onChange(_value);
    setValue({ ...value });
  };

  /**
   * 当过滤组修改时回调
   * @param {object} v 过滤组
   */
  const onValueChange = (v, i) => {
    log.debug('onValueChanged', JSON.stringify(v));
    value.filters[i].filter = v;
    const _value = ComplexModelUtil.getValidJson(value);
    setPropsValue(_value);
    onChange(_value);
    setValue({ ...value });
  };

  const onChangeConnector = filter => {
    return v => {
      filter.connector = v;
      onChange(value);
    };
  };

  log.debug('Before Render', JSON.stringify(value), context.canAdd);

  const onChangedd = (id, i) => {
    const info = selectList.find(n => n.id === id);
    ComplexModelUtil.addComplexGroupWithOneFilter(value, i);
    value.filters[i].id = info.id;
    value.filters[i].name = info.name;
    value.filters[i].lastCalcTime = info.lastCalcTime;
    value.filters[i].customerCount = info.customerCount;
    const _value = ComplexModelUtil.getValidJson(value);
    setPropsValue(_value);
    onChange(_value);
    setValue({ ...value });
  };

  const getSubContent = val => {
    return _.map(val.filters, (item, index) => {
      const keys = _.keys(item).slice(1);
      // 整理顺序
      let _keys = [];
      _keys[0] = _.find(keys, key => key === 'eventGroup');
      _keys[1] = _.find(keys, key => key === 'userProperty');
      _keys[2] = _.find(keys, key => key === 'userLabel');
      _keys[3] = _.find(keys, key => key === 'segment');

      _keys = _.without(_keys, false, undefined, null);

      return (
        <ActionCollectiveGroup
          key={`${index}`}
          connector={item.connector}
          filterCount={keys.length}
          inner="inner"
          mode="detail"
        >
          <div>
            {_.map(_keys, key => {
              if (key === 'eventGroup') {
                return (
                  <EventFilter
                    key="eventGroup"
                    dataProvider={dataProvider}
                    showInitLine
                    value={item[key] || {}}
                    mode="detail"
                  />
                );
              } else if (key === 'userProperty') {
                return (
                  <Filter
                    addButtonText={t('cpnt-16ix1Bww1EeB')}
                    key="userProperty"
                    dataProvider={dataProvider}
                    value={item[key] || {}}
                    mode="detail"
                  />
                );
              } else if (key === 'userLabel') {
                return (
                  <Label
                    key="label"
                    dataProvider={dataProvider}
                    value={item[key] || {}}
                    mode="detail"
                  />
                );
              } else if (key === 'segment') {
                return (
                  <Segment
                    key="segment"
                    dataProvider={dataProvider}
                    value={item[key] || {}}
                    mode="detail"
                  />
                );
              }
            })}
          </div>
        </ActionCollectiveGroup>
      );
    });
  };

  const count = () => {
    return (value && value.filters && value.filters.length) || 1;
  };

  const ruleRender = data => {
    if (data.type === 'CONDITIONAL') {
      return (
        <div className="customizeStyle">
          <Customize
            mode="detail"
            value={data || {}}
            dataProvider={dataProvider}
          />
        </div>
      );
    } else if (data.type === 'COMPLEX') {
      return (
        <div>
          <Title level={4}>{t('cpnt-0LFG2WaoPbKU')}</Title>
          {/* <Complex
            dataProvider={dataProvider}
            value={data.includeSegments || {}}
            selectList={segmentList}
            mode="detail"
          /> */}
          <div className="wolf-static-component_filter_FilterGroupPanel">
            <ComplexGroup
              connector={
                ComplexModelUtil.fromJson(data.includeSegments).connector
              }
              onChangeConnector={onChangeConnector(value)}
              filterCount={
                ComplexModelUtil.fromJson(data.includeSegments).filters.length
              }
              inner="inner"
              mode="detail"
            >
              {ComplexModelUtil.fromJson(data.includeSegments).filters.map(
                (v, i) => {
                  let filterCount = 0;
                  if (v.filter && v.filter.filters) {
                    v.filter.filters.forEach(w => {
                      if (w.filters && w.filters.length > 0) {
                        filterCount += w.filters.length;
                      }
                    });
                  }
                  return (
                    <div style={{ marginBottom: 20 }} key={i}>
                      <FilterContext.Provider
                        value={{
                          ...context,
                          canAdd: false,
                          filterCount,
                          mode: 'detail'
                        }}
                      >
                        <div style={{ marginLeft: '0px !important' }}>
                          <Select
                            showSearch
                            filterOption={filterOption}
                            disabled
                            value={v.id}
                            key={v.id}
                            onChange={id => onChangedd(id, i)}
                            optionLabelProp="label"
                            style={{ width: 280 }}
                            placeholder={t('cpnt-hiXFxpvq9XlM')}
                          >
                            {segmentList.map(n => (
                              <Option
                                key={`${n.id}`}
                                value={n.id}
                                label={n.name}
                              >
                                <Popover
                                  content={() => renderUserGroupInfo(n)}
                                  placement="right"
                                  key={n.name}
                                  overlayClassName="conplexInfoItemPopover"
                                  trigger="click"
                                >
                                  <div className="w-[280px]">{n.name}</div>
                                </Popover>
                              </Option>
                            ))}
                          </Select>
                        </div>

                        {v.id && (
                          <div style={{ marginLeft: 20 }}>
                            <FilterListGroup
                              value={v.filter}
                              onChange={val => onValueChange(val, i)}
                            />
                          </div>
                        )}
                      </FilterContext.Provider>
                    </div>
                  );
                }
              )}
            </ComplexGroup>
          </div>

          <div
            hidden={
              ComplexModelUtil.fromJson(data.excludeSegments).filters.length === 0
            }
          >
            <Divider />
            <Title level={4}>{t('cpnt-W6pb2bcSf7Wm')}</Title>
            {/* <Complex
              dataProvider={dataProvider}
              value={data.excludeSegments || {}}
              selectList={segmentList}
              mode="detail"
            /> */}
            <div className="wolf-static-component_filter_FilterGroupPanel">
              <ComplexGroup
                connector={
                  ComplexModelUtil.fromJson(data.excludeSegments).connector
                }
                onChangeConnector={onChangeConnector(value)}
                filterCount={
                  ComplexModelUtil.fromJson(data.excludeSegments).filters.length
                }
                inner="inner"
                mode="detail"
              >
                {ComplexModelUtil.fromJson(data.excludeSegments).filters.map(
                  (v, i) => {
                    let filterCount = 0;
                    if (v.filter && v.filter.filters) {
                      v.filter.filters.forEach(w => {
                        if (w.filters && w.filters.length > 0) {
                          filterCount += w.filters.length;
                        }
                      });
                    }
                    return (
                      <div style={{ marginBottom: 20 }} key={i}>
                        <FilterContext.Provider
                          value={{
                            ...context,
                            canAdd: false,
                            filterCount,
                            mode: 'detail',
                            validating: false
                          }}
                        >
                          <div style={{ marginLeft: '0px !important' }}>
                            <Select
                              showSearch
                              // optionFilterProp="children"
                              filterOption={filterOption}
                              // className={
                              //   context.validating && !v.id ? 'has-error' : ''
                              // }
                              disabled
                              value={v.id}
                              key={v.id}
                              onChange={id => onChangedd(id, i)}
                              optionLabelProp="label"
                              style={{ width: 280 }}
                              placeholder={t('cpnt-hiXFxpvq9XlM')}
                            >
                              {segmentList.map(n => (
                                <Option
                                  key={`${n.id}`}
                                  value={n.id}
                                  label={n.name}
                                >
                                  <Popover
                                    content={() => renderUserGroupInfo(n)}
                                    placement="right"
                                    key={n.name}
                                    overlayClassName="conplexInfoItemPopover"
                                    trigger="click"
                                  >
                                    <div className="w-[280px]">{n.name}</div>
                                  </Popover>
                                </Option>
                              ))}
                            </Select>
                          </div>
                          {v.id && (
                            <div style={{ marginLeft: 20 }}>
                              <FilterListGroup
                                value={v?.filter}
                                onChange={val => onValueChange(val, i)}
                              />
                            </div>
                          )}
                        </FilterContext.Provider>
                      </div>
                    );
                  }
                )}
              </ComplexGroup>
            </div>
          </div>
        </div>
      );
    } else if (data.type === 'CAMPAIGN') {
      return <CampaignDetail info={data} />;
    } else if (data.type === 'UPLOAD') {
      return (
        <Empty
          description={
            <span style={{ fontSize: 14, color: 'var(--ant-primary-color)' }}>
              {t('cpnt-RWtgm4wvYqcm')}
            </span>
          }
        />
      );
    } else if (data.type === 'CONDITION_AGGREGATE') {
      return (
        <div>
          <Title level={4}>{t('cpnt-0LFG2WaoPbKU')}</Title>
          {/* <ActionCollective
            dataProvider={dataProvider}
            value={data.includeConditionAggregate || {}}
            mode="detail"
          /> */}
          <div className="wolf-static-component_filter_FilterGroupPanel_action_collective">
            <ActionCollectiveGroup
              connector={data.includeConditionAggregate.connector}
              onChangeConnector={res => onChangeConnector(res, 'connector')}
              filterCount={count()}
              inner="inner"
              mode={mode}
            >
              {getSubContent(data.includeConditionAggregate)}
            </ActionCollectiveGroup>
          </div>
          <div hidden={data.excludeConditionAggregate.filters.length === 0}>
            <Divider />
            <Title level={4}>{t('cpnt-W6pb2bcSf7Wm')}</Title>
            {/* <ActionCollective
              dataProvider={dataProvider}
              value={data.excludeConditionAggregate || {}}
              mode="detail"
            /> */}
            <div className="wolf-static-component_filter_FilterGroupPanel_action_collective">
              <ActionCollectiveGroup
                connector={data.excludeConditionAggregate.connector}
                onChangeConnector={res => onChangeConnector(res, 'connector')}
                filterCount={count()}
                inner="inner"
                mode={mode}
              >
                {getSubContent(data.excludeConditionAggregate)}
              </ActionCollectiveGroup>
            </div>
          </div>
        </div>
      );
    } else if (data.type === 'SHORT_LINK') {
      return (
        <div className="short-link-rules">
          {_.map(data?.shortLinkInfo?.filters, item => {
            if (item.function === 'COUNT') {
              return (
                <div
                  key={`${item.name}${item.function}`}
                  className="short-link-rule"
                >{`${t('cpnt-TanygiVppr9T')}${operatorMap[item.operator]} ${
                  item.value
                }`}</div>
              );
            }
            return (
              <div
                key={`${item.name}${item.function}`}
                className="short-link-rule"
              >{`${item.name}: ${operatorMap[item.operator]} ${
                item.value
              }`}</div>
            );
          })}
        </div>
      );
    }
  };

  const renderUserGroupInfo = data => {
    const rule = {
      ONCE: t('cpnt-ESlpQfGSl2XL'),
      SCHEDULE: t('cpnt-1XcZOBm3SoIq'),
      DAY: t('cpnt-1PVPMGPICkbu'),
      WEEK: t('cpnt-4GamYBBdVox0'),
      MONTH: t('cpnt-WTxfKJ9jiASZ')
    };

    const {
      name,
      calcStatus,
      scheduleConf,
      customerCount,
      lastCalcTime,
      calcRule
    } = data;
    // const { calcRule, schedule } = scheduleConf;

    return (
      <div className="renderUserGroupInfo">
        <div className="title">{name}</div>
        <div className="content">
          <div>
            {t('cpnt-XbupIJXfRp7i')}
            {calcStatus && _.filter(calcStatusList, v => v.value === calcStatus)[0].name}
          </div>
          <div>{t('cpnt-vND4Kdf0wbXF')}{rule[calcRule]}</div>
          {calcRule === 'SCHEDULE' && (
            <div>
              {t('cpnt-Vjtz4ZdPgXwW')}
              {scheduleConf
                ? scheduleConf.schedule
                  ? rule[scheduleConf.schedule.scheduleRate]
                  : '-'
                : '-'}
            </div>
          )}
        </div>
        {data.type !== 'CAMPAIGN' && (
          <div>
            <div className="count">{customerCount}{t('cpnt-i09yvc01OXK7')}</div>
            <div className="lastCalcTime">
              {dayjs(lastCalcTime).format('YYYY-MM-DD HH:mm:ss')}
            </div>
          </div>
        )}
        {ruleRender(data)}
      </div>
    );
  };

  const filterOption = (inputValue, option) => {
    if (option.props.children.key.indexOf(inputValue) >= 0) return true;
  };

  return (
    // </FilterContext.Provider>
    <div className="wolf-static-component_filter_FilterGroupPanel">
      <ComplexGroup
        connector={value.connector}
        onChangeConnector={onChangeConnector(value)}
        filterCount={value.filters.length}
        inner="inner"
        mode={context.mode}
      >
        {value.filters.map((v, i) => {
          let filterCount = 0;
          if (v.filter && v.filter.filters) {
            v.filter.filters.forEach(w => {
              if (w.filters && w.filters.length > 0) {
                filterCount += w.filters.length;
              }
            });
          }
          return (
            <div style={{ marginBottom: 20 }} key={i}>
              <FilterContext.Provider
                value={{
                  ...context,
                  canAdd: filterCount < maxFilterCount,
                  filterCount
                }}
              >
                <div>
                  <Select
                    showSearch
                    // optionFilterProp="children"
                    filterOption={filterOption}
                    className={context.validating && !v.id ? 'has-error' : ''}
                    disabled={context.mode === 'detail'}
                    value={v.id}
                    key={v.id}
                    onChange={id => onChangedd(id, i)}
                    optionLabelProp="label"
                    style={{ width: 280 }}
                    placeholder={t('cpnt-hiXFxpvq9XlM')}
                  >
                    {segmentList.map(n => (
                      <Option key={`${n.id}`} value={n.id} label={n.name}>
                        {/* {n.name} */}
                        <Popover
                          content={() => renderUserGroupInfo(n)}
                          placement="right"
                          key={n.name}
                          overlayClassName="conplexInfoItemPopover"
                          trigger="hover"
                        >
                          <div className="w-[280px]">{n.name}</div>
                        </Popover>
                      </Option>
                    ))}
                  </Select>
                  {context.mode !== 'detail' && (
                    <CloseCircleFilled
                      onClick={() => deleteGroup(i)}
                      style={{
                        color: '#ccc',
                        fontSize: 20,
                        marginLeft: 20,
                        cursor: 'pointer'
                      }}
                    />
                  )}
                </div>

                {v.id && (
                  <div style={{ marginLeft: 20 }}>
                    <FilterListGroup
                      value={v.filter}
                      onChange={val => onValueChange(val, i)}
                    />
                    <div style={{ marginTop: 10 }} className="FilterAdder">
                      <Button
                        type="dashed"
                        icon={<PlusOutlined />}
                        onClick={() => addFilterGroup(i)}
                        disabled={filterCount >= maxFilterCount}
                        hidden={context.mode === 'detail'}
                      >
                        {t('cpnt-UmanJ2rFIJkJ')}
                      </Button>
                      <span
                        style={{ marginLeft: 10 }}
                        hidden={context.mode === 'detail'}
                      >
                        [{filterCount}/{maxFilterCount}] {t('cpnt-A1A3fYFSEZF1', { maxFilterCount })}
                      </span>
                    </div>
                  </div>
                )}
              </FilterContext.Provider>
            </div>
          );
        })}
      </ComplexGroup>
      <div>
        <Button
          type="dashed"
          icon={<PlusOutlined />}
          onClick={addComplexGroup}
          disabled={value.filters.length >= 5}
          hidden={context.mode === 'detail'}
        >
          {t('cpnt-Ru3icPeFC8vp')} [{value.filters.length}/5]
        </Button>
      </div>
    </div>
  );
});

export default Complex;
