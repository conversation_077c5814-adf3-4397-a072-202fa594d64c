import React, { useContext } from 'react';
import FilterGroup from './FilterGroup';
import FilterSingle from './FilterSingle';
import FilterContext from './FilterContext';
import FilterModelUtil from './FilterModelUtil';

function FilterListGroup({ value, onChange }) {
  const { logProvider, mode } = useContext(FilterContext);
  const log = logProvider.getLogger('FilterListGroup');

  const onChangeFilter = (filterList, index) => {
    return filter => {
      filterList[index] = filter;
      onChange(value);
    };
  };

  const onAddFilter = (filterList, index) => {
    return () => {
      filterList.filters = [
        ...filterList.filters.slice(0, index + 1),
        FilterModelUtil.createFilter(),
        ...filterList.filters.slice(index + 1)
      ];
      onChange(value);
    };
  };

  const onDeleteFilter = (filterList, index) => {
    const _value = value;
    return flag => {
      if (flag && _value.filters.length === 0) {
        filterList.filters.splice(index, 1);
        FilterModelUtil.deleteEmptyFilterList(value);
        onChange({});
      } else {
        filterList.filters.splice(index, 1);
        FilterModelUtil.deleteEmptyFilterList(value);
        onChange(value);
      }
    };
  };

  const getFilterSingle = (filterList, filter, index) => {
    return (
      <FilterSingle
        key={index}
        value={filter}
        onChange={onChangeFilter(filterList, index)}
        onAdd={onAddFilter(filterList, index)}
        onDelete={onDeleteFilter(filterList, index)}
      />
    );
  };

  /**
   * 获得FilterGroup组件
   * @param {array} filterList
   * @param {integer} index
   */
  const getFilterGroup = (filterList, index) => {
    if (!filterList || !filterList.filters) return '';
    const filterCount = mode === 'detail' ? FilterModelUtil.getValidFilterListCount(filterList) : filterList.filters.length;
    if (filterCount === 0) return '';

    return (
      <FilterGroup
        key={index}
        connector={filterList.connector}
        onChangeConnector={onChangeConnector(filterList)}
        filterCount={filterCount}
        inner="inner"
      >
        {filterList.filters.map((v, i) => getFilterSingle(filterList, v, i))}
      </FilterGroup>
    );
  };

  const onChangeConnector = filter => {
    return v => {
      filter.connector = v;
      onChange(value);
    };
  };

  // 没有值就退出
  if (!value || !value.filters) {
    return '';
  }

  log.debug('connector', value.connector);
  log.debug('filters', value.filters);

  // const filterListCount = mode === 'detail' ? FilterModelUtil.getValidFilterListGroupCount(value) : value.filters.length;
  const filterListCount = value.filters.length;
  if (filterListCount === 0) return '';

  return (
    <div className="FilterGroupListPannel">
      <FilterGroup
        connector={value.connector}
        onChangeConnector={onChangeConnector(value)}
        filterCount={filterListCount}
        inner=""
      >
        {value.filters.map(getFilterGroup)}
      </FilterGroup>
    </div>
  );
}

export default FilterListGroup;
