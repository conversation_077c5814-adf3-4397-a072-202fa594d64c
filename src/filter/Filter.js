// https://stackoverflow.com/questions/37949981/call-child-method-from-parent
import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Button } from 'antd';
import _ from 'lodash';
import { PlusOutlined } from '@ant-design/icons';
import FilterListGroup from './FilterListGroup';
import FilterContext from './FilterContext';
import FilterModelUtil from './FilterModelUtil';
import FilterConfig from './FilterConfig';
import Log from '../utils/log';
import { t } from '../utils/translation';
import './filter.scss';

const log = Log.getLogger('Filter');
/**
 * 过滤器组件
 * mode有两种形式，edit|detail，edit模式下可编辑，默认是edit
 * @param {object} 过滤值 value
 * @param {object} 数据提供器 {getPropertyList: (name) => data}
 * @param {function} 响应改变 onChange
 */
const Filter = forwardRef((props, ref) => {
  // The component instance will be extended
  // with whatever you return from the callback passed
  // as the second argument
  useImperativeHandle(ref, () => ({
    isValid(flag) {
      setContext({ ...context, validating: true });
      return FilterModelUtil.isFilterListGroupValid(value, flag);
    },
    addFilterGroup() {
      return addFilterGroup();
    },
    getValue() {
      return value;
    },
    getFilterCount() {
      if (!_.isEmpty(value.filters)) {
        return value.filters
          .map(v => v.filters?.length)
          .reduce((a, b) => a + b, 0);
      }
      return 0;
    }
  }));

  const { dataProvider, onChange, mode, hideAdd = false, hideInit = false, className, addButtonText = t('cpnt-UmanJ2rFIJkJ'), isUserGroup } = props;
  const { maxFilterCount } = FilterConfig;
  // const [value, setValue] = useState(FilterModelUtil.initCreateFilterGroup());
  const [value, setValue] = useState(() => {
    // if (hideInit) {
    //   return {};
    // }
    return props.value && props.value.filters && props.value.filters.length > 0 ? FilterModelUtil.fromJson(props.value) : FilterModelUtil.initCreateFilterGroup(hideInit);
  });
  // 用于保存父子组件的value状态，比对这个值和props.value可以判断需不需要刷新
  const [propsValue, setPropsValue] = useState({});

  const [context, setContext] = useState({
    dataProvider,
    logProvider: Log,
    canAdd: true,
    mode: mode || 'edit',
    filterCount: 0,
    validating: false,
    hideAdd,
    hideInit,
    isUserGroup
  });

  // 父组件改变，设置当前值
  useEffect(() => {
    log.debug('props.value changed', JSON.stringify(props.value));
    // 仅当不相等是才刷新文档
    if ((!_.isEmpty(props.value) || !_.isEmpty(propsValue)) && !_.isEqual(props.value, propsValue)) {
      setValue(FilterModelUtil.fromJson(props.value));
    }
  }, [props.value, propsValue]);

  // 过滤组改变，修改过滤条数
  // 父组件改变，设置当前值
  useEffect(() => {
    // const filterCount = value.filters
    //   .map(v => v.filters?.length)
    //   .reduce((a, b) => a + b, 0);
    let filterCount = 0;
    if (value && value.filters) {
      filterCount = value.filters
        .map(v => v.filters?.length)
        .reduce((a, b) => a + b, 0);
    }
    setContext({
      dataProvider,
      logProvider: Log,
      canAdd: filterCount < maxFilterCount,
      mode: mode || 'edit',
      filterCount,
      hideAdd,
      hideInit,
      isUserGroup
    });
  }, [value, maxFilterCount, dataProvider, mode, hideAdd, hideInit, isUserGroup]);

  /**
   * 添加过滤组
   */
  const addFilterGroup = () => {
    const _value = _.isEmpty(value) ? {
      connector: 'AND',
      filters: []
    } : value;

    FilterModelUtil.addFilterGroupWithOneFilter(_value);
    onValueChange(_value);
    return _value;
  };

  /**
   * 当过滤组修改时回调
   * @param {object} v 过滤组
   */
  const onValueChange = v => {
    log.debug('onValueChanged', JSON.stringify(v));
    // if (FilterModelUtil.isFilterListGroupValid(v)) {
    //   log.debug('Filter is valid, call Father\'s onChange');
    //   onChange(FilterModelUtil.toJson(v));
    // }
    const _v = FilterModelUtil.getValidJson(v);
    setPropsValue(_v);
    onChange(_v, v);
    setValue({ ...v });
  };

  log.debug('Before Render', JSON.stringify(value), context.canAdd);

  return (
    <FilterContext.Provider value={context}>
      <div className={`wolf-static-component_filter_FilterGroupPanel ${className || ''}`} style={{ display: hideInit && hideAdd && _.isEmpty(value || value.filters) ? 'none' : 'block' }}>
        <FilterListGroup value={value} onChange={onValueChange} />
        {
          !hideAdd && <div className="FilterAdder">
            <Button
              type="dashed"
              icon={<PlusOutlined />}
              onClick={addFilterGroup}
              disabled={!context.canAdd}
              hidden={context.mode === 'detail'}
            >
              {addButtonText}
            </Button>
            <span style={{ marginLeft: 10 }} hidden={context.mode === 'detail'}>
              [{context.filterCount}/{maxFilterCount}] {t('cpnt-A1A3fYFSEZF1', { maxFilterCount })}
            </span>
          </div>
        }
      </div>
    </FilterContext.Provider>
  );
});

export default Filter;
