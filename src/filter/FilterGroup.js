import React, { useContext } from 'react';
import FilterConnector from './FilterConnector';
import FilterContext from './FilterContext';

function FilterGroup({ connector, onChangeConnector, filterCount, children, inner }) {
  const { logProvider } = useContext(FilterContext);
  const log = logProvider.getLogger('FilterGroup');

  log.debug('connector', connector);

  return (
    <div className="FilterGroupPanel">
      <div className="ConnectorPanel" hidden={filterCount <= 1}>
        <div className="TopLine" />
        <div className="VLine" />
        <div className="BottomLine" />
        <div className="Connector">
          <FilterConnector value={connector} onChange={onChangeConnector} />
        </div>
      </div>
      <ul className={`FilterList ${inner}`}>{children}</ul>
    </div>
  );
}

export default FilterGroup;
