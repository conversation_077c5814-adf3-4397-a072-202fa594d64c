import './locales/config';
import Customize from './customize/customize.js';
import Filter from './filter/Filter.js';
import FlowEditor from './flow/FlowEditor.js';
import FlowCanvas from './flow/FlowCanvas.js';
import FlowNodeBox from './flow/FlowNodeBox.js';
import FlowNode from './flow/FlowNode.js';
import Complex from './filter/Complex.js';
import Label from './label/Filter.js';
import SelectTime from './selectTime/index';
import GrabbingBox from './grabbing';

import EventFilter from './event/Filter.js';
import ActionCollective from './actioncollective/actioncollective.js';
// 给用户360用的
import NormCollective from './newactioncollective/actioncollective.js';
import Segment from './segment/Filter.js';
import ShortcutTime from './shortcutTime/shortcutTime.jsx';
import SelectTimeV2 from './selectTimeV2/index';
import { setTheme } from './utils/theme.js';
import { cpntConvertToCSV, cpntGetNewLang } from './locales/utils/helper';

import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import dayjs from 'dayjs';

import 'animate.css/animate.min.css';
import './index.scss';
import './iconfont/iconfont.js';

dayjs.extend(localeData);
dayjs.extend(weekday);
export {
  Customize,
  Filter,
  FlowEditor,
  FlowCanvas,
  FlowNodeBox,
  FlowNode,
  Complex,
  Label,
  SelectTime,
  EventFilter,
  ActionCollective,
  Segment,
  NormCollective,
  SelectTimeV2,
  ShortcutTime,
  setTheme,
  GrabbingBox,
  cpntConvertToCSV,
  cpntGetNewLang
};
