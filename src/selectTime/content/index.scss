// @import '~antd/dist/antd.css';
.contentStyle{
    // width: 450px;
    .absoluteDateTime{
        display: flex;
        .timeStyle{
            flex: 1;
            // width: 120px;
            display: flex;
            .eachTime{
                height: 320px;
                overflow-y: auto;
                width: 50px;
                padding-bottom: 289px;
                .oneTime{
                    text-align: center;
                    padding: 5px;
                    // background-color: #f5f5f5;
                    &:hover{
                        // #1890ff
                        background-color: #f5f5f5;
                        
                    }
                }
                &::-webkit-scrollbar{
                    display: none;
                }
            }
            
        }
    }
    .ant-tabs-nav{
        margin-bottom: 5px!important;
    }
    .relativeDateTime{
        // height: 320px;
        display: flex;
        padding: 20px 0px;
        justify-content: center;
    }
    .nowDateTime{
        // height: 320px;
    }
    .save{
        display: flex;
        justify-content: flex-end;
        padding-top: 10px;
    }
}