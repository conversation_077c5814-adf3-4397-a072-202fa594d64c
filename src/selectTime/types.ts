export interface BaseSelectTimeConfig {
  type: 'RELATIVE' | 'NOW' | 'ABSOLUTE';
  times: number;
  timeTerm: 'DAY' | 'WEEK' | 'MONTH';
  isPast: boolean;
  truncateAsDay?: boolean;
  isActionCollection?: boolean;
}

export interface RelativeOrNowSelectTimeConfig extends BaseSelectTimeConfig {
  type: 'RELATIVE' | 'NOW';
}

export interface AbsoluteSelectTimeConfig extends BaseSelectTimeConfig {
  type: 'ABSOLUTE';
  timestamp: number;
}

export type SelectTimeConfig =
  | RelativeOrNowSelectTimeConfig
  | AbsoluteSelectTimeConfig;
export type SelectTimeConfigPair = [] | [SelectTimeConfig, SelectTimeConfig];

export interface Props {
  /**
   * @description 初始数据，包含时间信息
   * @type any
   */
  data: SelectTimeConfigPair;

  /**
   * @description 时间变化时的回调函数，接收两个参数：timeValue 和 flag。flag 为 true 代表校验不通过。
   * @param value 时间值
   * @param flag 校验标志
   */
  onChange?: (value: any, flag: boolean) => void;

  /**
   * @description 时间选择器的类型
   * @type string
   */
  type?: string;

  /**
   * @description 是否显示时间
   * @type boolean
   */
  showTime?: boolean;

  /**
   * @description 自定义样式
   * @type object
   */
  style?: any;

  /**
   * @description 是否为分析用时间组件
   * @type boolean
   */
  isAnalysis?: boolean;

  /**
   * @description 是否为分析用时间组件
   * @type boolean
   */
  isActionCollection?: boolean;
}
