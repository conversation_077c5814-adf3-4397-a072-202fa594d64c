import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { Input, Popover } from 'antd';
import Content from './content/index';
import ShortcutTime from '../shortcutTime/shortcutTime';
import { getString, getTime } from './config';
import { DataContext } from './context';
import { Props } from './types';
import './index.scss';
import { t } from '../utils/translation';

/**
 * @description: onChange接收两个参数timeValue 和 flag , flag为true代表校验不通过
 * @param {boolean} isAnalysis 是否是分析用时间组件
 * @param {boolean} isActionCollection 是否是在行为聚合分群
 *
 */
const SelectTime = forwardRef<HTMLButtonElement, Props>(
  ({ data, onChange, type, showTime, style, isAnalysis, isActionCollection }, ref) => {
    const [startVisible, setStartVisible] = useState(false);
    const [endVisible, setEndVisible] = useState(false);
    const [value, setValue] = useState<any>([]);
    const [desc, setDesc] = useState<any>([]);
    const [clearName, setClearName] = useState(0);
    const [errorFlag, setErrorFlag] = useState(false);

    // @ts-ignore
    useImperativeHandle(ref, () => ({
      check
    }));
    const check = () => {
      if (errorFlag) {
        return false;
      } else {
        return true;
      }
    };

    useEffect(() => {
      setValue(data ? [...data] : []);
      if (data && data[0] && data[1]) {
        const data0 = getTime(data[0]);
        const data1 = getTime(data[1]);
        setErrorFlag(data0 - data1 > 0);
      } else {
        setErrorFlag(false);
      }
    }, [data]);

    useEffect(() => {
      const str1 = getString(value[0], showTime, isAnalysis);
      const str2 = getString(value[1], showTime, isAnalysis);
      setDesc([str1, str2]);
    }, [value, showTime]);

    const onSave = (data1: any, from: string) => {
      const info = { ...data1 };
      if (from === 'start') {
        if (info.type === 'ABSOLUTE') {
          if (showTime) {
            info.timestamp = info.timestamp.valueOf();
          } else {
            info.timestamp = info.timestamp.startOf('day').valueOf();
          }
        }
        value[0] = info;
        setStartVisible(false);
        setEndVisible(true);
      } else if (from === 'end') {
        if (info.type === 'ABSOLUTE') {
          if (showTime) {
            info.timestamp = info.timestamp.valueOf();
          } else {
            info.timestamp = info.timestamp.endOf('day').valueOf();
          }
        }
        value[1] = info;
        setEndVisible(false);
      }
      if (isActionCollection) {
        value[0].truncateAsDay = true;
        value[1].isEndTime = true;
      }
      setValue([...value]);
      if (value[0] && value[1]) {
        const time1 = getTime(value[0]);
        const time2 = getTime(value[1]);
        const flag = time1 - time2 > 0;
        // todo 临时处理 设置时间错误的时候报红
        setErrorFlag(flag);
        onChange && onChange(value, flag);
      }
    };

    return (
      <div style={{ maxWidth: isAnalysis ? 800 : 360, ...style }} className="site-input-group-wrapper1">
        <DataContext.Provider value={{ type, showTime, isAnalysis, clearName, setClearName }}>
          <div style={{ display: 'flex' }}>
            {isAnalysis && (
              <ShortcutTime
                style={{ width: '170px' }}
                clearName={clearName}
                onChange={onChange}
                setTime={setValue}
                isAnalysis
              />
            )}
            <Input.Group compact>
              <Popover
                trigger="click"
                arrowPointAtCenter
                open={startVisible}
                onOpenChange={setStartVisible}
                placement="bottom"
                content={<Content data={value[0]} save={(date: any) => onSave(date, 'start')} />}
              >
                <Input
                  style={{
                    width: isAnalysis ? '30%' : '45%',
                    textAlign: 'center',
                    borderColor: errorFlag ? 'red' : ''
                  }}
                  value={desc[0]}
                  placeholder={t('cpnt-tWTS2J0uph9a')}
                />
              </Popover>
              <Input
                className="site-input-split"
                style={{
                  width: '10%',
                  borderLeft: 0,
                  borderRight: 0,
                  pointerEvents: 'none',
                  backgroundColor: '#fff',
                  textAlign: 'center',
                  borderColor: errorFlag ? 'red' : '',
                  padding: 4
                }}
                placeholder="~"
                disabled
              />
              <Popover
                trigger="click"
                open={endVisible}
                onOpenChange={setEndVisible}
                placement="bottom"
                content={<Content data={value[1]} save={(date: any) => onSave(date, 'end')} />}
              >
                <Input
                  className="site-input-right"
                  value={desc[1]}
                  style={{
                    width: isAnalysis ? '30%' : '45%',
                    textAlign: 'center',
                    borderColor: errorFlag ? 'red' : ''
                  }}
                  placeholder={t('cpnt-lXhIZ1x3h7Ec')}
                />
              </Popover>
            </Input.Group>
          </div>
        </DataContext.Provider>
      </div>
    );
  }
);
export default SelectTime;
