import dayjs from 'dayjs';
import { t } from '../utils/translation';

const timeTerm = [
  // {
  //   value: 'MINUTE',
  //   label: '分钟',
  //   unit: 'minutes'
  // },
  // {
  //   value: 'HOUR',
  //   label: '小时',
  //   unit: 'hours'
  // },
  {
    value: 'DAY',
    label: t('cpnt-Aiimylb7AS1W'),
    unit: 'days'
  },
  {
    value: 'WEEK',
    label: t('cpnt-xrHQvAEPkZaq'),
    unit: 'weeks'
  },
  {
    value: 'MONTH',
    label: t('cpnt-8kQT9PSM1TO2'),
    unit: 'months'
  }
];

const scorllUnit = 32;

const isPassObj = {
  true: t('cpnt-X5WLVuso1UkW'),
  false: t('cpnt-JOXq05oK6OLh')
};

const isAnalysisObj = {
  true: t('cpnt-Ez3b4iQ4X9Zh'),
  false: t('cpnt-BnNiHAAzlmle')
};

const getString = (obj, showTime, isAnalysis) => {
  let str = '';
  if (typeof obj === 'object') {
    if (obj.type === 'ABSOLUTE') {
      const format = showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';
      str = `${dayjs(obj.timestamp).format(format)}`;
    } else if (obj.type === 'RELATIVE') {
      !isAnalysis
        ? str = `${obj.times}${timeTerm.find(n => n.value === obj.timeTerm)?.label}${isPassObj[JSON.stringify(obj.isPast)]}`
        : str = `${isAnalysisObj[JSON.stringify(obj.isPast)]}${obj.times}${timeTerm.find(n => n.value === obj.timeTerm)?.label}`;
    } else if (obj.type === 'NOW') {
      str = t('cpnt-gJxdupdxv55v');
    }
  }
  return str;
};

const getTime = obj => {
  let time = dayjs();
  if (obj.type === 'ABSOLUTE') {
    time = dayjs(obj.timestamp);
  } else if (obj.type === 'RELATIVE') {
    const info = timeTerm.find(n => n.value === obj.timeTerm);
    if (obj.isPast) {
      time = dayjs().subtract(obj.times, info.unit);
    } else {
      time = dayjs().add(obj.times, info.unit);
    }
  }
  return time.valueOf();
};

export { timeTerm, scorllUnit, isPassObj, getString, getTime, isAnalysisObj };
