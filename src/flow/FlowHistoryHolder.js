import _ from 'lodash';
import Log from '../utils/log';

const log = Log.getLogger('FlowHistoryHolder');

export default class FlowHistoryHolder {
  #history = [];

  #head = null;

  #current = null;

  #maxHistory = 20;

  constructor() {
    log.debug('constructor');
    this.currentIndex = -1;
  }

  push(obj) {
    if (this.#head === obj || this.#current === obj) return;
    this.#head = obj;
    this.#history.length = (this.currentIndex + 1) > this.#history.length ? this.#history.length : (this.currentIndex + 1);
    this.#history.push(_.cloneDeep(obj));

    if (this.#history.length > this.#maxHistory) {
      this.#history.splice(0, this.#history.length - this.#maxHistory);
    }
    this.currentIndex = this.#history.length - 1;
    log.debug('push history', this.currentIndex, JSON.stringify(obj));
  }

  redu() {
    if (!this.canRedu()) return;
    this.currentIndex += 1;
    this.#current = _.cloneDeep(this.#history[this.currentIndex]);
    log.debug(`redu history >> size=${this.#history.length} index=${this.currentIndex} obj=${JSON.stringify(this.#current)}`);
    return this.#current;
  }

  undu() {
    if (!this.canUndu()) return;
    this.currentIndex -= 1;
    this.#current = _.cloneDeep(this.#history[this.currentIndex]);
    log.debug(`undu history << size=${this.#history.length} index=${this.currentIndex} obj=${JSON.stringify(this.#current)}`);
    return this.#current;
  }

  canRedu() {
    const newIndex = this.currentIndex + 1;
    if (this.#history.length > newIndex) return true;
    return false;
  }

  canUndu() {
    const newIndex = this.currentIndex - 1;
    if (this.#history.length > newIndex && newIndex >= 0) return true;
    return false;
  }
}
