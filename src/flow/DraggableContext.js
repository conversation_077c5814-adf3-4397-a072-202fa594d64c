import React from 'react';
import Log from '../utils/log';

const log = Log.getLogger('DragDropReducer');

const DragDropReducer = (state, action) => {
  const context = DraggableContextFactory.build().context;
  const { dragging, dropping, droppable } = action;
  switch (action.type) {
    case 'dragStart':
      log.debug('dragStart');
      return {
        ...context,
        dragging
      };
    case 'dragEnter':
      if (state.droppable === droppable && state.dropping === dropping) return state;
      log.debug('dragEnter', droppable, dropping);
      return {
        ...state,
        dropping,
        droppable
      };
    case 'dragLeave':
      if (state.dropping === null && state.droppable === null) return state;
      log.debug('dragLeave');
      return {
        ...state,
        dropping: null,
        droppable: null
      };
    case 'dragEnd':
      log.debug('dragEnd');
      return context;
    default:
      break;
  }
  return state;
};

const DraggableContextFactory = {
  build: () => {
    return {
      context: {
        dragging: null,
        dropping: null,
        droppable: null
      }
    };
  }
};

const DraggableContext = React.createContext(DraggableContextFactory.build());

export { DraggableContext, DraggableContextFactory, DragDropReducer };
