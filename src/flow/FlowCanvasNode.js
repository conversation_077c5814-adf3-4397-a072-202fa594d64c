import React, { useState, useEffect, useContext } from 'react';
import _ from 'lodash';
import { DraggableContext } from './DraggableContext';
import FlowNode from './FlowNode';
import Draggable from './Draggable';

export default function FlowCanvasNode({ model, mode, onClickNode, onUpdateNode }) {
  const [inCounts, setInCounts] = useState(model.inCounts);
  const [hoverCss, setHoverCess] = useState({
    node: '',
    inCounts: ''
  });
  const { context: { dragging } } = useContext(DraggableContext);
  const [droppable, setDroppable] = useState(null);

  useEffect(() => {
    if (!dragging) {
      setDroppable(null);
    } else if (model.canDrop(dragging) && droppable !== true) {
      setDroppable(true);
    } else if (!model.canDrop(dragging) && droppable === true) {
      setDroppable(false);
    }
  }, [dragging, model, droppable]);

  useEffect(() => {
    if (mode !== 'detail') return;
    setInCounts(model.inCounts || 0);
    if (model.inCounts !== 0) {
      setHoverCess({
        node: 'animated',
        inCounts: 'animated slideInUp'
      });
      setTimeout(() => {
        setHoverCess({
          node: '',
          inCounts: ''
        });
      }, 1000);
    }
  }, [model.inCounts, mode]);

  const getNodeStyle = () => {
    let style = '';
    if (model.isEditable()) {
      style += ' editable ';
      if (!model.detail || _.isEmpty(model.detail) || model.detail.hasData !== true) {
        style += ' noData ';
      }
      if (model.detail && !model.detail?.isValid && model.detail?.isDirty) {
        style += ' invalid ';
      }
    }
    return style;
  };

  const style = getNodeStyle();

  return (
    <div
      // onMouseMove={onMouseMove}
      // onMouseOut={onMouseOut}
      // onBlur={onBlur}
      // onFocus={onFocus}
      className={`${hoverCss.node}`}
    >
      <Draggable draggable={mode === 'edit' && model.isDragable()} droppable={mode === 'edit' && droppable} obj={model} draggingHoverCss="animated pulse">
        <FlowNode value={model} mode={mode} style={style} onUpdateNode={onUpdateNode} onClick={() => onClickNode && onClickNode(model)}>
          {mode === 'detail' && (
            <div className={`${hoverCss.inCounts}`}>{`${inCounts}人`}</div>
          )}
        </FlowNode>
      </Draggable>
    </div>
  );
}
