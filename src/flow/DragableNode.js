import React, { useContext, useState } from 'react';
import FlowEditorContext from './FlowEditorContext';
import FlowNodeDragContext from './FlowNodeDragContext';

export default function DragableNode({ mode, children, obj }) {
  const { logProvider } = useContext(FlowEditorContext);
  const { dragging, dragDrop } = useContext(FlowNodeDragContext);
  const draggable = mode === 'edit';
  const log = logProvider.getLogger('DragableNode');
  const [hoverCss, setHoverCss] = useState('');

  const onDrag = e => {
    if (!draggable) return;

    e.preventDefault();
    e.stopPropagation();
    if (dragging?.node !== obj) {
      log.debug('drag', JSON.stringify(obj));
      dragDrop({ type: 'drag', obj });
    }
  };

  const onMouseOver = () => {
    setHoverCss('animated pulse');
    // setHoverCss('');
  };

  const onMouseOut = () => {
    setHoverCss('');
  };

  return (
    <div
      draggable={draggable}
      className={`dragable ${mode}
      ${dragging?.node ? 'dragging' : ''}
      ${dragging?.canDrop === true ? 'droppable' : ''}
      ${dragging?.canDrop === false ? 'notDroppable' : ''}
      ${hoverCss}`}
      onDrag={onDrag}
      onMouseOver={onMouseOver}
      onFocus={onMouseOver}
      onMouseOut={onMouseOut}
      onBlur={onMouseOut}
    >
      {children}
    </div>
  );
}
