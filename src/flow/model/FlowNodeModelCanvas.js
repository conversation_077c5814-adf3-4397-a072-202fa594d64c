import _ from 'lodash';
import FlowNodeModel from './FlowNodeModel';
import Log from '../../utils/log';

const log = Log.getLogger('FlowNodeModelCanvas');
/**
 * 画布中的流程节点
 */
export default class FlowNodeModelCanvas extends FlowNodeModel {
  constructor({ displayName, nodeId, fatherIds, childrenIds, branchIndex, x, y, exeStatus, retryTime, inCounts, detail, ...json }) {
    super(json);
    _.forEach(
      {
        displayName,
        nodeId,
        fatherIds: fatherIds || [],
        childrenIds: childrenIds || [],
        branchIndex: branchIndex || 0,
        x,
        y,
        exeStatus,
        retryTime,
        inCounts,
        detail
      },
      (v, k) => (this[k] = v)
    );
  }

  toJson() {
    const { displayName, nodeId, fatherIds, childrenIds, branchIndex, x, y, exeStatus, retryTime, inCounts, detail } = this;
    return {
      ...super.toJson(),
      displayName,
      nodeId,
      fatherIds,
      childrenIds,
      branchIndex,
      x,
      y,
      exeStatus,
      retryTime,
      inCounts,
      detail
    };
  }

  getAtTime = async atTimeDataProvider => {
    if (this.busiType === 'AtTimeNode' && this.detail && atTimeDataProvider) {
      const atTimeValue = await atTimeDataProvider(this.detail);
      return atTimeValue;
    }
  }

  isDragable() {
    if (this.busiType === 'JoinHelperNode' && this.childrenIds.length === 1 && this.getChild0()?.joinType === 'END') return true;
    if (this.joinType === 'FUNCTION') {
      return true;
    }
    // if (this.busiType === 'JoinHelperNode' && this.childrenIds.length === 1 && this.getChild0()?.joinType === 'END') return true;
    return false;
  }

  isEntry() {
    return this.joinType === 'ENTRY';
  }

  isEditable() {
    return !this.busiType?.endsWith('HelperNode') && this.busiType !== 'JoinNode';
  }

  isDeleteAble() {
    return this.joinType !== 'END' && this.busiType !== 'JoinHelperNode';
  }

  canInsertBefore() {
    if (!_.isEmpty(this.fatherIds) && this.joinType !== 'ENTRY') {
      return true;
    }
    return false;
  }

  canDropBefore(draggingNode) {
    if (!draggingNode) return false;
    if (draggingNode.nodeId === this.nodeId) return false;
    if (draggingNode.joinType === 'ENTRY') return false;
    if (this.joinType === 'ENTRY') return false;
    if (draggingNode.busiType === 'JoinNode' && this.busiType !== 'ExitHelperNode') return false;
    if (this.busiType === 'JoinHelperNode' && this.getLeftNode()?.busiType === 'JoinNode') return false;
    // 如果拖动的是join节点
    if (draggingNode.busiType === 'JoinHelperNode') {
      // 需要判断this是不是当前节点直系祖先, 避免死循环
      if (this.isDescendants(draggingNode.nodeId) || this.isAncestor(draggingNode.nodeId)) return false;
      // 父节点不能是分支节点
      if (this.getFather0() && this.getFather0().joinType === 'SPLIT') return false;
    }
    if (this.busiType === 'JoinHelperNode' && draggingNode.joinType === 'SPLIT') return false;
    return true;
  }

  canDrop(draggingModel) {
    if (draggingModel.busiType === 'JoinHelperNode' && this.busiType === 'JoinHelperNode') {
      return true;
    }
    return false;
  }

  /**
   * 这个节点的子节点是一个分支节点并且子节点和它不在一行上
   * 的话就返回true，表示可以打断分支
   */
  canBreakChild() {
    const child = this.getChild0();
    if (child?.y !== this.y && child?.busiType === 'JoinHelperNode') {
      return true;
    }
    return false;
  }

  /**
   * 是否可以只把自己删除了，如果删除这种节点，让后面的数据接在这个节点的前面
   */
  canDeleteOnlyMe() {
    if (this.joinType === 'FUNCTION') {
      return true;
    }
    return false;
  }

  addTestBranch() {
    if (!this.detail) {
      this.detail = {};
    }

    if (!this.detail.branchList) {
      this.detail.branchList = [];
    }

    this.detail.branchList.push({
      branchName: `test branch ${this.detail.branchList.length}`
    });
  }

  check() {
    if (this.joinType === 'FUNCTION') {
      if (this.childrenIds.length !== 1 || this.fatherIds.length !== 1) {
        log.warn(`FUNCTION 节点应该一进一出 nodeId=${this.nodeId} name=${this.name} x=${this.x} y=${this.y}`);
      }
    } else if (this.busiType === 'END') {
      if (this.childrenIds.length !== 0 || this.fatherIds.length !== 1) {
        log.warn(`END 节点应该0进1出 nodeId=${this.nodeId} name=${this.name} x=${this.x} y=${this.y}`);
      }
    } else if (this.busiType === 'ENTRY') {
      if (this.childrenIds.length !== 1 || this.fatherIds.length !== 0) {
        log.warn(`END 节点应该1进0出 nodeId=${this.nodeId} name=${this.name} x=${this.x} y=${this.y}`);
      }
    }
    // join节点不应该只有一个父一个子，不符合文档要求
    if (this.busiType === 'JoinHelperNode') {
      // 子类方法，不会abstract方法
      // if (this.fatherIds.length === 1 && this.childrenIds.length === 1) {
      //   log.warn(`JoinHelperNode不应该只有一个父一个子 nodeId=${this.nodeId} name=${this.name} x=${this.x} y=${this.y}`);
      // }
      if (this.getFather0()) {
        if (this.getFather0().joinType === 'SPLIT') {
          log.warn(`JoinHelperNode前面不应该是切分型组件 nodeId=${this.nodeId} name=${this.name} x=${this.x} y=${this.y}`);
        }
      }
    } else if (this.joinType === 'SPLIT') {
      if (this.detail?.branchLis && this.childrenIds.length !== this.detail?.branchList?.length) {
        log.warn(`分支节点的子节点数和分支节点数不等 nodeId=${this.nodeId} name=${this.name} x=${this.x} y=${this.y} childrenIds.length=${this.childrenIds.length} this.detail.branchList.length=${this.detail?.branchList?.length}`);
      }
    }
  }
}
