import _ from 'lodash';
/**
 * 流程节点model
 */
export default class FlowNodeModel {
  constructor({ id, groupId, name, busiType, fakeable, displayInBox, color, icon, shape, orders, joinType, status }) {
    _.forEach(
      { id, groupId, name, busiType, fakeable, displayInBox, color, icon, shape, orders, joinType, status },
      (v, k) => {
        this[k] = v;
      }
    );
  }

  toJson() {
    const { id, groupId, name, busiType, fakeable, displayInBox, color, icon, shape, orders, joinType, status } = this;
    return { id, groupId, name, busiType, fakeable, displayInBox, color, icon, shape, orders, joinType, status };
  }
}
