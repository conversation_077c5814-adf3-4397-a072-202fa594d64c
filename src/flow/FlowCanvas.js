import React, { useState, useEffect, useContext, useReducer } from 'react';
import _ from 'lodash';
import FlowEditorContext from './FlowEditorContext';
import FlowCanvasCtrl from './ctrl/FlowCanvasCtrl';
// import FlowNodeDragContext from './FlowNodeDragContext';
import FlowCanvasContext from './FlowCanvasContext';

import { auxLineFactory, connectLineFactory, nodeFactory, auxRectFactory } from './FlowCanvasReducer';
import FlowNodeEditor from './FlowNodeEditor';
import GrabbingBox from '../grabbing';

import './FlowCanvas.scss';

function connectingNodeReducer(state, action) {
  const { startPos, endPos, moving } = state;
  const { type, pos } = action;

  if (pos === null) return state;

  if (type === 'mouseDown') {
    return {
      startPos: pos,
      endPos: null,
      moving: true
    };
  }

  if (type === 'mouseUp') {
    if (startPos.x === pos.x && startPos.y === pos.y) {
      return {
        ...state,
        moving: false
      };
    }
    return {
      startPos,
      endPos: pos,
      moving: false
    };
  }

  if (type === 'mouseMove') {
    if (!startPos || !moving) return state;
    if (endPos && endPos.x === pos.x && endPos.y === pos.y) return state;
    if (startPos.x === pos.x && startPos.y === pos.y) return state;
    return {
      startPos,
      endPos: pos,
      moving: true
    };
  }

  if (type === 'clear') {
    return {
      startPos: null,
      endPos: null,
      moving: false
    };
  }
  return state;
}

const empty = {};
const getConnectReducer = mode => {
  if (mode === 'template') return connectingNodeReducer;
  return () => {
    return empty;
  };
};

/**
 * @param {Object} [props]
 * @param {Object} [props.dataProvider]
 * @param {any} [props.value]
 * @param {boolean} [props.debug]
 * @param {'edit' | 'detail' | 'preview' | 'template'} [props.mode]
 * @param {() => void} [props.onChange]
 * @param {() => void} [props.onEditNode]
 * @param {() => void} [props.onClickNode]
 * @param {GrabbingBoxProps} [props.grabbing]
 */
export default function FlowCanvas(props) {
  const { value, onChange, debug, mode, onEditNode, onClickNode, dataProvider, grabbing } = props;

  const [flows, setFlows] = useState(value);
  const { logProvider, canvasConfig } = useContext(FlowEditorContext);
  const [log] = useState(logProvider.getLogger('FlowCanvas'));

  // 画布控制器
  const [canvasCtrl, setCanvasCtrl] = useState(null);
  // 正在操作的起始节点
  const [connectingNode, connectingNodeAction] = useReducer(getConnectReducer(mode), {
    startPos: null,
    endPos: null
  });
  const [canvasContext, setCanvasContxt] = useState({});
  const [hoverNode, setHoverNode] = useState({
    hidden: true,
    unitRect: null,
    model: null
  });

  const [canvas, setCanvas] = useState({
    // 背景高度宽度
    bg: { width: 100, height: 100 },
    // 辅助线
    auxLines: [],
    // 辅助块
    auxRects: [],
    // 节点容器
    nodeContainers: [],
    // 正在操作的线
    activeLine: '',
    // 父子线
    cnLines: []
  });

  useEffect(() => {
    setFlows(value);
  }, [value]);

  useEffect(() => {
    if (mode !== 'edit' && mode !== 'template') return;
    if (value !== flows) onChange(flows);
    log.debug('onChange', flows);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [flows, log, mode]);

  useEffect(() => {
    log.debug('canvasConfig changed');
    setCanvasCtrl(new FlowCanvasCtrl(canvasConfig));
  }, [canvasConfig, log]);

  useEffect(() => {
    if (!canvasCtrl) return;
    canvasCtrl.helperNodeMap = canvasConfig.helperNodes.reduce((a, b) => {
      a[b.busiType] = b;
      return a;
    }, {});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [canvasConfig.helperNodes]);

  useEffect(() => {
    if (!canvasCtrl) return;
    if (flows === canvasCtrl.flows) return;
    // edit 模式下自动修复错误
    const fixable = mode === 'edit';
    const fixed = canvasCtrl.setFlows(flows, fixable);
    // 如果修复了，那么不用刷新canvas，继续setFlow，然后等待下次刷新
    if (fixed) {
      log.debug('修复了flows');
      setFlows(canvasCtrl.getFlows());
      return;
    }

    setCanvas({
      // 设置背景
      bg: canvasCtrl.getBg(),
      // 设置辅助线
      auxLines: canvasCtrl.getAuxLine(auxLineFactory),
      // 辅助容器
      auxRects: canvasCtrl.getAuxRects(auxRectFactory),
      // 设置节点容器
      nodeContainers: canvasCtrl.getNodeUnit(nodeFactory),
      // 获得连接线
      cnLines: canvasCtrl.getNodeConnectLines(connectLineFactory),
      activeLine: ''
    });
  }, [canvasCtrl, flows, mode, log]);

  useEffect(() => {
    if (canvasCtrl?.changed) {
      const json = canvasCtrl.toJson();
      log.debug('canvasChanged', json);
      onChange(json);
    }
  }, [canvasCtrl, onChange, log]);

  useEffect(() => {
    const onDropNode = (x, y, flowNode) => {
      canvasCtrl.addNode(x, y, flowNode) && setFlows(canvasCtrl.getFlows());
    };
    const onDeleteNode = model => {
      canvasCtrl.deleteNode(model) && setFlows(canvasCtrl.getFlows());
    };
    const onUpdateNode = model => {
      canvasCtrl.updateNode(model) && setFlows(canvasCtrl.getFlows());
    };
    const onInsertNode = (node, insertBeforeNodeId, fatherNodeId) => {
      canvasCtrl.insertNode(node, insertBeforeNodeId, fatherNodeId) && setFlows(canvasCtrl.getFlows());
    };

    const onAddBranch = node => {
      canvasCtrl.addNodeBranch(node) && setFlows(canvasCtrl.getFlows());
    };

    const onBreakChild = node => {
      canvasCtrl.breakChild(node) && setFlows(canvasCtrl.getFlows());
    };

    const onNodeHover = (node, unitRect) => {
      let hidden = false;
      if ((mode !== 'edit' && mode !== 'template') || node === null || (!node.isEditable() && !node.isDeleteAble())) {
        hidden = true;
      }
      setHoverNode({
        hidden,
        model: node,
        unitRect
      });
    };

    setCanvasContxt({
      mode,
      onDropNode,
      onEditNode,
      onClickNode,
      onUpdateNode,
      onDeleteNode,
      connectingNodeAction,
      onInsertNode,
      onAddBranch,
      connectingNode,
      onBreakChild,
      canvasCtrl,
      debug,
      onNodeHover,
      editingNode: !hoverNode.hidden,
      dataProvider
    });
  }, [mode, connectingNodeAction, canvasCtrl, log, onEditNode, onClickNode, connectingNode, debug, hoverNode.hidden]);

  const { bg, auxLines, auxRects, activeLine, cnLines, nodeContainers } = canvas;

  log.debug('before render', flows, canvas);

  const onNodeEndEdit = () => {
    setHoverNode({
      hidden: true,
      model: null,
      unitRect: null
    });
  };

  const grabbingBoxProps = _.isBoolean(grabbing) ? { grabbing: true } : { ...grabbing };

  return (
    <FlowCanvasContext.Provider value={canvasContext}>
      <div className="wolf-static-cpnt FlowCanvas">
        <GrabbingBox {...grabbingBoxProps}>
          <div className="outerContainer" style={{ ...bg }}>
            <div className="bg">
              <svg className="auxLineContainer">{debug && auxLines}</svg>
              <div className="auxRectContainer">{debug && auxRects}</div>
            </div>

            <div className="container">
              <FlowNodeEditor
                hidden={hoverNode.hidden}
                unitRect={hoverNode.unitRect}
                model={hoverNode.model}
                onEditNode={canvasContext.onEditNode}
                onAddBranch={canvasContext.onAddBranch}
                onBreakChild={canvasContext.onBreakChild}
                onDeleteNode={canvasContext.onDeleteNode}
                mode={canvasContext.mode}
                debug={canvasContext.debug}
                onEndEdit={onNodeEndEdit}
              />
              {nodeContainers}
              <svg className="connectLine">
                <defs>
                  <marker id="markerArrow" markerWidth="13" markerHeight="13" refX="2" refY="4" orient="auto" markerUnits="strokeWidth">
                    <path d="M2,2 L2,6 L6,4 L2,2" fill="#ddd" />
                  </marker>
                </defs>
                {activeLine}
                {cnLines}
              </svg>
              <div className="help" hidden={!_.isEmpty(value)}>
                {`${mode === 'edit'
                  ? '拖动一个“活动开始”控件到这里'
                  : '没有节点数据'
                }`}
              </div>
            </div>
          </div>
        </GrabbingBox>
      </div>
    </FlowCanvasContext.Provider>
  );
}
