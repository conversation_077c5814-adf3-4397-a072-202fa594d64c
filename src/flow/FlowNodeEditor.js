import React, { useState, useContext } from 'react';
import { BarsOutlined, DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Tooltip } from 'antd';
import { DraggableContext } from './DraggableContext';

export default function FlowNodeEditor({
  hidden,
  unitRect,
  model,
  onEditNode,
  onAddBranch,
  onBreakChild,
  onDeleteNode,
  mode,
  debug,
  onEndEdit
}) {
  const { context: { dragging } } = useContext(DraggableContext);
  const [editing, setEditing] = useState(false);
  const [toolBar, setToolBar] = useState(true);

  const onDoubleClick = () => {
    if (debug && model.joinType === 'SPLIT') {
      onAddBranch(model);
    }
  };

  const endEditCallback = () => {
    setEditing(false);
    onEndEdit();
  };

  const onMouseLeave = () => {
    if (!editing) {
      onEndEdit();
    }
    setToolBar(true);
  };

  return (
    <div
      className="FlowNodeEditor"
      style={{ ...unitRect }}
      hidden={hidden || dragging}
      onMouseLeave={onMouseLeave}
    >
      <div hidden={toolBar} className="toolBar">
        {model && model.canBreakChild()
        && <Tooltip placement="rightTop" title="解除合并点">
          {/* <Icon onClick={() => endEditCallback(onBreakChild(model))} className="toolIcon" type="duankai" /> */}
          <svg onClick={() => endEditCallback(onBreakChild(model))} className="wolf-icon toolIcon" style={{ width: 16, height: 16 }} aria-hidden="true">
            <use xlinkHref="#iconduankai" />
          </svg>
        </Tooltip>}
        {model && model.isEditable()
        && <Tooltip placement="rightTop" title="编辑配置">
          <EditOutlined onClick={() => endEditCallback(onEditNode(model))} className="toolIcon" />
        </Tooltip>}
        {model !== null && model.isDeleteAble() && mode !== 'template'
        && <Tooltip placement="rightTop" title={<div style={{ width: 120 }}>删除组件，将删除组件所有关联流程</div>}>
          <DeleteOutlined
            onClick={() => { setEditing(true); endEditCallback(onDeleteNode(model)); }}
            className="toolIcon"
          />
        </Tooltip>}
        {debug && <PlusOutlined className="toolIcon" onClick={() => onDoubleClick()} />}
      </div>
      <Button
        icon={<BarsOutlined />}
        className="deleteButton"
        hidden={!toolBar}
        onMouseEnter={() => setToolBar(false)}
      />
      {/* <Icon className="toolIcon" type="dash" /> */}
      {/* {model && model.isEditable() && (
        <Button
          type="primary"
          size="small"
          className="editButton"
          onClick={() => endEditCallback(onEditNode(model))}
        >
          编辑
        </Button>
      )}
      {model && model.canBreakChild() && (
        <Button
          type="primary"
          size="small"
          className="editButton"
          onClick={() => endEditCallback(onBreakChild(model))}
        >
          解除合并
        </Button>
      )} */}
      {/* <Popconfirm
        placement="right"
        title="删除活动开始组件，将删除之后所有的流程。"
        onConfirm={() => endEditCallback(onDeleteNode(model))}
        onCancel={() => endEditCallback()}
        okText="删除"
        cancelText="取消"
      >
        <Button
          icon="delete"
          className="deleteButton"
          hidden={
            model === null || !model.isDeleteAble() || mode === 'template'
          }
          onClick={() => setEditing(true)}
        />
      </Popconfirm> */}
      {/* {debug && <Button className="breakButton" icon="plus" onClick={() => onDoubleClick()} />} */}
      {/* <Popconfirm
    placement="right"
    title="解除合并分支"
    onConfirm={() => onBreakChild(model)}
    okText="删除"
    cancelText="取消"
  >
    <Button
      className="breakButton"
      hidden={model === null || !model.canBreakChild()}
    >
      <svg className="wolf-icon" aria-hidden="true">
        <use xlinkHref="#icongaibanxianxingtubiao-" />
      </svg>
    </Button>
  </Popconfirm> */}
    </div>
  );
}
