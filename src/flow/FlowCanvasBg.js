// import React from 'react';

// export default function FlowCanvasBg(canvasCtrl) {
//   // 背景高度宽度
//   const [bg, setBg] = useState({ width: 100, height: 100 });

//   const auxLineFactory = (x1, y1, x2, y2) => {
//     return <line key={`${x1}_${y1}_${x2}_${y2}`} x1={x1} y1={y1} x2={x2} y2={y2} className="auxLine" />;
//   };

//   return (
//     <div className="bg" style={{ width: bg.width, height: bg.height }}>
//       <svg className="auxLineContainer">
//         {canvasCtrl.getAuxLine(auxLineFactory)}
//       </svg>
//     </div>
//   );
// }
