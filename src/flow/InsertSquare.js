import React, { useState, useEffect, useContext } from 'react';
import { Modal, Radio } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { DraggableContext } from './DraggableContext';
import FlowCanvasContext from './FlowCanvasContext';
import Draggable from './Draggable';

export default function InsertSquare({ insertRect, insertBeforeNode }) {
  const { context, dragDrop } = useContext(DraggableContext);
  const { mode, onInsertNode, canvasCtrl } = useContext(FlowCanvasContext);
  const [droppable, setDroppable] = useState(false);
  const [dropHover, setDropHover] = useState(false);

  useEffect(() => {
    if (mode !== 'edit') return;

    setDroppable(canvasCtrl.canDropBefore(context.dragging, insertBeforeNode));
  }, [insertBeforeNode, context.dragging, mode, canvasCtrl]);

  const onDrop = e => {
    e.preventDefault();
    e.stopPropagation();
    dragDrop({ type: 'drop' });
    if (context.dragging && insertBeforeNode) {
      const fatherNodesExcludeJoinNodes = insertBeforeNode.fatherIds.map(v => insertBeforeNode.dag.getNodeByNodeId(v)).filter(v => v.busiType !== 'JoinNode');
      let value = fatherNodesExcludeJoinNodes[0].nodeId;
      if (fatherNodesExcludeJoinNodes.length > 1) {
        const onRadioChange = event => {
          value = event.target.value;
        };

        Modal.confirm({
          title: '请选择要插入的父节点',
          icon: <ExclamationCircleOutlined />,
          content: <Radio.Group onChange={onRadioChange} defaultValue={value}>
            {fatherNodesExcludeJoinNodes.map(v => <Radio value={v.nodeId}>{`${v.name}[${v.nodeId}]`}</Radio>)}
          </Radio.Group>,
          okText: '确认',
          cancelText: '取消',
          onOk() {
            onInsertNode(context.dragging, insertBeforeNode.nodeId, value);
          }
        });
      } else {
        onInsertNode(context.dragging, insertBeforeNode.nodeId, value);
      }
    }
  };

  return (
    <div
      className={`insertSquare  ${droppable ? 'animated bounceIn' : ''} ${dropHover ? 'hover' : ''}`}
      hidden={!droppable || !context.dragging}
      style={{ ...insertRect }}
    >
      <Draggable draggable={false} droppable={droppable} obj={null} droppingHoverCss="animated pulse" onDrop={onDrop} onDropHover={v => setDropHover(v)}>
        <div style={{ width: insertRect.width, height: insertRect.height }} />
      </Draggable>
    </div>
  );
}
