@import '../variable.scss';
.wolf-static-cpnt.FlowNode {
  display: inline-block;
  background-color: transparent;
  > .container {
    // border-right: 1px dotted #ccc;
    // border-bottom: 1px dotted #ccc;
    background-color: transparent;
    width: 60px;
    text-align: center;
    overflow: hidden;
    > .icon,
    .icon:hover {
      display: inline-block;
      width: 44px;
      height: 44px;
      line-height: 44px;
      color: #fff;
      position: relative;
      background-color: #ccc;
      &.CIRCLE {
        border-radius: 50%;
      }
      &.SQUARE {
        border-radius: 8px;
      }
      &.DIAMOND {
        width: 36px;
        height: 36px;
        border-radius: 5px;
        transform: rotate(45deg) !important;
        left: 0px;
        top: 7px;
        margin-bottom: 7px;
        position: relative;
        svg {
          position: absolute;
          transform: rotate(-45deg) !important;
          top: 6px;
          left: 6px;
        }
      }
      &.LIT_DIAMOND {
        width: 20px;
        height: 20px;
        border-radius: 5px;
        transform: rotate(45deg) !important;
        left: 0px;
        top: 14px;
        margin-bottom: 14px;
        position: relative;
        .wolf-icon {
          width: 10px;
          height: 10px;
        }
        svg {
          position: absolute;
          transform: rotate(-45deg) !important;
          top: 5px;
          left: 5px;
        }
      }
    }
    .name,
    .userCount {
      font-family: PingFangSC-Regular;
      background-color: #fff;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.918);
      line-height: 15px;
      word-break: break-all;
      > div.animated {
        border-radius: 5px;
        background-color: $primary_light_color;
      }
    }
    .name {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }
    .id {
      font-family: PingFangSC-Regular;
      background-color: #fff;
      font-size: 12px;
      color: rgb(128, 138, 135);
      line-height: 12px;
      word-break: break-all;
      > div.animated {
        border-radius: 5px;
        background-color: $primary_light_color;
      }
    }
    .editable-cell-value-wrap-name {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.85);
      // line-height: 22px;
      margin: 4px 0px;
    }
    .editable-cell-value-wrap-name:hover {
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      // padding: 4px 11px;
    }
    &.edit.editable,
    &.template.editable,
    &.detail.editable {
      cursor: pointer;
      &.noData,
      &.invalid {
        .icon {
          background-color: #ccc;
        }
      }
      &.edit.invalid,
      &.template.invalid {
        .icon {
          border: 1px dashed $danger_color;
        }
      }
    }
  }
}
