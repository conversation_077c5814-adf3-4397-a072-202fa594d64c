@import '../variable.scss';

.wolf-static-cpnt.FlowCanvas {
  // 默认样式
  min-height: 200px;
  min-width: 400px;
  height: calc(100% - 40px);
  width: 100%;
  overflow: auto;


  // grabbing样式
  // height: calc(100% - 40px);
  // width: 100%;
  // overflow: hidden;

  .outerContainer {
    position: relative;

    >.bg {

      // visibility: hidden;
      >.auxLineContainer {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;

        >.auxLine {
          stroke: #ccc;
          stroke-width: 1;
        }
      }

      >.auxRectContainer {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;

        >.AuxRect {
          position: absolute;

          >.auxNumber {
            position: absolute;
            left: 0;
            top: 0;
          }
        }
      }
    }

    >.container {
      z-index: 100;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;

      >.FlowNodeEditor {
        z-index: 11000;
        position: absolute;

        .ant-btn {
          transition: unset;
        }

        .toolBar {
          background-color: #e8e8e8;
          color: rgba(0, 0, 0, .65);
          border: 1px solid #d9d9d9;
          border-radius: 3px;
          width: 33px;
          height: 100%;
          position: absolute;
          right: 0;
          text-align: center;

          .toolIcon {
            font-size: 16px;
            margin-top: 10px;

            &:hover {
              color: $danger_color;
            }

            ;
          }
        }

        .deleteButton {
          &:hover {
            color: $danger_color;
          }

          position: absolute;
          right: 0px;
          top: 0px;
          border: 0;
        }

        .breakButton {
          &:hover {
            color: $danger_color;
          }

          position: absolute;
          right: 3px;
          top: 24px;
          border: 0;
          width: 24px;
          padding: 0;
        }

        .editButton {
          position: absolute;
          width: 100px;
          bottom: 15px;
          left: calc((100% - 100px) / 2);
        }

        background-color: #fff;
        -webkit-box-shadow: 0px 0px 14px 0px rgba(153, 153, 153, 1);
        -moz-box-shadow: 0px 0px 14px 0px rgba(153, 153, 153, 1);
        box-shadow: 0px 0px 14px 0px rgba(153, 153, 153, 1);
      }

      >.insertSquare {
        position: absolute;
        background-color: #fff;
        border-radius: 8px;
        position: absolute;
        border: 1px dashed $active_color;
        z-index: 5001;

        &.hover {
          background-color: #eee;
        }
      }

      .node {
        z-index: 100000;
        position: absolute;
        text-align: center;

        &.edit.droppable {
          background-color: transparent;
          border: 1px dashed $active_color;
          border-radius: 32px;
        }

        &.edit.hover {
          background-color: #eee;
        }

        >.branchName {
          z-index: 90000;
          position: absolute;
          top: 8px;
          left: -80px;
          width: 80px;
          font-size: 11px;
          color: $active_color;
          font-family: PingFangSC-Regular;
          word-break: break-all;
          background: transparent;
          // text-align: right;
          display: flex;
          justify-content: flex-end;
        }

        >.timerRight {
          z-index: 90000;
          position: absolute;
          top: 8px;
          right: -80px;
          width: 80px;
          font-size: 11px;
          color: $active_color;
          font-family: PingFangSC-Regular;
          background: transparent;
          // text-align: right;
          display: flex;
          justify-content: flex-start;
        }

        >.tagRight {
          z-index: 90000;
          position: absolute;
          display: flex;
          flex-direction: column;
          top: 8px;
          right: -80px;
          width: 80px;
          font-size: 11px;
          color: $active_color;
          font-family: PingFangSC-Regular;
          background: transparent;
          // text-align: right;
          display: flex;
          justify-content: flex-start;

          >div {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: left;
          }
        }
      }

      >.connectLine {
        position: absolute;
        z-index: 5000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;

        >.group {

          // pointer-events: all;
          >g {
            position: absolute;

            >line.show {
              stroke-width: 2;
              stroke: #ddd;
            }

            >line.aux {
              stroke-width: 20;
              stroke: transparent;
            }

            >circle {
              fill: transparent;
            }
          }

          &.active {
            g {
              >line.show {
                stroke: $active_color;
                stroke-width: 4;
              }

              >circle {
                fill: $active_color;
              }
            }
          }
        }
      }

      .help {
        position: absolute;
        top: 55px;
        left: 110px;
        width: 120px;
      }
    }
  }
}