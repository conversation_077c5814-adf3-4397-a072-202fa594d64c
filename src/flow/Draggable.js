import React, { useState, useContext, useEffect } from 'react';
import debounce from 'lodash/debounce';
import { DraggableContext } from './DraggableContext';
import './Draggable.scss';
import Log from '../utils/log';

const log = Log.getLogger('Draggable');
/**
 * 负责drag和drop
 */
export default function Draggable({ children, draggable, droppable, draggingHoverCss, droppingHoverCss, obj, onDropHover, onDrop, onHover }) {
  const [hover, setHover] = useState(false);
  const [dropHover, setDropHover] = useState(false);
  const { context, dragDrop } = useContext(DraggableContext);
  const [isDragging, setIsDragging] = useState(false);
  const [draggingDroppable, setDraggingDroppable] = useState(null);
  const [isDropping, setIsDropping] = useState(false);
  const [hidden, setHidden] = useState(false);

  const onDragStart = e => {
    e.dataTransfer.effectAllowed = 'copyMove';
    // getCurrentTarget(e).style.cursor = 'move';
    if (!draggable) return;
    if (!isDragging) setIsDragging(true);
    if (context.dragging !== obj) {
      log.debug('dragStart', JSON.stringify(obj));
      dragDrop({ type: 'dragStart', dragging: obj });
    }
    return true;
  };

  const onDragEnd = e => {
    e.stopPropagation();
    e.preventDefault();
    if (!draggable) return;
    if (isDragging) setIsDragging(false);
    if (context.dragging !== obj) {
      dragDrop({ type: 'dragEnd' });
    }
  };

  const onDragEnter = e => {
    // 拖动的是自己的，禁止其他动作
    if (isDragging) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    if (!isDropping) setIsDropping(true);
    if (context.dropping !== obj) {
      dragDrop({ type: 'dragEnter', dropping: obj, droppable });
    }
    if (!dropHover) setDropHover(true);
  };

  const onDragLeave = e => {
    // 拖动的是自己的，禁止其他动作
    if (isDragging) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    if (isDropping) setIsDropping(false);
    if (context.dropping !== obj) {
      dragDrop({ type: 'dragLeave', dropping: obj, droppable });
    }
    if (dropHover) setDropHover(false);
  };

  const onMouseEnter = () => {
    debounce(() => {
      if (!hover) {
        setHover(true);
        setHidden(true);
        setHidden(false);
      }
    }, 100)();
  };

  const onMouseOut = () => {
    if (hover) setHover(false);
  };

  const onDragOver = e => {
    e.preventDefault();
    if (isDropping) {
      e.dataTransfer.dropEffect = 'copy';
    }
    return false;
  };

  useEffect(() => {
    if (onHover) onHover(hover);
  }, [hover, onHover]);

  useEffect(() => {
    if (onDropHover) onDropHover(dropHover);
  }, [dropHover, onDropHover]);

  useEffect(() => {
    if (!context.dragging && dropHover) {
      setDropHover(false);
    }
  }, [context.dragging, dropHover]);

  // 拖动的如果是当前物体，并且不能drop，需要设置样式
  useEffect(() => {
    debounce(() => {
      const { dragging } = context;
      if (dragging && dragging === obj && draggingDroppable !== context.droppable) {
        setDraggingDroppable(context.droppable);
      }
    }, 100)();
  }, [context, obj, draggingDroppable]);

  const _onDrop = e => {
    if (dropHover) setDropHover(false);
    if (onDrop) onDrop(e);
  };

  return (
    <div
      draggable={draggable}
      className={`wolf-static-cpnt draggable
      ${draggable ? 'enable' : ''}
      ${isDragging ? 'dragging' : ''}
      ${hover && draggable ? `${draggingHoverCss || ''}` : ''}
      ${isDropping ? `dropping ${droppingHoverCss || ''}` : ''}
      ${droppable === true ? 'droppable' : ''}
      ${droppable === false || draggingDroppable === false ? 'notDroppable' : ''}`}
      onDragEnter={onDragEnter}
      onDragLeave={onDragLeave}
      onDragOver={onDragOver}
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
      onMouseEnter={onMouseEnter}
      onFocus={onMouseEnter}
      onMouseOut={onMouseOut}
      onBlur={onMouseOut}
      onDrop={_onDrop}
      hidden={hidden}
    >
      {children}
    </div>
  );
}
