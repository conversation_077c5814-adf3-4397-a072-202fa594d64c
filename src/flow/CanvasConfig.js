export default {
  initX: 1,
  initY: 1,
  maxX: 100,
  maxY: 100,
  showAuxLine: true,
  unitHeight: 136,
  unitWidth: 136,
  iconWidth: 48,
  iconHeight: 48,
  nodeContainerWidth: 64,
  nodeContainerHeight: 64,
  helperNodes: [
    {
      name: '退出',
      busiType: 'ExitHelperNode',
      color: '#1EC78B',
      icon: '#iconyonghuzhongxin-1',
      shape: 'CIRCLE',
      joinType: 'END'
    },
    {
      name: '',
      busiType: 'Join<PERSON>elperNode',
      color: '#FCA400',
      icon: '#iconicon_xiaochengxu',
      shape: 'LIT_DIAMOND',
      joinType: 'JOIN'
    },
    {
      name: '合并分支',
      displayName: '合并分支',
      busiType: 'JoinNode',
      color: '#FCA400',
      icon: '#icongaibanxianxingtubiao-',
      shape: 'DIAMOND',
      joinType: 'FUNCTION'
    }
  ]
};
