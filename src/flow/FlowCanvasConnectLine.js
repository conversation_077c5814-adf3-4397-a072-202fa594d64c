import React, { Fragment, useState, useContext, useEffect } from 'react';
// import FlowNode from './FlowNode';
// import FlowNodeDragContext from './FlowNodeDragContext';
import FlowCanvasContext from './FlowCanvasContext';

export default function FlowCanvasConnectLine({ lines, fatherNodeId }) {
  // const { draggingNode, dragDrop } = useContext(FlowNodeDragContext);
  const { canvasCtrl } = useContext(FlowCanvasContext);
  const [, setNodeRect] = useState({});

  useEffect(() => {
    setNodeRect(canvasCtrl.getNodeContainerRect(fatherNodeId));
  }, [canvasCtrl, fatherNodeId]);

  const [active, setActive] = useState(false);
  const onMouseMove = () => {
    // if (mode !== 'edit' && mode !== 'template') return;
    if (!active) {
      setActive(true);
    }
  };
  const onMouseOut = () => {
    // if (mode !== 'edit' && mode !== 'template') return;
    if (active) {
      setActive(false);
    }
  };

  return (
    <>
      {/* <svg className="wolf-icon" aria-hidden="true">
        <use xlinkHref="#iconicon_yonghu_" />
      </svg> */}
      <g className={`group ${active ? 'active' : ''}`}>
        {lines.map(line => {
          const { x1, y1, x2, y2, connectTail, direction } = line;
          let props = {};
          if (connectTail) {
            props = {
              markerEnd: 'url(#markerArrow)'
            };
          }
          return (
            <g key={`${x1}_${y1}_${x2}_${y2}`}>
              <line
                className="show"
                x1={x1}
                y1={y1}
                x2={connectTail && direction === 'right' ? x2 - 32 : x2}
                y2={connectTail && direction === 'up' ? y2 + 32 : connectTail && direction === 'down' ? y2 - 32 : y2}
                onMouseMove={onMouseMove}
                onMouseOut={onMouseOut}
                onFocus={onMouseMove}
                onBlur={onMouseOut}
                // eslint-disable-next-line react/jsx-props-no-spreading
                {...props}
              />
              <line
                x1={x1}
                y1={y1}
                x2={x2}
                y2={y2}
                onMouseMove={onMouseMove}
                onMouseOut={onMouseOut}
                onFocus={onMouseMove}
                onBlur={onMouseOut}
                className="aux"
                // markerEnd="url(#markerArrow)"
              />
              {/* <circle
                cx={x2}
                cy={y2}
                r={3}
                onMouseMove={onMouseMove}
                onMouseOut={onMouseOut}
                onFocus={onMouseMove}
                onBlur={onMouseOut}
              /> */}
            </g>
          );
        })}
      </g>
    </>
  );
}
