import React from 'react';
// import _ from 'lodash';
import FLowCanvasNodeUnit from './FLowCanvasNodeUnit';
import FlowCanvasConnectLine from './FlowCanvasConnectLine';
// import Log from '../utils/log';

// const log = Log.getLogger('FlowCanvasReducer');

function AuxRect({ x, y, rect }) {
  return (
    <div className="AuxRect" style={{ ...rect }}>
      <div className="auxNumber">
        <span>{`${x}, ${y}`}</span>
      </div>
    </div>
  );
}

const lineFactory = (x1, y1, x2, y2, className) => {
  return <line key={`${x1}_${y1}_${x2}_${y2}`} x1={x1} y1={y1} x2={x2} y2={y2} className={className} />;
};

const auxLineFactory = (x1, y1, x2, y2) => {
  return lineFactory(x1, y1, x2, y2, 'auxLine');
};

const connectLineFactory = (lines, fatherNodeId, childNodeId) => (
  <FlowCanvasConnectLine
    key={`${fatherNodeId}_${childNodeId}`}
    lines={lines}
    fatherNodeId={fatherNodeId}
    childNodeId={childNodeId}
  />
);

const nodeFactory = ({ x, y, unitRect, containerRect, insertRect, model, branchName, maxY }) => (
  <FLowCanvasNodeUnit
    key={`${model?.nodeId || `${x}_${y}`}`}
    x={x}
    y={y}
    model={model}
    unitRect={unitRect}
    containerRect={containerRect}
    branchName={branchName}
    insertRect={insertRect}
    maxY={maxY}
  />
);

const auxRectFactory = (x, y, rect) => {
  return (
    <AuxRect
      key={`${x}_${y}`}
      x={x}
      y={y}
      rect={rect}
    />
  );
};

export { auxLineFactory, connectLineFactory, nodeFactory, auxRectFactory };

// const connectNode = (canvasCtrl, action) => {
//   log.debug('connectNode', action);
//   const { startPos, endPos, moving } = action;
//   if (startPos && endPos) {
//     const aLine = canvasCtrl.getConnectLine(
//       startPos.x,
//       startPos.y,
//       endPos.x,
//       endPos.y,
//       connectLineFactory
//     );
//     if (moving) {
//       return { activeLine: aLine };
//     } else {
//       // 结束移动，尝试连接节点
//       const connectedNode = canvasCtrl.connectPosNode(startPos, endPos);
//       if (connectedNode) {
//         return {
//           // 获得连接线
//           cnLines: canvasCtrl.getNodeConnectLines(connectLineFactory),
//           activeLine: ''
//         };
//       }
//       return {
//         activeLine: ''
//       };
//     }
//   }
// };

// const redraw = (canvasCtrl, action) => {
//   const { value } = action;
//   canvasCtrl.redraw(value);
//   return refreshCanvas(canvasCtrl);
// };

// const refreshCanvas = canvasCtrl => {
//   return {
//     // 设置背景
//     bg: canvasCtrl.getBg(),
//     // 设置辅助线
//     auxLines: canvasCtrl.getAuxLine(auxLineFactory),
//     // 辅助容器
//     auxRects: canvasCtrl.getAuxRects(auxRectFactory),
//     // 设置节点容器
//     nodeContainers: canvasCtrl.getNodeUnit(nodeFactory),
//     // 获得连接线
//     cnLines: canvasCtrl.getNodeConnectLines(connectLineFactory),
//     activeLine: ''
//   };
// };

// const dropNode = (canvasCtrl, action) => {
//   const { x, y, flowNode } = action;
//   const ret = canvasCtrl.addNode(x, y, flowNode);
//   if (ret) return refreshCanvas(canvasCtrl);
// };

// const deleteNode = (canvasCtrl, action) => {
//   const { node } = action;
//   if (!node) return;
//   const deletedNode = canvasCtrl.deleteNode(node);
//   if (_.isEmpty(deletedNode)) return refreshCanvas(canvasCtrl);
// };

// const insertNode = (canvasCtrl, action) => {
//   const { node, insertBeforeNodeId } = action;
//   if (!node) return;
//   const insertedNode = canvasCtrl.insertNode(node, insertBeforeNodeId);
//   if (_.isEmpty(insertedNode)) return refreshCanvas(canvasCtrl);
// };

// const addNodeBranch = (canvasCtrl, action) => {
//   const { node } = action;
//   if (!node) return;
//   const added = canvasCtrl.addNodeBranch(node.nodeId);
//   if (added) return refreshCanvas(canvasCtrl);
// };

// const breakChild = (canvasCtrl, action) => {
//   const { node } = action;
//   if (!node) return;
//   const breakedNode = canvasCtrl.breakChild(node);
//   if (_.isEmpty(breakedNode)) return refreshCanvas(canvasCtrl);
// };

// export default function FlowCanvasReducer(canvas, action) {
//   log.debug('action ==================== ', action);
//   const { type, canvasCtrl } = action;
//   if (!canvasCtrl) return canvas;
//   const startTime = new Date().getTime();

//   let changedCanvas = null;
//   switch (type) {
//     case 'connectingNode':
//       changedCanvas = connectNode(canvasCtrl, action);
//       break;
//     case 'redraw':
//       changedCanvas = redraw(canvasCtrl, action);
//       break;
//     case 'dropNode':
//       changedCanvas = dropNode(canvasCtrl, action);
//       break;
//     case 'deleteNode':
//       changedCanvas = deleteNode(canvasCtrl, action);
//       break;
//     case 'insertNode':
//       changedCanvas = insertNode(canvasCtrl, action);
//       break;
//     case 'addNodeBranch':
//       changedCanvas = addNodeBranch(canvasCtrl, action);
//       break;
//     case 'breakChild':
//       changedCanvas = breakChild(canvasCtrl, action);
//       break;
//     default:
//       break;
//   }

//   log.debug(`${type} cost time ${new Date().getTime() - startTime}`);
//   return { ...canvas, ...changedCanvas };
// }
