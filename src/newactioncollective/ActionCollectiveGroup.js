import React from 'react';
import FilterConnector from './FilterConnector';

function FilterGroup({ connector, onChangeConnector, filterCount, children, inner, mode }) {
  return (
    <div className="FilterGroupPanel3">
      <div className="ConnectorPanel" hidden={filterCount <= 1}>
        <div className="TopLine" />
        <div className="VLine" />
        <div className="BottomLine" />
        <div className="Connector">
          <FilterConnector mode={mode} value={connector} onChange={onChangeConnector} />
        </div>
      </div>
      <ul className={`FilterList ${inner}`}>{children}</ul>
    </div>
  );
}

export default FilterGroup;
