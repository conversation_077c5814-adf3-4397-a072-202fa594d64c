// https://stackoverflow.com/questions/37949981/call-child-method-from-parent
import React from 'react';
import './filter.scss';

const HandelGroup = props => {
  const { mode } = props;

  if (mode === 'detail') return null;

  return (
    <div className="action_collective_button_group" hidden={mode === 'detail'}>
      {/* <Button type="dashed" disabled={value.eventGroup} icon={<PlusOutlined />} onClick={() => onClick('eventGroup')}>指标</Button> */}
      {/* <Button type="dashed" disabled={value.userProperty} icon={<PlusOutlined />} onClick={() => onClick('userProperty')}>用户属性</Button>
      <Button type="dashed" disabled={value.userLabel} icon={<PlusOutlined />} onClick={() => onClick('userLabel')}>用户标签</Button>
      <Button type="dashed" disabled={value.segment} icon={<PlusOutlined />} onClick={() => onClick('segment')}>用户分群</Button> */}
    </div>
  );
};

export default HandelGroup;
