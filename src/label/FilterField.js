/* eslint-disable react-hooks/exhaustive-deps */
import React, {
  useState,
  useRef,
  useEffect,
  useCallback,
  useContext
} from 'react';
import { DownOutlined } from '@ant-design/icons';
import { Menu, Dropdown, Input, Tree, Tag, Spin, Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import FilterContext from './FilterContext';
import { t } from '../utils/translation';

const { DirectoryTree } = Tree;

const findObj = (data, key) => {
  let info = {};
  data.forEach(n => {
    if (n.key === key) {
      info = n;
    }
    n.children &&
      n.children.forEach(w => {
        if (w.key === key) {
          info = w;
        }
        w.children &&
          w.children.forEach(h => {
            if (h.key === key) {
              info = h;
            }
          });
      });
  });
  return info;
};

const getLevelList = (_list, _categoryList) => {
  const _treeData = [];
  let _expandedKeys = [];
  const categoryObj = {};
  _categoryList.forEach(n => {
    categoryObj[`key.${n.id}`] = n;
  });
  _list.forEach(n => {
    const info = categoryObj[`key.${n.categoryId}`];
    if (!info) {
      _treeData.push({ title: n.displayName, key: `${n.id}`, isLeaf: true });
    } else {
      const codeArr = `${info.path}${info.id}`
        .split(',')
        .filter(x => x !== '0')
        .map(h => `key.${h}`);
      _expandedKeys = _expandedKeys.concat(codeArr);
      if (codeArr[0] && !_treeData.find(item => item.key === codeArr[0])) {
        _treeData.push({
          title: categoryObj[codeArr[0]]?.name,
          key: codeArr[0]
        });
      }
      if (codeArr[1]) {
        const category1Info = findObj(_treeData, codeArr[0]);
        if (!category1Info.children) {
          category1Info.children = [
            { title: categoryObj[codeArr[1]]?.name, key: codeArr[1] }
          ];
        } else if (
          category1Info.children.findIndex(w => w.key === codeArr[1]) === -1
        ) {
          category1Info.children.push({
            title: categoryObj[codeArr[1]]?.name,
            key: codeArr[1]
          });
        }
      }
      if (codeArr[2]) {
        const category2Info = findObj(_treeData, codeArr[1]);
        if (!category2Info.children) {
          category2Info.children = [
            { title: categoryObj[codeArr[2]]?.name, key: codeArr[2] }
          ];
        } else if (
          category2Info.children.findIndex(w => w.key === codeArr[2]) === -1
        ) {
          category2Info.children.push({
            title: categoryObj[codeArr[2]]?.name,
            key: codeArr[2]
          });
        }
      }
      const categoryInfo = findObj(_treeData, `key.${n.categoryId}`);
      if (!categoryInfo.children) {
        categoryInfo.children = [
          { title: n.displayName, key: `${n.id}`, isLeaf: true }
        ];
      } else {
        categoryInfo.children.push({
          title: n.displayName,
          key: `${n.id}`,
          isLeaf: true
        });
      }
    }
  });
  _treeData.sort((a, b) => (a.isLeaf ? 1 : 0) - (b.isLeaf ? 1 : 0));
  return { _treeData, _expandedKeys };
};

const getObjData = _list => {
  const data = {};
  _list.forEach(n => {
    data[n.id] = n;
  });
  return data;
};

const updateTreeData = (list, key, children) => {
  return list.map(node => {
    if (node.key === key) {
      return { ...node, children };
    } else if (node.children) {
      return {
        ...node,
        children: updateTreeData(node.children, key, children)
      };
    }
    return node;
  });
};

const Item = props => {
  const { value, onChange } = props;
  const { dataProvider } = useContext(FilterContext);
  const [treeData, setTreeData] = useState([]);
  const [visible, setVisible] = useState(false);
  const [tagInfo, setTagInfo] = useState({});
  const [searchValue, setSearchValue] = useState(value.label);
  const [displayName, setDisplayName] = useState('');
  const [selectedKeys, setSelectedKeys] = useState(value.id ? [value.id] : []);
  const [objData, setObjData] = useState({});
  const [selectedTag, setSelectedTag] = useState(
    value?.displayName || t('cpnt-hPPhAHihD5WS')
  );
  const [loadedKeys, setLoadedKeys] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [flag, setFlag] = useState(true);
  const [loading, setLoading] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [isOnFocus, setIsOnFocus] = useState(false);
  const searchRef = useRef(null);

  useEffect(() => {
    setSearchValue(value.displayName);
    setSelectedKeys(value.id ? [value.id] : []);
    setSelectedTag(value?.displayName || t('cpnt-hPPhAHihD5WS'));
  }, [value]);

  useEffect(() => {
    const init = async () => {
      setLoading(true);
      if (displayName) {
        const _list = await dataProvider.getTagList({ displayName });
        const _categoryList = await dataProvider.findCategoryByProjectId();
        const result = getLevelList(_list, _categoryList);
        setLoadedKeys([]);
        setTreeData(result._treeData);
        setExpandedKeys(result._expandedKeys);
        const data = getObjData(_list);
        setObjData({ ...objData, ...data });
        setFlag(true);
      } else if (flag) {
        setFlag(false);
        const data = await getAsyncData('key.0');
        const _list = await dataProvider.getTagList({
          displayName: value.displayName
        });
        let obj = {};
        if (_list) {
          obj = _list.find(n => Number(n.id) === Number(value.id));
          value.changeProperty({ ...value, tagInfo: obj });
          onChange(value);
        }
        setLoadedKeys([]);
        setExpandedKeys([]);
        setTreeData(data);
      }
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    };
    // if (visible) {
    init();
    // }
  }, [displayName]);

  const getAsyncData = async key => {
    // setLoading(true);
    const result = await dataProvider.findAllCategory({
      categoryId: parseInt(key.split('.')[1])
    });
    const data = [];
    const obj = {};
    result.categoryList &&
      result.categoryList.forEach(n => {
        data.push({ title: n?.name, key: `key.${n?.id}` });
      });
    result.userLabels &&
      result.userLabels.forEach(n => {
        data.push({ title: n?.displayName, key: `${n?.id}`, isLeaf: true });
        obj[`${n?.id}`] = n;
      });
    setObjData({ ...objData, ...obj });
    // setLoading(false);
    return data;
  };

  const onLoadData = async ({ key, children }) => {
    if (children) {
      return;
    }
    const data = await getAsyncData(key);
    setTreeData(origin => updateTreeData(origin, key, data));
  };

  const onSelect = (data, info) => {
    if (info.node.isLeaf) {
      if (_.isEmpty(tagInfo) || Number(data[0]) !== Number(tagInfo.id)) return
      setSelectedKeys(data);
      handleVisibleChange(false, data);
      const label = objData[data[0]].name;
      const labelDisplayName = objData[data[0]].displayName;
      const fieldType = objData[data[0]].dataType;
      value.changeProperty({
        id: data[0],
        displayName: labelDisplayName,
        label,
        fieldType: fieldType || 'STRING',
        tagInfo
      });
      value.changeDateType(tagInfo.busiDate ? 'LATEST' : 'RELATIVE', true);
      onChange(value);
    }
  };
  const env = localStorage.getItem('env');
  const onMouseEnter = async event => {
    if (event.node.isLeaf) {
      const _tagInfp = objData[event.node.key];
      if (_tagInfp) {
        const _items = await dataProvider.getTagValuesById(_tagInfp.id);
        let rule = '';
        if (env === 'NS' && dataProvider?.getEffectRule) {
          rule = await dataProvider.getEffectRule({
            ruleType: 0,
            labelId: _tagInfp.id
          });
        }
        const param = { ..._tagInfp, userLabelValues: _items, rule };
        setTagInfo(param);
      }
    }
  };

  // const renderTreeNodes = data => data.map(item => {
  //   if (item.children) {
  //     return (
  //       <TreeNode title={item.title} key={item.key} dataRef={item}>
  //         {renderTreeNodes(item.children)}
  //       </TreeNode>
  //     );
  //   }
  //   // eslint-disable-next-line react/jsx-props-no-spreading
  //   return <TreeNode key={item.key} {...item} dataRef={item} />;
  // });
  const handleVisibleChange = (flag1, data) => {
    if (!flag1) {
      const lastData = (data && data[0]) || selectedKeys[0] || '';
      const _value =
        (lastData && objData[lastData]?.displayName) || value.displayName;
      setSearchValue(_value);
      setDisplayName('');
      _value && setSelectedTag(_value);
      searchRef.current.blur();
    }
    setVisible(flag1);
  };

  const debouncedSave = useCallback(
    _.debounce(nextValue => setDisplayName(nextValue), 1000),
    []
  );

  const onSearch = function(e) {
    const _value = e.target.value;
    setSearchValue(_value);
    debouncedSave(_value);
  };

  const renderTag = info => {
    let showTagValue =
      info.value.length <= 10
        ? info.priorityShow === 2 && info.displayValue
          ? info.valueAndDisplayValue
          : info.value
        : info.priorityShow === 2 && info.displayValue
        ? `${info.valueAndDisplayValue}`
        : `${info.value}`;
    if (tagInfo.dataType === 'TIMESTAMP') {
      showTagValue = dayjs(parseInt(showTagValue)).format(
        'YYYY-MM-DD HH:mm:ss'
      );
    }
    const renderValue = type => {
      if (type === 'title') {
        return showTagValue;
      } else {
        return showTagValue.length > 10
          ? `${showTagValue.substring(0, 10)}...`
          : showTagValue;
      }
    };
    return (
      <Tooltip key={info.id} title={renderValue('title')}>
        <Tag style={{ marginBottom: 4 }}>{renderValue()}</Tag>
      </Tooltip>
    );
  };

  const menu = (
    <Menu>
      <div
        className="cpnt-label"
        style={{
          display: 'flex',
          padding: '10px 0px',
          width: 640,
          minHeight: 320
        }}
      >
        <div
          style={{
            width: 320,
            borderRight: '1px solid #E1E1E1',
            maxHeight: 320,
            overflow: 'auto'
          }}
          className="labelTree"
        >
          {/* {!loading ? <DirectoryTree
            className="fieldDirectoryTree"
            showIcon
            onSelect={onSelect}
            onMouseEnter={onMouseEnter}
            selectedKeys={selectedKeys}
            loadedKeys={loadedKeys}
            onLoad={data => setLoadedKeys(data)}
            loadData={onLoadData}
            expandedKeys={expandedKeys}
            onExpand={setExpandedKeys}
            treeData={treeData}
          >
          </DirectoryTree> : <Spin />} */}
          <Spin spinning={loading}>
            <DirectoryTree
              className="fieldDirectoryTree"
              showIcon
              onSelect={onSelect}
              onMouseEnter={onMouseEnter}
              selectedKeys={selectedKeys}
              loadedKeys={loadedKeys}
              onLoad={data => setLoadedKeys(data)}
              loadData={onLoadData}
              expandedKeys={expandedKeys}
              onExpand={setExpandedKeys}
              treeData={treeData}
              height={300}
            />
          </Spin>
        </div>
        <div
          style={{
            width: 320,
            maxHeight: 320,
            overflowY: 'auto',
            padding: '3px 10px'
          }}
        >
          <div
            style={{
              fontFamily: 'PingFangSC-Regular',
              fontSize: 16,
              fontWeight: 600,
              color: '#000000'
            }}
          >
            {tagInfo.displayName}
          </div>
          <div
            style={{
              fontFamily: 'PingFangSC-Regular',
              fontSize: 14,
              color: 'rgba(0, 0, 0, 0.65)',
              marginTop: 8
            }}
          >
            {tagInfo.name}
          </div>
          <div
            style={{
              fontFamily: 'PingFangSC-Regular',
              fontSize: 14,
              color: 'rgba(0, 0, 0, 0.65)',
              marginTop: 8
            }}
          >
            {tagInfo.dataType}
          </div>

          <div hidden={_.isEmpty(tagInfo.rule)}>
            {t('cpnt-vSRNIHV9oZjf')}
            <pre style={{ color: 'rgba(0, 0, 0, 0.65)' }}>{tagInfo.rule}</pre>
          </div>

          <div
            className="cpnt-label-remark"
            style={{
              opacity: 0.5,
              fontFamily: 'PingFangSC-Regular',
              fontSize: 14,
              color: 'rgba(0,0,0,0.65)',
              margin: '0 auto',
              paddingTop: 16
            }}
            title={tagInfo.remark}
          >
            {tagInfo.remark}
          </div>
          <div
            style={{
              marginTop: 16,
              fontFamily: 'PingFangSC-Regular',
              fontSize: 14,
              color: 'rgba(0,0,0,0.65)'
            }}
          >
            {t('cpnt-fAblunbexjbD')}
          </div>
          <div>
            {tagInfo.userLabelValues &&
              tagInfo.userLabelValues
                .filter((w, i) => i <= 9)
                .map(n => renderTag(n))}
            {tagInfo.userLabelValues &&
              tagInfo.userLabelValues.length > 10 &&
              '...'}
          </div>
          {/* <div style={{ marginTop: 28, fontFamily: 'PingFangSC-Regular', fontSize: 14, color: 'rgba(0,0,0,0.65)' }}>最近计算时间：{tagInfo.lastCalcTime ? dayjs(tagInfo.lastCalcTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</div>         */}
          {/* <div style={{ marginTop: 28, fontFamily: 'PingFangSC-Regular', fontSize: 14, color: 'rgba(0,0,0,0.65)' }}>创建人：{tagInfo.updateUserName}</div>
          <div style={{ marginTop: 20, fontFamily: 'PingFangSC-Regular', fontSize: 12, color: 'rgba(0,0,0,0.65)' }}>创建时间：{dayjs(tagInfo.createTime).format('YYYY-MM-DD HH:mm:ss')}</div> */}
        </div>
      </div>
    </Menu>
  );

  const onFocus = () => {
    setSearchValue('');
    setDisplayName('');
    setIsOnFocus(true);
  };

  const onBlur = () => {
    setIsOnFocus(false);
  };

  return (
    <div>
      <Dropdown
        trigger={['click']}
        onOpenChange={handleVisibleChange}
        getPopupContainer={triggerNode => triggerNode.parentNode}
        open={visible}
        overlay={menu}
      >
        {/* <Search
          ref={searchRef}
          onChange={onSearch}
          onFocus={() => { setSearchValue(''); setDisplayName(''); }}
          value={searchValue}
          placeholder={selectedTag}
        /> */}
        <div className="clickWrapper">
          <Input
            onKeyDown={loading ? e => e.preventDefault() : null}
            className="ant-dropdown-link"
            ref={searchRef}
            placeholder={selectedTag}
            // suffix={<DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />}
            // suffix={isOnFocus ? <SearchOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} /> : <DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />}
            style={{ paddingRight: '25px' }}
            onChange={onSearch}
            onFocus={onFocus}
            onBlur={onBlur}
            value={searchValue}
          />
          <DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />
        </div>
        {/* <TreeNode visible={false} /> */}
      </Dropdown>
    </div>
  );
};

export default Item
