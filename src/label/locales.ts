export default {
  zh_CN: {
    'cpnt-LPoRKsnqFykj': '标签',
    'cpnt-60CponsmBOMv': '最多添加{{maxFilterCount}}条',
    'cpnt-frZeanQ0tQbu': '月',
    'cpnt-VigOoa0mSlAz': '日',
    'cpnt-iRQaaNMBlOHi': '请输入日期',
    'cpnt-Ej8Ej8Ej8Ej8': '开始时间',
    'cpnt-Fj9Fj9Fj9Fj9': '结束时间',
    'cpnt-Gk0Gk0Gk0Gk0': '最小值',
    'cpnt-Hl1Hl1Hl1Hl1': '最大值',
    'cpnt-Im2Im2Im2Im2': '至',
    'cpnt-Jn3Jn3Jn3Jn3': '月份',
    'cpnt-Ko4Ko4Ko4Ko4': '日期',
    'cpnt-Lp5Lp5Lp5Lp5': '勾选后，输入内容使用逗号(中英文)、分号(中英文)、空格、顿号作为分隔符拆分多值',
    'cpnt-Mq6Mq6Mq6Mq6': '使用分隔符输入',
    'cpnt-Nr7Nr7Nr7Nr7': '值',
    'cpnt-Os8Os8Os8Os8': '请选择过滤属性',
    'cpnt-Pt9Pt9Pt9Pt9': '选择的过滤属性类型不存在',
    'cpnt-Qu0Qu0Qu0Qu0': '请输入操作符',
    'cpnt-Rv1Rv1Rv1Rv1': '请填写完整',
    'cpnt-Sw2Sw2Sw2Sw2': '请选择标签值',
    'cpnt-Tx3Tx3Tx3Tx3': '请输入',
    'cpnt-Uy4Uy4Uy4Uy4': '标签值前后不能有空格',
    'cpnt-Vz5Vz5Vz5Vz5': '请选择时间类型',
    'cpnt-Wa6Wa6Wa6Wa6': '请选择更新时间',
    'cpnt-Xb7Xb7Xb7Xb7': '天前',
    'cpnt-X5DwSgW1kKcG': '个',
    'cpnt-TSXhMl6CZoQt': '月末',
    'cpnt-Fvh01H0MnvEu': '年末',
    'cpnt-XEHan4IFYXt8': '标签更新时间',
    'cpnt-sdb44cpp4nyg': '相对值：将使用相对的时间作为标签业务数据',
    'cpnt-VgEX50jPXZ3q': '固定值：将使用固定的时间来作为标签业务数据',
    'cpnt-kZMCt2bUmx2F': '最新值：将最近一次计算的时间来作为标签业务数据，可选择排除某日历的日历标识，最新值会根据排除的日历标识，自动选取最近的一个计算的日期。',
    'cpnt-eyqAN05wffCN': '标签最近计算时间：',
    'cpnt-Yc8Yc8Yc8Yc8': '仅计算当前标签规则的覆盖人数，不与其他节点做聚合计算',
    'cpnt-Zd9Zd9Zd9Zd9': '标签计算',
    'cpnt-hPPhAHihD5WS': '请选择',
    'cpnt-vSRNIHV9oZjf': '标签业务规则:',
    'cpnt-fAblunbexjbD': '标签值',
    'cpnt-3bqV0nXTKK1N': '操作符',
    'cpnt-4x0hFK9xWk4h': '标签排除日历组件 需要传递dataProvider.getCalendar',
    'cpnt-dtw2EFaxOr8p': '排除日历：',
    'cpnt-brQR7Wx4kGcp': '排除',
    'cpnt-adcAmFggMphK': '营销日历/日历标识',
    'cpnt-ucbwNp99vnNl': '小时',
    'cpnt-wy5t7DM3AaX9': '天',
    'cpnt-1lkXXg0QXa8O': '标签计算结果校验',
    'cpnt-BleZvtjTgFtm': '勾选时，自画布每个批次预计开始执行时间起，按照配置时间内获取标签最新数据(根据标签数据的就绪时间判定)，若无最新数据，则不会有人进入该分支，或者结合画布等待依赖数据就绪逻辑使用。',
    'cpnt-dsaoMJWvU0Fa': '画布使用在',
    'cpnt-I5yH1HDjcm5c': '内标签最新数据',
    'cpnt-BYL1zW7U4cjF': '请选择天数',
    'cpnt-oMsEHqGLA7n5': '选择相对时间',
    'cpnt-N60AcdeHiJ9O': '确认',
    'cpnt-qVBp6bvhYixP': '取消',
    'cpnt-CCrMFznNlbHz': '自定义',
    'cpnt-pc01aX36KJLK': '自定义过去N天',
    'cpnt-MQ7jqnefRp0Z': '主组件',
    'cpnt-McMLyVcjhBLZ': '主方法',
    'cpnt-ZiGm4LsQZVr3': 'N天前',
    'cpnt-u0unyA1G5iQX': '上N个月末',
    'cpnt-fkEIZIm360jk': '上N年末',
    'cpnt-YT79YwIDFduJ': '等于',
    'cpnt-nU4aZ4JqVaVf': '不等于',
    'cpnt-ngjBMGtAHDlj': '大于',
    'cpnt-OhiZjcbBojNm': '大于等于',
    'cpnt-PMjzbI8StsBA': '小于',
    'cpnt-IQinnVAiid91': '小于等于',
    'cpnt-kmYGh1HP127y': '范围',
    'cpnt-WG8jJizaMx8L': '高级范围',
    'cpnt-8MpxPWje4d6K': '包含',
    'cpnt-zO6q3HoZlZo5': '不包含',
    'cpnt-ey4rGfktgrjG': '有值',
    'cpnt-7W8CKFwgwhsm': '空值',
    'cpnt-Duh5GXogrmQs': '匹配',
    'cpnt-ud1CW7imBSbQ': '不匹配',
    'cpnt-qUIQEEpW9ht7': '开头匹配',
    'cpnt-MVypvSjwOkmv': '开头不匹配',
    'cpnt-jbq5yrsElHBM': '结尾匹配',
    'cpnt-bi5aoBRw4wF7': '结尾不匹配',
    'cpnt-iAFlCRIy5y7e': '是',
    'cpnt-NopWbLBN3P4u': '否',
    'cpnt-fIjxjem1L4mq': '时间切片',
    'cpnt-q9oMtYCVEzqG': '时间切片-月范围',
    'cpnt-mitSvdjkamZ1': '时间切片-日范围',
    'cpnt-f71g0mYiulVT': '时间切片-月日范围',
    'cpnt-t6tuX5M5ld1u': '时间切片-月等于',
    'cpnt-pRPbhw0XOZ6m': '时间切片-日等于',
    'cpnt-Y1uYp8DGzAHv': '时间切片-月日等于',
    'cpnt-SbFnPeuR2XhO': '且',
    'cpnt-M1LLytB7EIQe': '或',
    'cpnt-q7H4sbrLNxhV': '请输入',
    'cpnt-ETeQNvitoqpB': '最大输入500个字符',
    'cpnt-0eH3UaKZw1hO': '最大长度11个字符',
    'cpnt-PYBJf2GamIhl': '请输入数字',
    'cpnt-HN4doLXjWPte': '最大长度20个字符',
    'cpnt-hxxUmlZT63AO': '请输入浮点数字',
    'cpnt-ZcEqR4qZGHS5': '请输入日期时间',
    'cpnt-yjwljEMeuR6A': '请输入日期',
    'cpnt-6R4R0diJOaw3': '最新值',
    'cpnt-OeVAhIxqQgyh': '固定时间',
    'cpnt-W3VyCzcOQdg2': '相对时间',
    'cpnt-ozp2CfKL5ASV': '今天',
    'cpnt-Q3ZMpXjO6uUH': '昨天',
    'cpnt-4xxrJfDsQOJo': '前天',
    'cpnt-I2NJNgzs9zPH': '上月末',
    'cpnt-BMQmD5Jh4hcK': '上2个月末',
    'cpnt-30T2zWoI3t8n': '上3个月末',
    'cpnt-dTbE8sqljTsu': '上年末',
    'cpnt-xRt9YruYMFBh': '上2年末',
    'cpnt-NCdhG6M23yR7': '上3年末'
  },
  en_US: {
    'cpnt-LPoRKsnqFykj': 'Label',
    'cpnt-60CponsmBOMv': 'Add up to {{maxFilterCount}} filters',
    'cpnt-frZeanQ0tQbu': 'Month',
    'cpnt-VigOoa0mSlAz': 'Day',
    'cpnt-iRQaaNMBlOHi': 'Please enter date',
    'cpnt-Ej8Ej8Ej8Ej8': 'Start Time',
    'cpnt-Fj9Fj9Fj9Fj9': 'End Time',
    'cpnt-Gk0Gk0Gk0Gk0': 'Min Value',
    'cpnt-Hl1Hl1Hl1Hl1': 'Max Value',
    'cpnt-Im2Im2Im2Im2': 'To',
    'cpnt-Jn3Jn3Jn3Jn3': 'Month',
    'cpnt-Ko4Ko4Ko4Ko4': 'Date',
    'cpnt-Lp5Lp5Lp5Lp5': 'When checked, input content uses commas (Chinese and English), semicolons (Chinese and English), spaces, and pause marks as delimiters to split multiple values',
    'cpnt-Mq6Mq6Mq6Mq6': 'Use delimiter input',
    'cpnt-Nr7Nr7Nr7Nr7': 'Value',
    'cpnt-Os8Os8Os8Os8': 'Please select filter property',
    'cpnt-Pt9Pt9Pt9Pt9': 'Selected filter property type does not exist',
    'cpnt-Qu0Qu0Qu0Qu0': 'Please enter operator',
    'cpnt-Rv1Rv1Rv1Rv1': 'Please fill in completely',
    'cpnt-Sw2Sw2Sw2Sw2': 'Please select label value',
    'cpnt-Tx3Tx3Tx3Tx3': 'Please enter',
    'cpnt-Uy4Uy4Uy4Uy4': 'Label value cannot have spaces before or after',
    'cpnt-Vz5Vz5Vz5Vz5': 'Please select time type',
    'cpnt-Wa6Wa6Wa6Wa6': 'Please select update time',
    'cpnt-Xb7Xb7Xb7Xb7': 'days ago',
    'cpnt-X5DwSgW1kKcG': '',
    'cpnt-TSXhMl6CZoQt': 'month end',
    'cpnt-Fvh01H0MnvEu': 'year end',
    'cpnt-XEHan4IFYXt8': 'Label Update Time',
    'cpnt-sdb44cpp4nyg': 'Relative value: will use relative time as label business data',
    'cpnt-VgEX50jPXZ3q': 'Fixed value: will use fixed time as label business data',
    'cpnt-kZMCt2bUmx2F': 'Latest value: will use the most recent calculation time as label business data, can choose to exclude certain calendar identifiers, latest value will automatically select the most recent calculation date based on excluded calendar identifiers.',
    'cpnt-eyqAN05wffCN': 'Label Latest Calculation Time:',
    'cpnt-Yc8Yc8Yc8Yc8': 'Only calculate the coverage population of current label rules, not aggregated with other nodes',
    'cpnt-Zd9Zd9Zd9Zd9': 'Label Calculation',
    'cpnt-hPPhAHihD5WS': 'Please select',
    'cpnt-vSRNIHV9oZjf': 'Label Business Rules:',
    'cpnt-fAblunbexjbD': 'Label Values',
    'cpnt-3bqV0nXTKK1N': 'Operator',
    'cpnt-4x0hFK9xWk4h': 'Label exclude calendar component needs to pass dataProvider.getCalendar',
    'cpnt-dtw2EFaxOr8p': 'Exclude Calendar:',
    'cpnt-brQR7Wx4kGcp': 'Exclude',
    'cpnt-adcAmFggMphK': 'Marketing Calendar/Calendar ID',
    'cpnt-ucbwNp99vnNl': 'Hour',
    'cpnt-wy5t7DM3AaX9': 'Day',
    'cpnt-1lkXXg0QXa8O': 'Label Calculation Result Validation',
    'cpnt-BleZvtjTgFtm': 'When checked, from the estimated start execution time of each batch in the canvas, obtain the latest label data within the configured time (determined by the readiness time of label data). If there is no latest data, no one will enter this branch, or use it in combination with the canvas waiting for dependent data readiness logic.',
    'cpnt-dsaoMJWvU0Fa': 'Canvas use within',
    'cpnt-I5yH1HDjcm5c': 'latest label data',
    'cpnt-BYL1zW7U4cjF': 'Please select days',
    'cpnt-oMsEHqGLA7n5': 'Select Relative Time',
    'cpnt-N60AcdeHiJ9O': 'Confirm',
    'cpnt-qVBp6bvhYixP': 'Cancel',
    'cpnt-CCrMFznNlbHz': 'Custom',
    'cpnt-pc01aX36KJLK': 'Custom past N days',
    'cpnt-MQ7jqnefRp0Z': 'Main Component',
    'cpnt-McMLyVcjhBLZ': 'Main Method',
      'cpnt-ZiGm4LsQZVr3': 'N days ago',
    'cpnt-u0unyA1G5iQX': 'Last N month end',
    'cpnt-fkEIZIm360jk': 'Last N year end',
    'cpnt-YT79YwIDFduJ': 'Equal',
    'cpnt-nU4aZ4JqVaVf': 'Not Equal',
    'cpnt-ngjBMGtAHDlj': 'Greater Than',
    'cpnt-OhiZjcbBojNm': 'Greater Than or Equal',
    'cpnt-PMjzbI8StsBA': 'Less Than',
    'cpnt-IQinnVAiid91': 'Less Than or Equal',
    'cpnt-kmYGh1HP127y': 'Range',
    'cpnt-WG8jJizaMx8L': 'Advanced Range',
    'cpnt-8MpxPWje4d6K': 'Contains',
    'cpnt-zO6q3HoZlZo5': 'Does Not Contain',
    'cpnt-ey4rGfktgrjG': 'Has Value',
    'cpnt-7W8CKFwgwhsm': 'Null Value',
    'cpnt-Duh5GXogrmQs': 'Match',
    'cpnt-ud1CW7imBSbQ': 'Does Not Match',
    'cpnt-qUIQEEpW9ht7': 'Starts With',
    'cpnt-MVypvSjwOkmv': 'Does Not Start With',
    'cpnt-jbq5yrsElHBM': 'Ends With',
    'cpnt-bi5aoBRw4wF7': 'Does Not End With',
    'cpnt-iAFlCRIy5y7e': 'Yes',
    'cpnt-NopWbLBN3P4u': 'No',
    'cpnt-fIjxjem1L4mq': 'Time Slice',
    'cpnt-q9oMtYCVEzqG': 'Time Slice - Month Range',
    'cpnt-mitSvdjkamZ1': 'Time Slice - Day Range',
    'cpnt-f71g0mYiulVT': 'Time Slice - Month Day Range',
    'cpnt-t6tuX5M5ld1u': 'Time Slice - Month Equal',
    'cpnt-pRPbhw0XOZ6m': 'Time Slice - Day Equal',
    'cpnt-Y1uYp8DGzAHv': 'Time Slice - Month Day Equal',
    'cpnt-SbFnPeuR2XhO': 'AND',
    'cpnt-M1LLytB7EIQe': 'OR',
    'cpnt-q7H4sbrLNxhV': 'Please enter',
    'cpnt-ETeQNvitoqpB': 'Maximum input 500 characters',
    'cpnt-0eH3UaKZw1hO': 'Maximum length 11 characters',
    'cpnt-PYBJf2GamIhl': 'Please enter a number',
    'cpnt-HN4doLXjWPte': 'Maximum length 20 characters',
    'cpnt-hxxUmlZT63AO': 'Please enter a floating point number',
    'cpnt-ZcEqR4qZGHS5': 'Please enter date time',
    'cpnt-yjwljEMeuR6A': 'Please enter date',
    'cpnt-6R4R0diJOaw3': 'Latest Value',
    'cpnt-OeVAhIxqQgyh': 'Fixed Time',
    'cpnt-W3VyCzcOQdg2': 'Relative Time',
    'cpnt-ozp2CfKL5ASV': 'Today',
    'cpnt-Q3ZMpXjO6uUH': 'Yesterday',
    'cpnt-4xxrJfDsQOJo': 'Day Before Yesterday',
    'cpnt-I2NJNgzs9zPH': 'Last Month End',
    'cpnt-BMQmD5Jh4hcK': 'Last 2 Month End',
    'cpnt-30T2zWoI3t8n': 'Last 3 Month End',
    'cpnt-dTbE8sqljTsu': 'Last Year End',
    'cpnt-xRt9YruYMFBh': 'Last 2 Year End',
    'cpnt-NCdhG6M23yR7': 'Last 3 Year End'
  }
};
