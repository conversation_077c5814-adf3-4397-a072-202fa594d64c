/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, Fragment, useContext } from 'react';
import {
  DatePicker,
  Input,
  Modal,
  InputNumber,
  message,
  Button,
  Select
} from 'antd';
import _ from 'lodash';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import locale from 'antd/es/date-picker/locale/zh_CN';
import FilterConfig from './FilterConfig';
import './label.scss';
import FilterContext from './FilterContext';
import { t } from '../utils/translation';

dayjs.locale('zh-cn');

const { relativeTimeObj, timeSelctor, relativeTimeObjNew } = FilterConfig;
const { Option } = Select;
function DateInput(props) {
  let { times, onChange } = props;

  const onValueChange = m => {
    onChange(m && m.startOf('day').valueOf());
  };

  return (
    <DatePicker
      placeholder={t('cpnt-iRQaaNMBlOHi')}
      // showTime={showTime}
      // format={format}
      locale={locale}
      style={{ width: 200 }}
      disabledDate={currentDate => dayjs() < currentDate}
      allowClear
      value={times && dayjs(times)}
      getCalendarContainer={triggerNode => triggerNode.parentNode}
      onChange={onValueChange}
    />
  );
}

const RelativeTimeNew = ({ times, onChange, timeType }) => {
  const env = localStorage.getItem('env');
  const [visible, setVisible] = useState(false);
  const [maxNumber, setMaxNumber] = useState(undefined);
  const [content, setContent] = useState(
    times === null || (env === 'NS' && times === 0)
      ? ''
      : relativeTimeObjNew[`${timeType}_${times}`]
      ? `${relativeTimeObjNew[`${timeType}_${times}`]} `
      : `${timeType !== 'DAY' ? '上' : ''}${times} ${
          timeType === 'DAY' ? t('cpnt-Xb7Xb7Xb7Xb7') : timeType === 'MONTH' ? t('cpnt-TSXhMl6CZoQt') : t('cpnt-Fvh01H0MnvEu')
        }`
  );
  const groupBy = arr => {
    const groupedData = [];
    const groupByKeyword = (items, keywords) => {
      const groups = {};
      items.forEach(item => {
        keywords.forEach(keyword => {
          if (item[0].startsWith(keyword)) {
            if (!groups[keyword]) {
              groups[keyword] = [];
            }
            groups[keyword].push(item);
          }
        });
      });
      return groups;
    };
    const keywords = ['DAY', 'MONTH', 'YEAR'];
    const groups = groupByKeyword(arr, keywords);
    Object.keys(groups).forEach(keyword => {
      groupedData.push(groups[keyword]);
    });
    return groupedData;
  };
  const [value, setValue] = useState({
    number: env === 'NS' && times === 0 ? 1 : times,
    timeType: timeType || 'DAY'
  });
  useEffect(() => {
    const getMax = () => {
      if (env === 'NS') {
        if (value.times === 0 && value.timeType === 'DAY') {
          setValue({ ...value, number: 1 });
        }
        if (value.timeType === 'DAY') {
          return 8;
        } else if (value.timeType === 'MONTH') {
          return 12;
        } else {
          return 3;
        }
      }
      return undefined;
    };
    setMaxNumber(getMax());
  }, [value.timeType]);
  const handleOk = () => {
    if (value === undefined || value === null) {
      message.error(t('cpnt-BYL1zW7U4cjF'));
      return;
    }
    const dateEnd = dayjs()
      .subtract(value.number, value.timeType)
      .endOf(value.timeType)
      .format('YYYY-MM-DD');
    const dateStart = dayjs().format('YYYY-MM-DD');
    const diffDay = dayjs(dateStart).diff(dayjs(dateEnd), 'day');
    const pastDate = dayjs()
      .subtract(diffDay, 'day')
      .format('YYYY-MM-DD');

    // setContent(`${relativeTimeObjNew[value]} ${pastDate}` || `${value} 天前`);
    setContent(
      relativeTimeObjNew[`${value.timeType}_${value.number}`]
        ? `${
            relativeTimeObjNew[`${value.timeType}_${value.number}`]
          } ${pastDate}`
        : `${value.timeType !== 'DAY' ? '上' : ''}${value.number} ${
            value.timeType === 'DAY'
              ? t('cpnt-Xb7Xb7Xb7Xb7')
              : value.timeType === 'MONTH'
              ? t('cpnt-TSXhMl6CZoQt')
              : t('cpnt-Fvh01H0MnvEu')
          } ${pastDate}`
    );
    onChange(value);
    setVisible(false);
  };
  const changeNumber = e => {
    if (env === 'NS') {
      if (maxNumber < e) {
        setValue({ ...value, number: maxNumber });
      } else if (e < 1) {
        setValue({ ...value, number: 1 });
      } else {
        setValue({ ...value, number: e });
      }
    } else {
      setValue({ ...value, number: e });
    }
  };
  return (
    <>
      <Input
        value={content}
        placeholder={t('cpnt-BYL1zW7U4cjF')}
        readOnly
        onClick={() => setVisible(true)}
      />
      <Modal
        title={t('cpnt-oMsEHqGLA7n5')}
        open={visible}
        onOk={handleOk}
        okText={t('cpnt-N60AcdeHiJ9O')}
        cancelText={t('cpnt-qVBp6bvhYixP')}
        onCancel={() => setVisible(false)}
        className="relative-time-modal"
      >
        <div className="relative-time-btn">
          {groupBy(
            Object.entries(
              env === 'NS'
                ? _.omit(relativeTimeObjNew, 'DAY_0')
                : relativeTimeObjNew
            )
          ).map(item => {
            return (
              <div className="relative-time-item">
                {item.map(n => (
                  <Button
                    onClick={() => setValue(extractNumberFromStr(n[0]))}
                    type={
                      n[0] === `${value.timeType}_${value.number}`
                        ? 'primary'
                        : 'default'
                    }
                    style={{ marginRight: 10 }}
                    key={n[0]}
                  >
                    {n[1]}
                  </Button>
                ))}
              </div>
            );
          })}
        </div>
        <div className="relative-time-input">
          {t('cpnt-CCrMFznNlbHz')}
          <Select
            value={value.timeType}
            onChange={e => {
              if (env === 'NS') {
                switch (e) {
                  case 'DAY':
                    setValue({
                      number: value.number > 8 ? 8 : value.number,
                      timeType: e
                    });
                    break;
                  case 'MONTH':
                    setValue({
                      number: value.number > 12 ? 12 : value.number,
                      timeType: e
                    });
                    break;
                  case 'YEAR':
                    setValue({
                      number: value.number > 3 ? 3 : value.number,
                      timeType: e
                    });
                    break;
                  default:
                    setValue({ ...value, timeType: e });
                    break;
                }
              } else {
                setValue({
                  ...value,
                  timeType: e,
                  number: value.number === 0 && e !== 'DAY' ? 1 : value.number
                });
              }
            }}
            style={{ marginLeft: 8, minWidth: 100 }}
          >
            {timeSelctor.map(n => (
              <Option key={n.key}>{n.value}</Option>
            ))}
          </Select>
          <InputNumber
            max={maxNumber}
            min={env === 'NS' ? 1 : value.timeType !== 'DAY' ? 1 : 0}
            precision={0}
            value={value.number}
            onChange={changeNumber}
            style={{ borderRadius: '6px', marginLeft: 8 }}
          />
        </div>
      </Modal>
    </>
  );
};
const RelativeTime = ({ times, onChange }) => {
  const [visible, setVisible] = useState(false);
  const [content, setContent] = useState(
    times === null ? '' : relativeTimeObj[times] || `${times} ${t('cpnt-Xb7Xb7Xb7Xb7')}`
  );
  const [value, setValue] = useState(times);

  const handleOk = () => {
    if (value === undefined || value === null) {
      message.error(t('cpnt-BYL1zW7U4cjF'));
      return;
    }

    const pastDate = dayjs()
      .subtract(value, 'day')
      .format('YYYY-MM-DD');

    // setContent(`${relativeTimeObj[value]} ${pastDate}` || `${value} 天前`);
    setContent(
      relativeTimeObj[value]
        ? `${relativeTimeObj[value]} ${pastDate}`
        : `${value} ${t('cpnt-Xb7Xb7Xb7Xb7')} ${pastDate}`
    );
    onChange(value);
    setVisible(false);
  };

  return (
    <>
      <Input
        value={content}
        placeholder={t('cpnt-BYL1zW7U4cjF')}
        readOnly
        onClick={() => setVisible(true)}
      />
      <Modal
        title={t('cpnt-oMsEHqGLA7n5')}
        open={visible}
        onOk={handleOk}
        okText={t('cpnt-N60AcdeHiJ9O')}
        cancelText={t('cpnt-qVBp6bvhYixP')}
        onCancel={() => setVisible(false)}
      >
        <div style={{ marginBottom: 20 }}>
          {Object.entries(relativeTimeObj).map(n => (
            <Button
              onClick={() => setValue(parseInt(n[0]))}
              type={parseInt(n[0]) === value ? 'primary' : 'default'}
              style={{ marginRight: 10 }}
              key={n[0]}
            >
              {n[1]}
            </Button>
          ))}
        </div>
        <div>
          {t('cpnt-pc01aX36KJLK')}{' '}
          <InputNumber
            min={0}
            precision={0}
            value={value}
            onChange={setValue}
          />
        </div>
      </Modal>
    </>
  );
};
/**
 * *****************************************
 * 主组件
 * *****************************************
 */
export default function FilterValue({ value, onChange }) {
  const { times, dateType, timeType } = value;
  const [, setFieldValue] = useState(value.value);
  const { isUserGroup, isCampaignV2 } = useContext(FilterContext);
  useEffect(() => {
    setFieldValue(value.value);
  }, [value.times]);

  const onChangeFieldValue = v => {
    value.times = v;
    onChange(v);
    setFieldValue(v);
  };

  /**
   * 主方法
   * 返回渲染组件
   */
  const filterValue = () => {
    return <DateInput times={times} onChange={onChangeFieldValue} />;
  };

  const RelativeTimeComponent = () => {
    return isUserGroup || isCampaignV2 ? (
      <RelativeTimeNew
        times={times}
        timeType={timeType}
        onChange={onChangeFieldValue}
      />
    ) : (
      <RelativeTime times={times} onChange={onChangeFieldValue} />
    );
  };

  return (
    <div>
      {dateType === '' ? (
        <Input placeholder={t('cpnt-Nr7Nr7Nr7Nr7')} disabled />
      ) : dateType === 'RELATIVE' ? (
        RelativeTimeComponent()
      ) : (
        filterValue()
      )}
    </div>
  );
}

function extractNumberFromStr(str) {
  const parts = _.split(str, '_');
  const number = _.parseInt(parts[1]);
  const timeType = parts[0];
  return { number, timeType };
}
