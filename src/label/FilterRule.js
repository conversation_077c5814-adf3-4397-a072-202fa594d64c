import React, { useContext } from 'react';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Tooltip, Checkbox, InputNumber, Select } from 'antd';
import { getRoundedHour } from '../utils/timeUtils';
import FilterContext from './FilterContext';
import { t } from '../utils/translation';

const { Option } = Select;

const FilterRule = ({ value, onChange }) => {
  const { mode, campaignInfo } = useContext(FilterContext);
  const { dateType } = value;

  const selectAfter = (
    <Select
      disabled={mode === 'detail'}
      style={{ width: 70 }}
      value={value?.timeTerm || 'HOUR'}
      onChange={e => {
        value.changeRule('timeTerm', e);
        onChange(value);
      }}
    >
      <Option value="HOUR">{t('cpnt-ucbwNp99vnNl')}</Option>
      <Option value="DAY">{t('cpnt-wy5t7DM3AaX9')}</Option>
    </Select>
  );

  if (dateType === 'LATEST') {
    return (
      <div style={{ paddingTop: 8 }}>
        <div>
          <Checkbox
            disabled={mode === 'detail'}
            checked={value?.checkUserTag || false}
            onChange={e => {
              value.changeRule('checkUserTag', e.target.checked);
              if (e) {
                value.changeRule('checkDuration', getRoundedHour(campaignInfo));
                value.changeRule('timeTerm', 'HOUR');
              }
              onChange(value);
            }}
          >
            {t('cpnt-1lkXXg0QXa8O')}
          </Checkbox>
          <Tooltip
            placement="left"
            overlayStyle={{ maxWidth: 350 }}
            title={t('cpnt-BleZvtjTgFtm')}
          >
            <InfoCircleOutlined
              style={{ marginLeft: '-4px', color: 'rgba(0,0,0,0.45)' }}
            />
          </Tooltip>
        </div>
        <div>
          <div
            style={{
              display: 'flex',
              gap: 8,
              background: '#fafafa',
              padding: '0 12px'
            }}
            hidden={!value?.checkUserTag}
          >
            <span>{t('cpnt-dsaoMJWvU0Fa')}</span>
            <InputNumber
              disabled={mode === 'detail'}
              value={value?.checkDuration || getRoundedHour(campaignInfo)}
              defaultValue={1}
              style={{ width: 170 }}
              min={1}
              addonAfter={selectAfter}
              onChange={e => {
                value.changeRule('checkDuration', e);
                onChange(value);
              }}
            />
            <span>{t('cpnt-I5yH1HDjcm5c')}</span>
          </div>
        </div>
      </div>
    );
  } else {
    return null;
  }
};

export default FilterRule;
