import React, { useState, useContext, useEffect, useRef } from 'react';
import {
  CloseCircleOutlined,
  PlusCircleOutlined,
  QuestionCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { Tooltip, Input } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import FilterSingleWrapper from './FilterSingleWrapper';
import FilterField from './FilterField';
import FilterOperator from './FilterOperator';
import FilterValue from './FilterValue';
import FilterDateType from './FilterTimeType';
import FilterUpdateTime from './FilterUpdateTime';
import FilterContext from './FilterContext';
import FilterRule from './FilterRule';
import CalendarComponent from './calendarComponent';
import { t } from '../utils/translation';

/**
 * 封装单个过滤组件
 * 当前组件失去焦点时校验数据
 * 当Field、Operator、Value结束编辑时，刺激当前组件获得焦点，以便贸然校验
 */
function FilterSingle({ value, onChange, onAdd, onDelete }) {
  const { displayName } = value;

  // campaignInfo
  const { canAdd, mode, dataProvider, checked, isUserGroup } = useContext(
    FilterContext
  );

  // 校验器
  const [validator, setValidator] = useState({});
  const [detail, setDetail] = useState(undefined);

  // 日历最近时间
  const [lastSyncDate, setLastSyncDate] = useState(undefined);

  // const [loading, setLoading] = useState(false);
  // const [joinCount, setJoinCount] = useState(undefined);

  const getTagList = async () => {
    if (dataProvider.getLabelInfo) {
      const res = await dataProvider.getLabelInfo(value.id);
      setDetail(res);
    }
  };

  useEffect(() => {
    if (mode !== 'edit') {
      getTagList();
      return;
    }
    // 退出编辑
    setValidator(value.valid());
  }, [
    value.id,
    value.value,
    value.times,
    value.operator,
    value.dateType,
    value.checkUserTag,
    value.checkDuration,
    value.timeTerm,
    mode
  ]);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    value.changeExCalendar({});
    onChange && onChange(value);
  }, [value.id, value.dateType]);

  useEffect(() => {
    (async () => {
      if (!isUserGroup || value.dateType !== 'LATEST' || _.isEmpty(value.tagInfo) || _.isEmpty(value.exCalendar)) return setLastSyncDate(undefined);
      const _lastSyncDate = await dataProvider.lastUsableSyncDate({
        ..._.omit(value, 'tagInfo'),
        lastCalcTime: value?.tagInfo?.busiDate
      });
      setLastSyncDate(_lastSyncDate);
    })();
  }, [value.exCalendar, value.tagInfo]);

  const isFirstRender = useRef(true);

  // const getTagCount = () => {
  //   const result = value.valid();
  //   if (!result.isValid) {
  //     message.error('请填写完整的信息');
  //     return;
  //   }
  //   const valueIntegration = _.cloneDeep(value);
  //   if (valueIntegration.dateType === 'RELATIVE') {
  //     if (typeof valueIntegration.times === 'object') {
  //       const { number, timeType = 'DAY' } = valueIntegration.times;
  //       valueIntegration.times = number;
  //       valueIntegration.timeType = timeType;
  //     }
  //   } else {
  //     delete valueIntegration.timeType;
  //   }
  //   // todo 这里不存到数据里，需要change多事件 还要去修改value的方法，直接使用的useState 给界面一次性用
  //   const params = {
  //     campaignId: campaignInfo.id,
  //     scenario: campaignInfo.scenario,
  //     userTagSplit: {
  //       labelInfo: {
  //         connector: 'AND',
  //         filters: [
  //           {
  //             connector: 'AND',
  //             filters: [valueIntegration]
  //           }
  //         ]
  //       }
  //     }
  //   };
  //   let timer = null;
  //   // setLoading(true);
  //   const doRequest = async forceFlag => {
  //     try {
  //       const tagCount = await dataProvider.countUserTagCustomers({
  //         ...params,
  //         force: forceFlag
  //       });
  //       if (tagCount?.header?.code === 0) {
  //         // setJoinCount(tagCount.body?.count);
  //         // setLoading(false);
  //       } else if (tagCount?.header?.code === 210) {
  //         timer = setTimeout(async () => {
  //           await doRequest(false);
  //         }, 2000);
  //       }
  //     } catch (error) {
  //       // setLoading(false);
  //       // eslint-disable-next-line no-console
  //       console.error(error);
  //     }
  //   };
  //   doRequest(true);
  //   timer && clearTimeout(timer);
  // };

  return (
    <li
      className={`FilterSingle ${mode}`}
    >
      <div
        style={{ display: 'flex', flexDirection: 'column', gap: 8 }}
        hidden={mode !== 'edit' && !value.valid().isValid}
      >
        <div style={{ display: 'flex' }}>
          <div
            className={`FilterField ${mode} ${validator?.id && value.validating ? 'has-error' : ''
            }`}
          >
            <FilterSingleWrapper
              value={displayName}
              useTakePlaceWidth
              info={value}
            >
              <FilterField value={value} onChange={onChange} />
            </FilterSingleWrapper>
          </div>
          <div
            className={`FilterOperator ${mode} ${validator?.operator && value.validating ? 'has-error' : ''
            }`}
          >
            <FilterSingleWrapper
              value={value.getOperatorShow()}
              useTakePlaceWidth
            >
              <FilterOperator value={value} onChange={onChange} />
            </FilterSingleWrapper>
          </div>
          <div
            className={`FilterValue ${mode} ${validator?.value && value.validating ? 'has-error' : ''
            }`}
            hidden={value.isValueCanEdit() === false}
          >
            <FilterSingleWrapper value={value.getValueShow()}>
              <FilterValue value={value} onChange={onChange} />
            </FilterSingleWrapper>
          </div>
        </div>
        <div style={{ display: 'flex' }}>
          <div style={{ paddingLeft: 8 }}>
            {t('cpnt-XEHan4IFYXt8')}
            <span style={{ padding: '0 8px' }}>
              <Tooltip
                overlayStyle={{ maxWidth: '410px' }}
                overlay={
                  <div>
                    {t('cpnt-sdb44cpp4nyg')}
                    <br />
                    {t('cpnt-VgEX50jPXZ3q')}
                    <br />
                    {t('cpnt-kZMCt2bUmx2F')}
                  </div>
                }
              >
                <InfoCircleOutlined />
              </Tooltip>
            </span>
          ：
          </div>
          <div
            className={`FilterOperator ${mode} ${validator?.dateType && value.validating ? 'has-error' : ''
            }`}
          >
            <FilterSingleWrapper
              value={value.getDateTypeShow()}
              useTakePlaceWidth
            >
              <FilterDateType value={value} onChange={onChange} />
            </FilterSingleWrapper>
          </div>
          {value.dateType === 'LATEST' ? null : (
            <div
              className={`FilterValue ${mode} ${validator?.times && value.validating ? 'has-error' : ''
              }`}
            >
              <FilterSingleWrapper value={value.getTimeShow()}>
                <FilterUpdateTime value={value} onChange={onChange} />
              </FilterSingleWrapper>
            </div>
        )}
          {mode === 'edit' && value.dateType === 'LATEST' ? (
            <span>
              <Input
                value={
                  _.isNumber(lastSyncDate) ? dayjs(lastSyncDate).format('YYYY-MM-DD')
              : value?.tagInfo?.busiDate
                ? dayjs(value.tagInfo.busiDate).format('YYYY-MM-DD')
                : '-'
                }
                style={{ height: 32, width: 108, marginRight: 8 }}
                readOnly
              />
              {isUserGroup && (
              <CalendarComponent value={value} onChange={onChange} />
            )}
            </span>
        ) : null}
          {mode === 'edit' && (
          <div className="Ctroller">
            {value.validating
                && (validator?.id
                  || validator?.times
                  || validator?.operator
                  || validator?.value) && (
                  <Tooltip
                    placement="topRight"
                    title={_.head(_.values(validator.message))}
                  >
                    <div style={{ marginRight: 5, display: 'flex' }}>
                      <QuestionCircleOutlined className="Validator" />
                    </div>
                  </Tooltip>
                )}
            <PlusCircleOutlined
              className="add"
              onClick={onAdd}
              hidden={!canAdd}
            />
            <CloseCircleOutlined className="delete" onClick={onDelete} />
          </div>
        )}
          <div style={{ marginLeft: 12 }}>
            {/* todo 这里面的标签最近时间 逻辑有时间需要优化，写的太乱了  可以直接在上面判 */}
            {
            isUserGroup && mode === 'detail' && value.dateType === 'LATEST' && !_.isEmpty(value.exCalendar) && (
              <CalendarComponent value={value} mode="detail" />
            )
            }
            {mode === 'edit'
            ? value?.tagInfo?.lastCalcTime
              ? `${t('cpnt-eyqAN05wffCN')}${dayjs(value?.tagInfo?.lastCalcTime).format(
                'YYYY-MM-DD HH:mm:ss'
              )}`
              : `${t('cpnt-eyqAN05wffCN')} -`
            : `${t('cpnt-eyqAN05wffCN')}${detail && detail.lastCalcTime
              ? dayjs(detail.lastCalcTime).format('YYYY-MM-DD HH:mm:ss')
              : '-'
            }`}
          </div>
        </div>
      </div>
      {checked && <FilterRule value={value} onChange={onChange} />}
      {/* <div hidden={!_.hasIn(dataProvider, 'countUserTagCustomers')} style={{ marginLeft: '-15px', marginRight: 1 }}>
        <Button
          type="link"
          onClick={getTagCount}
          loading={!!loading}
          disabled={!value?.id}
        >
          <Tooltip title="仅计算当前标签规则的覆盖人数，不与其他节点做聚合计算">
            标签计算
          </Tooltip>
        </Button>
        <span hidden={!_.isNumber(joinCount)}>
          预计覆盖人数: <b>{joinCount} 人</b>
        </span>
      </div> */}
    </li>
  );
}

export default FilterSingle;
