import { t } from '../utils/translation';

const timeOperator = [
  'EQ',
  'NE',
  'IS_NOT_NULL',
  'IS_NULL',
  'GT',
  'GTE',
  'LT',
  'LTE',
  'BETWEEN',
  'ADVANCED_BETWEEN'
];
const userGroupTimeOperator = [
  'EQ',
  'NE',
  'IS_NOT_NULL',
  'IS_NULL',
  'GT',
  'GTE',
  'LT',
  'LTE',
  'BETWEEN',
  'ADVANCED_BETWEEN',
  'TIMESLICING',
  'MONTH_BETWEEN',
  'DAY_BETWEEN',
  'DATE_FORMAT_BETWEEN',
  'MONTH_EQ',
  'DAY_EQ',
  'DATE_FORMAT_EQ'
];
const timeType = [
  { key: 'DAY', value: t('cpnt-ZiGm4LsQZVr3') },
  { key: 'MONTH', value: t('cpnt-u0unyA1G5iQX') },
  { key: 'YEAR', value: t('cpnt-fkEIZIm360jk') }
];
export default {
  maxFilterCount: 20,
  operatorList: [
    { name: t('cpnt-YT79YwIDFduJ'), operator: 'EQ' },
    { name: t('cpnt-nU4aZ4JqVaVf'), operator: 'NE' },
    { name: t('cpnt-ngjBMGtAHDlj'), operator: 'GT' },
    { name: t('cpnt-OhiZjcbBojNm'), operator: 'GTE' },
    { name: t('cpnt-PMjzbI8StsBA'), operator: 'LT' },
    { name: t('cpnt-IQinnVAiid91'), operator: 'LTE' },
    { name: t('cpnt-kmYGh1HP127y'), operator: 'BETWEEN' },
    { name: t('cpnt-WG8jJizaMx8L'), operator: 'ADVANCED_BETWEEN' },
    { name: t('cpnt-8MpxPWje4d6K'), operator: 'IN' },
    { name: t('cpnt-zO6q3HoZlZo5'), operator: 'NOT_IN' },
    { name: t('cpnt-ey4rGfktgrjG'), operator: 'IS_NOT_NULL' },
    { name: t('cpnt-7W8CKFwgwhsm'), operator: 'IS_NULL' },
    // { name: '全部', operator: 'ALL' },
    { name: t('cpnt-Duh5GXogrmQs'), operator: 'LIKE' },
    { name: t('cpnt-ud1CW7imBSbQ'), operator: 'NOT_LIKE' },
    { name: t('cpnt-qUIQEEpW9ht7'), operator: 'START_WITH' },
    { name: t('cpnt-MVypvSjwOkmv'), operator: 'NOT_START_WITH' },
    { name: t('cpnt-jbq5yrsElHBM'), operator: 'END_WITH' },
    { name: t('cpnt-bi5aoBRw4wF7'), operator: 'NOT_END_WITH' },
    { name: t('cpnt-iAFlCRIy5y7e'), operator: 'IS_TRUE' },
    { name: t('cpnt-NopWbLBN3P4u'), operator: 'IS_FALSE' }
  ],
  userGroupOperatorList: [
    { name: t('cpnt-YT79YwIDFduJ'), operator: 'EQ' },
    { name: t('cpnt-nU4aZ4JqVaVf'), operator: 'NE' },
    { name: t('cpnt-ngjBMGtAHDlj'), operator: 'GT' },
    { name: t('cpnt-OhiZjcbBojNm'), operator: 'GTE' },
    { name: t('cpnt-PMjzbI8StsBA'), operator: 'LT' },
    { name: t('cpnt-IQinnVAiid91'), operator: 'LTE' },
    { name: t('cpnt-kmYGh1HP127y'), operator: 'BETWEEN' },
    { name: t('cpnt-WG8jJizaMx8L'), operator: 'ADVANCED_BETWEEN' },
    { name: t('cpnt-8MpxPWje4d6K'), operator: 'IN' },
    { name: t('cpnt-zO6q3HoZlZo5'), operator: 'NOT_IN' },
    { name: t('cpnt-ey4rGfktgrjG'), operator: 'IS_NOT_NULL' },
    { name: t('cpnt-7W8CKFwgwhsm'), operator: 'IS_NULL' },
    { name: t('cpnt-Duh5GXogrmQs'), operator: 'LIKE' },
    { name: t('cpnt-ud1CW7imBSbQ'), operator: 'NOT_LIKE' },
    { name: t('cpnt-qUIQEEpW9ht7'), operator: 'START_WITH' },
    { name: t('cpnt-MVypvSjwOkmv'), operator: 'NOT_START_WITH' },
    { name: t('cpnt-jbq5yrsElHBM'), operator: 'END_WITH' },
    { name: t('cpnt-bi5aoBRw4wF7'), operator: 'NOT_END_WITH' },
    { name: t('cpnt-iAFlCRIy5y7e'), operator: 'IS_TRUE' },
    { name: t('cpnt-NopWbLBN3P4u'), operator: 'IS_FALSE' },
    {
      name: t('cpnt-fIjxjem1L4mq'),
      operator: 'TIMESLICING',
      children: [
        { name: t('cpnt-q9oMtYCVEzqG'), operator: 'MONTH_BETWEEN' },
        { name: t('cpnt-mitSvdjkamZ1'), operator: 'DAY_BETWEEN' },
        { name: t('cpnt-f71g0mYiulVT'), operator: 'DATE_FORMAT_BETWEEN' },
        { name: t('cpnt-t6tuX5M5ld1u'), operator: 'MONTH_EQ' },
        { name: t('cpnt-pRPbhw0XOZ6m'), operator: 'DAY_EQ' },
        { name: t('cpnt-Y1uYp8DGzAHv'), operator: 'DATE_FORMAT_EQ' }
      ]
    }
  ],
  typeOperator: {
    INT: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'GT',
      'GTE',
      'LT',
      'LTE',
      'BETWEEN',
      'IN',
      'NOT_IN'
    ],
    LONG: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'GT',
      'GTE',
      'LT',
      'LTE',
      'BETWEEN',
      'IN',
      'NOT_IN'
    ],
    DOUBLE: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'GT',
      'GTE',
      'LT',
      'LTE',
      'BETWEEN',
      'IN',
      'NOT_IN'
    ],
    HIVE_DATE: timeOperator,
    DATE: timeOperator,
    DATETIME: timeOperator,
    HIVE_TIMESTAMP: timeOperator,
    TIMESTAMP: timeOperator,
    STRING: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'ALL',
      'LIKE',
      'NOT_LIKE',
      'START_WITH',
      'NOT_START_WITH',
      'END_WITH',
      'NOT_END_WITH',
      'IN',
      'NOT_IN'
    ],
    BOOL: ['IS_TRUE', 'IS_FALSE'],
    LABEL: ['IN', 'NOT_IN', 'ALL']
  },
  userGroupTypeOperator: {
    INT: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'GT',
      'GTE',
      'LT',
      'LTE',
      'BETWEEN',
      'IN',
      'NOT_IN'
    ],
    LONG: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'GT',
      'GTE',
      'LT',
      'LTE',
      'BETWEEN',
      'IN',
      'NOT_IN'
    ],
    DOUBLE: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'GT',
      'GTE',
      'LT',
      'LTE',
      'BETWEEN',
      'IN',
      'NOT_IN'
    ],
    HIVE_DATE: userGroupTimeOperator,
    DATE: userGroupTimeOperator,
    DATETIME: userGroupTimeOperator,
    HIVE_TIMESTAMP: userGroupTimeOperator,
    TIMESTAMP: userGroupTimeOperator,
    STRING: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'ALL',
      'LIKE',
      'NOT_LIKE',
      'START_WITH',
      'NOT_START_WITH',
      'END_WITH',
      'NOT_END_WITH',
      'IN',
      'NOT_IN'
    ],
    BOOL: ['IS_TRUE', 'IS_FALSE'],
    LABEL: ['IN', 'NOT_IN', 'ALL']
  },
  typeFormatter: {
    DATE: 'YYYY-MM-DD',
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    TIMESTAMP: 'YYYY-MM-DD HH:mm:ss',
    HIVE_DATE: 'YYYY-MM-DD',
    HIVE_TIMESTAMP: 'YYYY-MM-DD HH:mm:ss'
  },
  connector: [
    {
      name: t('cpnt-SbFnPeuR2XhO'),
      value: 'AND'
    },
    {
      name: t('cpnt-M1LLytB7EIQe'),
      value: 'OR'
    }
  ],
  validator: {
    STRING: {
      option: {
        required: true,
        maxLen: 500
      },
      message: {
        required: t('cpnt-q7H4sbrLNxhV'),
        maxLen: t('cpnt-ETeQNvitoqpB')
      }
    },
    INT: {
      option: {
        required: true,
        maxLen: 11,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: t('cpnt-q7H4sbrLNxhV'),
        maxLen: t('cpnt-0eH3UaKZw1hO'),
        regex: t('cpnt-PYBJf2GamIhl')
      }
    },
    LONG: {
      option: {
        required: true,
        maxLen: 20,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: t('cpnt-q7H4sbrLNxhV'),
        maxLen: t('cpnt-HN4doLXjWPte'),
        regex: t('cpnt-PYBJf2GamIhl')
      }
    },
    DOUBLE: {
      option: {
        required: true,
        regex: '^[+-]?\\d+(\\.\\d+)?$',
        maxLen: 20
      },
      message: {
        required: t('cpnt-q7H4sbrLNxhV'),
        maxLen: t('cpnt-HN4doLXjWPte'),
        regex: t('cpnt-hxxUmlZT63AO')
      }
    },
    DATETIME: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-ZcEqR4qZGHS5')
      }
    },
    TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-ZcEqR4qZGHS5')
      }
    },
    DATE: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-yjwljEMeuR6A')
      }
    },
    HIVE_TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-ZcEqR4qZGHS5')
      }
    },
    HIVE_DATE: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-yjwljEMeuR6A')
      }
    },
    MONTH_BETWEEN: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-yjwljEMeuR6A')
      }
    },
    DAY_BETWEEN: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-yjwljEMeuR6A')
      }
    },
    DATE_FORMAT_BETWEEN: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-yjwljEMeuR6A')
      }
    },
    MONTH_EQ: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-yjwljEMeuR6A')
      }
    },
    DAY_EQ: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-yjwljEMeuR6A')
      }
    },
    DATE_FORMAT_EQ: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-yjwljEMeuR6A')
      }
    },
    BOOL: {
      option: {
        required: false
      },
      message: {
        required: t('cpnt-q7H4sbrLNxhV')
      }
    }
  },
  DATE_TYPE_MAP: {
    LATEST: t('cpnt-6R4R0diJOaw3'),
    ABSOLUTE: t('cpnt-OeVAhIxqQgyh'),
    RELATIVE: t('cpnt-W3VyCzcOQdg2')
  },
  DATE_TYPE_NOLATEST_MAP: {
    ABSOLUTE: t('cpnt-OeVAhIxqQgyh'),
    RELATIVE: t('cpnt-W3VyCzcOQdg2')
  },
  relativeTimeObj: {
    0: t('cpnt-ozp2CfKL5ASV'),
    1: t('cpnt-Q3ZMpXjO6uUH'),
    2: t('cpnt-4xxrJfDsQOJo')
  },
  relativeTimeObjNew: {
    DAY_0: t('cpnt-ozp2CfKL5ASV'),
    DAY_1: t('cpnt-Q3ZMpXjO6uUH'),
    DAY_2: t('cpnt-4xxrJfDsQOJo'),
    MONTH_1: t('cpnt-I2NJNgzs9zPH'),
    MONTH_2: t('cpnt-BMQmD5Jh4hcK'),
    MONTH_3: t('cpnt-30T2zWoI3t8n'),
    YEAR_1: t('cpnt-dTbE8sqljTsu'),
    YEAR_2: t('cpnt-xRt9YruYMFBh'),
    YEAR_3: t('cpnt-NCdhG6M23yR7')
  },
  timeSelctor: timeType
};
