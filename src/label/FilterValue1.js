/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useContext, useEffect, useRef, Fragment } from 'react';
import { Input, AutoComplete, DatePicker, Select } from 'antd';
import _ from 'lodash';
import dayjs from 'dayjs';
import FilterContext from './FilterContext';
import useDebounce from '../utils/useDebounce';

const { Option } = AutoComplete;
const { RangePicker } = DatePicker;

const FilterValueContext = React.createContext();

/**
 * 输入一个值的情况
 */
function TextInput(props) {
  let { fieldValue, items, onChange } = props;
  const { setMenuVisible } = useContext(FilterValueContext);
  const inputEl = useRef(null);

  if (_.isArray(items)) {
    items.filter(i => typeof i === 'number').forEach((v, i) => items[i] = v.toString());
  } else {
    items = [];
  }

  const itemsDs = items.map((v, i) => (
    <Option label={v.name || v} value={v.value || v} key={i}>{v.name || v}</Option>
  ));

  return (
    <AutoComplete
      dataSource={itemsDs}
      showSearch={items.length > 0}
      onChange={onChange}
      value={fieldValue}
      // allowClear
      optionLabelProp="label"
      ref={inputEl}
      onDropdownVisibleChange={v => setMenuVisible(v)}
    />
  );
  // return 'OneInput';
}

function DateInput(props) {
  let { fieldType, onChange } = props;

  const showTime = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP') ? { format: 'HH:mm:ss' } : null;
  const format = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP') ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';

  const onValueChange = m => {
    onChange(m.valueOf());
  };

  return (
    <DatePicker
      placeholder="请输入日期"
      showTime={showTime}
      format={format}
      allowClear
      value={dayjs(props.fieldValue || new Date().getTime())}
      // getCalendarContainer={triggerNode => triggerNode.parentNode}
      onChange={onValueChange}
    />
  );
}

/**
 * 单一输入的输入框，要根据fieldType决定显示那种输入框，可能是TextInput, 也可能是DateInput
 * @param {FilterModel} value
 * @param {function} onChange
 */
function SingleInput(props) {
  const { fieldType } = props;

  if (fieldType === 'DATE' || fieldType === 'DATETIME' || fieldType === 'TIMESTAMP') {
    return DateInput(props);
  }

  return TextInput(props);
}

const longToMoment = fv => {
  return _.isArray(fv)
    ? fv.map(v => dayjs(v))
    : [new Date().getTime(), new Date().getTime()].map(v => dayjs(v));
};

/**
 * 范围日历输入框
 */
function DateBetweenInput(props) {
  let { fieldValue, fieldType, onChange } = props;
  const [value, setValue] = useState(longToMoment(fieldValue));
  const showTime = fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' ? { format: 'HH:mm:ss' } : null;
  const format = fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';

  const onValueChange = m => {
    setValue(m);
  };

  const onValueOk = m => {
    onChange([m[0].valueOf(), m[1].valueOf()]);
  };

  return (
    <RangePicker
      showTime={showTime}
      format={format}
      placeholder={['开始时间', '结束时间']}
      onOk={onValueOk}
      onChange={onValueChange}
      value={value}
    />
  );
}

function NumberBetweenInput(props) {
  let { onChange, fieldValue } = props;
  const inputEl = useRef(null);

  if (!_.isArray(fieldValue)) {
    fieldValue = ['', ''];
  }
  return (
    <>
      <Input
        style={{ width: 100, marginRight: '5px' }}
        placeholder="最小值"
        value={fieldValue[0]}
        ref={inputEl}
        onChange={e => onChange([e.target.value, fieldValue[1]])}
      />
      至
      <Input
        style={{ width: 100, marginLeft: '5px' }}
        placeholder="最大值"
        value={fieldValue[1]}
        onChange={e => onChange([fieldValue[0], e.target.value])}
      />
    </>
  );
}

function TwoInput(props) {
  const { fieldType, operator, onChange, fieldValue, items } = props;
  if (fieldType === 'DATE' || fieldType === 'DATETIME') {
    return (
      <DateBetweenInput
        fieldType={fieldType}
        operator={operator}
        fieldValue={fieldValue}
        items={items}
        onChange={onChange}
      />
    );
  }
  return (
    <NumberBetweenInput
      fieldType={fieldType}
      operator={operator}
      fieldValue={fieldValue}
      items={items}
      onChange={onChange}
    />
  );
}

/**
 * 枚举类型的输入框
 * @param {object} props
 */
function EnumInput(props) {
  let { onChange, fieldValue, items } = props;
  const inputEl = useRef(null);
  const { setMenuVisible } = useContext(FilterValueContext);

  if (!_.isArray(fieldValue)) {
    fieldValue = [];
  }

  // <Select
  //           mode="multiple"
  //           // size={size}
  //           maxTagCount={5}
  //           placeholder="请选择"
  //           // showSearch
  //           optionFilterProp="children"
  //           style={{ width: '100%' }}
  //         >
  //           {tagValues.map(n => <Option key={n.id} label={n.value} value={n.id}>{n.value}</Option>)}
  //         </Select>

  return (
    <Select
      showTime
      mode="tags"
      onChange={onChange}
      allowClear
      maxTagCount={5}
      value={fieldValue}
      ref={inputEl}
      style={{ minWidth: 150 }}
      optionLabelProp="label"
      onDropdownVisibleChange={v => setMenuVisible(v)}
    >
      {items.map(item => (
        <Option
          value={item.value}
          label={item.value}
          key={item.id}
        >
          {item.value}
        </Option>
      ))}
    </Select>
  );
}

/**
 * *****************************************
 * 主组件
 * *****************************************
 */
export default function FilterValue({ value, onChange }) {
  const { logProvider, dataProvider } = useContext(FilterContext);
  const [items, setItems] = useState([]);
  const [log] = useState(logProvider.getLogger('FilterValue'));
  const [menuVisible, setMenuVisible] = useState(false);
  const [filterValueContext] = useState({
    setMenuVisible
  });

  const { fieldType, operator, id } = value;
  const [fieldValue, setFieldValue] = useState(value.value);
  const debounceFieldValue = useDebounce(fieldValue, 500);

  useEffect(() => {
    const fetchItem = async () => {
      // setItems([]);
      // if (!isEnum) {
      //   return;
      // }
      const _items = await dataProvider.getTagValuesById(id);
      setItems(_items);
    };
    // fetchItem();
    if (menuVisible) {
      fetchItem();
    }
  }, [id, menuVisible]);

  useEffect(() => {
    setFieldValue(value.value);
  }, [value.value]);

  useEffect(() => {
    log.debug('debounceFieldValue changed call onChange', debounceFieldValue);
    if (debounceFieldValue === null || debounceFieldValue === undefined) return;
    onChange(debounceFieldValue);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceFieldValue]);

  const onChangeFieldValue = v => {
    log.debug('onChangeFieldValue', v, JSON.stringify(value));
    value.value = v;

    // 如果value是个枚举类型的属性，并且枚举是keyvalue形式的，需要向filterModel赋值一个showValue，方便用于回显
    if (value.isEnum && _.isArray(items) && items.length > 0 && items[0].name) {
      const itemValueMap = items.reduce((a, b) => {
        a[b.value] = b.name;
        return a;
      }, {});
      if (_.isArray(v)) {
        value.showValue = v.map(_v => itemValueMap[_v] || _v);
      } else {
        value.showValue = itemValueMap[v] || v;
      }
    }
    setFieldValue(v);
  };

  /**
   * 主方法
   * 返回渲染组件
   */
  const filterValue = () => {
    switch (operator) {
      case 'EQ':
      case 'NE':
      case 'GT':
      case 'GTE':
      case 'LT':
      case 'LTE':
      case 'LIKE':
      case 'NOT_LIKE':
      case 'START_WITH':
      case 'NOT_START_WITH':
      case 'END_WITH':
      case 'NOT_END_WITH':
        return (
          <SingleInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'BETWEEN':
        return (
          <TwoInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'IN':
      case 'NOT_IN':
        return (
          <EnumInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'IS_NOT_NULL':
      case 'IS_NULL':
      case 'ALL':
      case 'IS_TRUE':
      case 'IS_FALSE':
        return <span />;

      default:
        return <Input placeholder="值" disabled={!operator} />;
    }
  };

  return (
    <FilterValueContext.Provider value={filterValueContext}>
      <div>{filterValue()}</div>
    </FilterValueContext.Provider>
  );
}
