echo "请选择操作:"
echo "1) 只启动项目"
echo "2) 进入rsbuild 项目中调试"

read -p "输入选项 (1或2): " choice

if [ "$choice" == "1" ]; then
    npm run start
elif [ "$choice" == "2" ]; then
    echo "启动编译器:"
    echo "1) VSCode"
    echo "2) Cursor"
    echo "3) 不启动编辑器"

    read -p "输入选项 (1, 2或3): " editor_choice

    if [ "$editor_choice" == "1" ]; then
        code .
    elif [ "$editor_choice" == "2" ]; then
        cursor .
    fi

    npm run start-rsbuild
else
    echo "无效选项"
fi