{
  "extends": "./tsconfig.paths.json",
  "compilerOptions": {
    "target": "ESNext",
    "jsx": "react",
    "experimentalDecorators": true,
    "module": "ESNext",
    "baseUrl": "./",
    "noEmitOnError": false,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": false,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "skipLibCheck": true,
    "lib": ["esnext", "DOM"],
    "allowJs": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    // "noEmit": true,
    "noUnusedParameters": false,
    "noUnusedLocals": false,
    "sourceMap": true,
    "outDir": "./dist"
    // "checkJs": true
  },
  "exclude": ["node_modules", "src/components"],
  "include": ["src"]
}
