{"name": "wolf-static-cpnt", "version": "1.5.1", "description": "wolf component library", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": "https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/index.modern.js", "source": "src/index.ts", "engines": {"node": ">=10"}, "scripts": {"dev": "bash dev.sh", "build": "microbundle-crl --no-compress --format modern,cjs --css-modules false --jsxFragment React.Fragment", "clean": "rimraf ./dist", "prepublishOnly": "npm run clean && microbundle-crl --compress --no-sourcemap --format modern,cjs --css-modules false --jsxFragment React.Fragment", "start": "microbundle-crl watch --no-compress --format modern,cjs  --css-modules false --jsxFragment React.Fragment", "start-rsbuild": "concurrently \"npm run start\" \"pnpm -F 'wolf-static-cpnt-debug' dev\"", "test": "run-s test:unit test:lint test:build", "test:build": "run-s build", "test:lint": "eslint src", "eslint:fix": "eslint src --fix", "test:unit": "cross-env CI=1 react-scripts test --env=jsdom", "test:watch": "react-scripts test --env=jsdom", "predeploy": "cd example && yarn install && yarn run build", "deploy": "gh-pages -d example/build", "changeLog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0 "}, "peerDependencies": {"react": ">=16.10.2", "react-dom": ">=16.10.2", "dayjs": "^1.11.13"}, "resolutions": {"dayjs": "^1.11.13"}, "devDependencies": {"@types/jest": "^29.5.1", "@types/lodash": "^4.14.195", "@types/node": "^20.2.4", "@types/react": "^16.14.0", "@types/react-dom": "^18.2.4", "@types/react-redux": "^7.1.29", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^6.3.0", "@typescript-eslint/parser": "^6.3.0", "babel-eslint": "^10.0.3", "commitizen": "^4.3.0", "compare-func": "2.0.0", "conventional-changelog-cli": "^5.0.0", "cross-env": "^7.0.2", "cz-conventional-changelog": "^3.3.0", "eslint": "^6.6.0", "eslint-config-airbnb": "18.0.0", "eslint-config-prettier": "^9.0.0", "eslint-config-standard": "^14.1.0", "eslint-config-standard-react": "^9.2.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-react": "7.14.3", "eslint-plugin-react-hooks": "^3.0.0", "eslint-plugin-standard": "^4.0.1", "eslint-plugin-unused-imports": "^3.0.0", "gh-pages": "^2.2.0", "json-beautify": "^1.1.1", "microbundle-crl": "^0.13.8", "npm-run-all": "^4.1.5", "react": "^16.14.0", "react-dom": "^16.14.0", "react-json-pretty": "^2.2.0", "react-scripts": "^3.4.1", "rimraf": "^5.0.7", "sass": "1.63.4", "sass-loader": "^13.3.2", "standard-version": "^9.5.0", "typescript": "5.0.4", "concurrently": "^9.1.0", "prettier": "^3.2.5", "i18next": "^23.16.8", "react-i18next": "12.2.0"}, "files": ["dist"], "dependencies": {"@ant-design/charts": "1.4.2", "@ant-design/compatible": "^1.0.8", "@ant-design/icons": "^4.3.0", "animate.css": "^3.7.2", "antd": "4.24.9", "babel-plugin-transform-async-to-promises": "^0.8.18", "lodash": "^4.17.15", "ml-matrix": "6.10.2", "dayjs": "^1.11.13"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}