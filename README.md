## 🛠️ 启动项目

- 安装 [git](https://git-scm.com/downloads)
- 安装 [node](http://nodejs.cn)，版本 >= 14
- 安装 [pnpm](https://pnpm.io/zh)，pnpm@7
- 安装 vscode 插件 [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)、[Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)

### 运行项目

```
npm clone https://gitlab.datatist.cn/datatist/datatist-wolf-static-cpnt.git
cd Front
pnpm install
pnpm start
```

#### eslint && prettier 格式化项目 src 所有文件

```
pnpm format

```
